"""
🧪 Tests de performance pour le système de trading
Tests de charge, latence et utilisation des ressources
"""

import pytest
import asyncio
import time
import psutil
import os
from concurrent.futures import ThreadPoolExecutor
from unittest.mock import Mock, AsyncMock, patch
import sys
from pathlib import Path

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent.parent))

from bots.dex_scalping.dex_scalping_bot import DexScalpingBot, ScalpingConfig
from dashboard.dashboard_manager import DashboardManager
from dashboard.metrics_collector import MetricsCollector
from risk_management.portfolio_manager import PortfolioManager

@pytest.mark.performance
class TestPerformance:
    """Tests de performance du système"""
    
    def setup_method(self):
        """Configuration avant chaque test"""
        self.process = psutil.Process(os.getpid())
        self.initial_memory = self.process.memory_info().rss
        self.initial_cpu_time = self.process.cpu_times()
    
    def teardown_method(self):
        """Nettoyage après chaque test"""
        # Forcer le garbage collection
        import gc
        gc.collect()
    
    @pytest.mark.slow
    @pytest.mark.asyncio
    async def test_bot_startup_time(self):
        """Test du temps de démarrage des bots"""
        config = ScalpingConfig(target_pairs=['ETH/USDC'])
        bot = DexScalpingBot(config)
        
        # Mesurer le temps de démarrage
        start_time = time.time()
        
        # Mock pour éviter les connexions réseau
        with patch('bots.dex_scalping.dex_connector.DexConnector'):
            bot_task = asyncio.create_task(bot.start())
            
            # Attendre que le bot soit démarré
            while not bot.is_running:
                await asyncio.sleep(0.01)
            
            startup_time = time.time() - start_time
            
            # Le démarrage doit prendre moins de 2 secondes
            assert startup_time < 2.0, f"Démarrage trop lent: {startup_time:.2f}s"
            
            # Nettoyer
            await bot.stop()
            bot_task.cancel()
            
            try:
                await bot_task
            except asyncio.CancelledError:
                pass
    
    @pytest.mark.slow
    @pytest.mark.asyncio
    async def test_opportunity_processing_throughput(self):
        """Test du débit de traitement des opportunités"""
        config = ScalpingConfig(
            target_pairs=['ETH/USDC'],
            min_profit_usd=1.0  # Seuil bas pour traiter plus d'opportunités
        )
        bot = DexScalpingBot(config)
        
        # Mock de l'exécution rapide
        async def fast_execution(opportunity):
            await asyncio.sleep(0.001)  # 1ms de traitement
            return True
        
        bot.scalping_engine.execute_scalping_trade = fast_execution
        
        # Générer beaucoup d'opportunités
        from bots.dex_scalping.opportunity_detector import ScalpingOpportunity
        from datetime import datetime
        
        opportunities = []
        for i in range(1000):
            opp = ScalpingOpportunity(
                pair='ETH/USDC',
                buy_dex='uniswap_v2',
                sell_dex='sushiswap',
                buy_price=2000.0,
                sell_price=2000.0 + i * 0.1,
                profit_usd=5.0 + i * 0.1,
                confidence_score=0.8,
                timestamp=datetime.now()
            )
            opportunities.append(opp)
        
        # Mesurer le temps de traitement
        start_time = time.time()
        
        # Traiter toutes les opportunités
        tasks = [bot._process_opportunity(opp) for opp in opportunities]
        await asyncio.gather(*tasks)
        
        processing_time = time.time() - start_time
        throughput = len(opportunities) / processing_time
        
        # Doit traiter au moins 100 opportunités par seconde
        assert throughput > 100, f"Débit trop faible: {throughput:.1f} opp/s"
        
        print(f"Débit de traitement: {throughput:.1f} opportunités/seconde")
    
    @pytest.mark.slow
    def test_memory_usage_under_load(self):
        """Test de l'utilisation mémoire sous charge"""
        # Créer plusieurs bots
        bots = []
        for i in range(10):
            config = ScalpingConfig(target_pairs=[f'TOKEN{i}/USDC'])
            bot = DexScalpingBot(config)
            bots.append(bot)
        
        # Mesurer l'utilisation mémoire
        memory_before = self.process.memory_info().rss
        
        # Simuler de l'activité
        for bot in bots:
            # Ajouter des données de test
            bot.opportunities_detected = 100
            bot.trades_executed = 50
            bot.trade_history = [
                {'profit': 10.0, 'timestamp': time.time()} for _ in range(100)
            ]
        
        memory_after = self.process.memory_info().rss
        memory_increase = memory_after - memory_before
        
        # L'augmentation mémoire doit être raisonnable (< 100MB pour 10 bots)
        max_memory_mb = 100 * 1024 * 1024
        assert memory_increase < max_memory_mb, \
            f"Utilisation mémoire excessive: {memory_increase / 1024 / 1024:.1f}MB"
        
        print(f"Utilisation mémoire pour 10 bots: {memory_increase / 1024 / 1024:.1f}MB")
    
    @pytest.mark.slow
    @pytest.mark.asyncio
    async def test_dashboard_response_time(self):
        """Test du temps de réponse du dashboard"""
        dashboard = DashboardManager(host="127.0.0.1", port=8082)
        
        # Ajouter des bots de test
        for i in range(5):
            bot = Mock()
            bot.get_status.return_value = {
                'is_running': True,
                'total_trades': 100 + i * 10,
                'total_profit': 500.0 + i * 50
            }
            dashboard.register_bot(f"test_bot_{i}", "test", bot)
        
        # Mesurer le temps de génération du résumé
        response_times = []
        
        for _ in range(100):
            start_time = time.time()
            summary = dashboard.get_performance_summary()
            response_time = time.time() - start_time
            response_times.append(response_time)
            
            # Vérifier que le résumé est généré
            assert 'trading' in summary
            assert 'system' in summary
        
        avg_response_time = sum(response_times) / len(response_times)
        max_response_time = max(response_times)
        
        # Le temps de réponse moyen doit être < 10ms
        assert avg_response_time < 0.01, f"Temps de réponse moyen trop élevé: {avg_response_time*1000:.1f}ms"
        
        # Le temps de réponse maximum doit être < 50ms
        assert max_response_time < 0.05, f"Temps de réponse max trop élevé: {max_response_time*1000:.1f}ms"
        
        print(f"Temps de réponse moyen: {avg_response_time*1000:.1f}ms")
        print(f"Temps de réponse max: {max_response_time*1000:.1f}ms")
    
    @pytest.mark.slow
    @pytest.mark.asyncio
    async def test_concurrent_api_requests(self):
        """Test de la gestion des requêtes API concurrentes"""
        dashboard = DashboardManager(host="127.0.0.1", port=8083)
        
        # Ajouter des données de test
        for i in range(10):
            bot = Mock()
            bot.get_status.return_value = {'is_running': True, 'total_trades': i * 10}
            dashboard.register_bot(f"bot_{i}", "test", bot)
        
        # Fonction pour simuler une requête API
        async def api_request():
            start_time = time.time()
            summary = dashboard.get_performance_summary()
            response_time = time.time() - start_time
            return response_time, len(summary)
        
        # Lancer 100 requêtes concurrentes
        tasks = [api_request() for _ in range(100)]
        results = await asyncio.gather(*tasks)
        
        response_times = [r[0] for r in results]
        avg_response_time = sum(response_times) / len(response_times)
        max_response_time = max(response_times)
        
        # Vérifier que toutes les requêtes ont réussi
        assert len(results) == 100
        
        # Les temps de réponse doivent rester raisonnables même sous charge
        assert avg_response_time < 0.02, f"Temps de réponse dégradé sous charge: {avg_response_time*1000:.1f}ms"
        assert max_response_time < 0.1, f"Temps de réponse max dégradé: {max_response_time*1000:.1f}ms"
        
        print(f"Temps de réponse sous charge (100 requêtes concurrentes):")
        print(f"  Moyen: {avg_response_time*1000:.1f}ms")
        print(f"  Maximum: {max_response_time*1000:.1f}ms")
    
    @pytest.mark.slow
    def test_portfolio_manager_performance(self):
        """Test des performances du gestionnaire de portefeuille"""
        pm = PortfolioManager()
        pm.total_balance = 100000.0
        pm.available_balance = 80000.0
        
        # Test de création massive de positions
        start_time = time.time()
        
        position_ids = []
        for i in range(1000):
            position_id = pm.open_position(
                symbol=f"TOKEN{i % 10}/USDC",
                position_type="LONG",
                size=0.1,
                entry_price=100.0 + i % 100
            )
            if position_id:
                position_ids.append(position_id)
        
        creation_time = time.time() - start_time
        
        # Test de mise à jour massive des prix
        start_time = time.time()
        
        for position_id in position_ids:
            pm.update_position_price(position_id, 105.0)
        
        update_time = time.time() - start_time
        
        # Test de fermeture massive
        start_time = time.time()
        
        for position_id in position_ids:
            pm.close_position(position_id, 110.0)
        
        close_time = time.time() - start_time
        
        # Vérifier les performances
        creation_rate = len(position_ids) / creation_time
        update_rate = len(position_ids) / update_time
        close_rate = len(position_ids) / close_time
        
        # Doit traiter au moins 1000 opérations par seconde
        assert creation_rate > 1000, f"Création trop lente: {creation_rate:.1f} pos/s"
        assert update_rate > 1000, f"Mise à jour trop lente: {update_rate:.1f} pos/s"
        assert close_rate > 1000, f"Fermeture trop lente: {close_rate:.1f} pos/s"
        
        print(f"Performances Portfolio Manager:")
        print(f"  Création: {creation_rate:.1f} positions/seconde")
        print(f"  Mise à jour: {update_rate:.1f} positions/seconde")
        print(f"  Fermeture: {close_rate:.1f} positions/seconde")
    
    @pytest.mark.slow
    def test_metrics_collection_performance(self):
        """Test des performances de collecte de métriques"""
        collector = MetricsCollector()
        
        # Enregistrer beaucoup de bots
        bots = []
        for i in range(50):
            bot = Mock()
            bot.get_status.return_value = {
                'is_running': True,
                'start_time': time.time(),
                'bot_type': 'test'
            }
            bot.get_performance_stats.return_value = {
                'total_executions': i * 10,
                'successful_executions': i * 8,
                'net_profit': i * 100.0,
                'total_volume': i * 1000.0
            }
            
            collector.register_bot(f"bot_{i}", "test", bot)
            bots.append(bot)
        
        # Mesurer le temps de collecte
        start_time = time.time()
        
        # Simuler plusieurs cycles de collecte
        for _ in range(10):
            asyncio.run(collector._collect_bot_metrics())
            asyncio.run(collector._collect_system_metrics())
        
        collection_time = time.time() - start_time
        avg_cycle_time = collection_time / 10
        
        # Chaque cycle doit prendre moins de 100ms pour 50 bots
        assert avg_cycle_time < 0.1, f"Collecte trop lente: {avg_cycle_time*1000:.1f}ms/cycle"
        
        print(f"Temps de collecte pour 50 bots: {avg_cycle_time*1000:.1f}ms par cycle")
    
    @pytest.mark.slow
    @pytest.mark.asyncio
    async def test_websocket_broadcast_performance(self):
        """Test des performances de diffusion WebSocket"""
        from dashboard.web_server import DashboardWebServer
        from dashboard.metrics_collector import MetricsCollector
        
        collector = MetricsCollector()
        server = DashboardWebServer(collector, host="127.0.0.1", port=8084)
        
        # Simuler des connexions WebSocket
        mock_websockets = []
        for i in range(100):
            ws = AsyncMock()
            ws.send_json = AsyncMock()
            mock_websockets.append(ws)
        
        server.websocket_connections = mock_websockets
        
        # Préparer un gros message
        large_data = {
            'timestamp': time.time(),
            'bots': {f'bot_{i}': {'status': 'running', 'data': list(range(100))} for i in range(50)},
            'metrics': list(range(1000))
        }
        
        # Mesurer le temps de diffusion
        start_time = time.time()
        
        await server.broadcast_update(large_data)
        
        broadcast_time = time.time() - start_time
        
        # La diffusion doit prendre moins de 100ms pour 100 connexions
        assert broadcast_time < 0.1, f"Diffusion trop lente: {broadcast_time*1000:.1f}ms"
        
        # Vérifier que tous les WebSockets ont reçu le message
        for ws in mock_websockets:
            ws.send_json.assert_called_once()
        
        print(f"Temps de diffusion WebSocket (100 connexions): {broadcast_time*1000:.1f}ms")
    
    @pytest.mark.slow
    def test_cpu_usage_under_load(self):
        """Test de l'utilisation CPU sous charge"""
        # Mesurer l'utilisation CPU initiale
        cpu_before = self.process.cpu_percent()
        
        # Créer une charge de travail intensive
        def cpu_intensive_task():
            # Simuler du calcul intensif
            total = 0
            for i in range(1000000):
                total += i ** 2
            return total
        
        # Lancer plusieurs tâches en parallèle
        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = [executor.submit(cpu_intensive_task) for _ in range(10)]
            
            # Attendre que toutes les tâches se terminent
            results = [future.result() for future in futures]
        
        # Mesurer l'utilisation CPU après
        time.sleep(1)  # Laisser le temps au CPU de se stabiliser
        cpu_after = self.process.cpu_percent()
        
        # Vérifier que toutes les tâches ont réussi
        assert len(results) == 10
        assert all(r > 0 for r in results)
        
        print(f"Utilisation CPU: {cpu_before:.1f}% -> {cpu_after:.1f}%")
    
    @pytest.mark.slow
    @pytest.mark.asyncio
    async def test_memory_leak_detection(self):
        """Test de détection de fuites mémoire"""
        initial_memory = self.process.memory_info().rss
        
        # Créer et détruire des objets en boucle
        for cycle in range(10):
            # Créer des bots temporaires
            bots = []
            for i in range(10):
                config = ScalpingConfig(target_pairs=[f'TOKEN{i}/USDC'])
                bot = DexScalpingBot(config)
                
                # Ajouter des données
                bot.trade_history = [{'profit': j} for j in range(100)]
                bots.append(bot)
            
            # Simuler de l'activité
            for bot in bots:
                bot.opportunities_detected = 50
                bot.trades_executed = 25
            
            # Détruire les bots
            del bots
            
            # Forcer le garbage collection
            import gc
            gc.collect()
            
            # Mesurer la mémoire tous les 3 cycles
            if cycle % 3 == 0:
                current_memory = self.process.memory_info().rss
                memory_increase = current_memory - initial_memory
                
                print(f"Cycle {cycle}: Mémoire = {memory_increase / 1024 / 1024:.1f}MB")
                
                # La mémoire ne doit pas augmenter de plus de 50MB
                assert memory_increase < 50 * 1024 * 1024, \
                    f"Fuite mémoire détectée au cycle {cycle}: {memory_increase / 1024 / 1024:.1f}MB"
        
        final_memory = self.process.memory_info().rss
        total_increase = final_memory - initial_memory
        
        print(f"Augmentation mémoire totale: {total_increase / 1024 / 1024:.1f}MB")
        
        # L'augmentation totale doit être minime
        assert total_increase < 20 * 1024 * 1024, \
            f"Fuite mémoire significative: {total_increase / 1024 / 1024:.1f}MB"

    @pytest.mark.slow
    def test_large_dataset_processing(self):
        """Test de traitement de gros volumes de données"""
        from dashboard.metrics_collector import MetricsCollector, MetricPoint
        from datetime import datetime, timedelta

        collector = MetricsCollector()

        # Générer un gros dataset de métriques
        start_time = time.time()

        # Ajouter 10000 points de données
        base_time = datetime.now()
        for i in range(10000):
            timestamp = base_time - timedelta(minutes=i)
            collector._add_metric_point(f"test_metric_{i % 100}", i * 1.5)

        generation_time = time.time() - start_time

        # Test de récupération
        start_time = time.time()

        # Récupérer l'historique de plusieurs métriques
        for i in range(100):
            history = collector.get_metric_history(f"test_metric_{i}", hours=24)
            assert len(history) > 0

        retrieval_time = time.time() - start_time

        # Vérifier les performances
        generation_rate = 10000 / generation_time
        retrieval_rate = 100 / retrieval_time

        assert generation_rate > 5000, f"Génération trop lente: {generation_rate:.1f} points/s"
        assert retrieval_rate > 50, f"Récupération trop lente: {retrieval_rate:.1f} requêtes/s"

        print(f"Traitement gros dataset:")
        print(f"  Génération: {generation_rate:.1f} points/seconde")
        print(f"  Récupération: {retrieval_rate:.1f} requêtes/seconde")
