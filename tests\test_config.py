#!/usr/bin/env python3
"""
🧪 Tests de configuration pour botCrypto
Tests unitaires pour la gestion de configuration
"""

import sys
import unittest
import os
from pathlib import Path

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from config.settings import BotConfig

class TestBotConfig(unittest.TestCase):
    """Tests pour la configuration du bot"""
    
    def setUp(self):
        """Configuration des tests"""
        # Sauvegarder les variables d'environnement existantes
        self.original_env = dict(os.environ)
        
        # Définir des variables de test
        os.environ.update({
            "PRIVATE_KEY": "test_private_key",
            "WALLET_ADDRESS": "******************************************",
            "BSC_SCAN_API_KEY": "test_bsc_key",
            "TELEGRAM_BOT_TOKEN": "test_telegram_token",
            "TELEGRAM_CHAT_ID": "test_chat_id",
            "safe_bot_TEST_API_KEY": "test_api_key",
            "safe_bot_TEST_API_SECRET": "test_api_secret",
            "safe_bot_PROD_API_KEY": "prod_api_key",
            "safe_bot_PROD_API_SECRET": "prod_api_secret"
        })
    
    def tearDown(self):
        """Nettoyage après les tests"""
        # Restaurer les variables d'environnement
        os.environ.clear()
        os.environ.update(self.original_env)
    
    def test_config_properties(self):
        """Test des propriétés de configuration"""
        config = BotConfig()
        
        # Test des propriétés crypto
        self.assertEqual(config.private_key, "test_private_key")
        self.assertEqual(config.wallet_address, "******************************************")
        self.assertEqual(config.bsc_scan_api_key, "test_bsc_key")
        
        # Test des propriétés Telegram
        self.assertEqual(config.telegram_token, "test_telegram_token")
        self.assertEqual(config.telegram_chat_id, "test_chat_id")
        
        # Test des propriétés Binance
        self.assertEqual(config.binance_test_api_key, "test_api_key")
        self.assertEqual(config.binance_test_api_secret, "test_api_secret")
        self.assertEqual(config.binance_prod_api_key, "prod_api_key")
        self.assertEqual(config.binance_prod_api_secret, "prod_api_secret")
    
    def test_trading_config_test(self):
        """Test de la configuration de trading pour l'environnement test"""
        config = BotConfig()
        trading_config = config.get_trading_config("test")
        
        # Vérifier les champs obligatoires
        required_fields = [
            "pair", "grid_size", "grid_spacing", "order_size", 
            "capital_max", "stop_loss_percent", "take_profit_percent",
            "base_url", "api_key", "api_secret"
        ]
        
        for field in required_fields:
            self.assertIn(field, trading_config)
        
        # Vérifier les valeurs spécifiques au test
        self.assertEqual(trading_config["base_url"], "https://testnet.binance.vision")
        self.assertEqual(trading_config["api_key"], "test_api_key")
        self.assertEqual(trading_config["api_secret"], "test_api_secret")
    
    def test_trading_config_prod(self):
        """Test de la configuration de trading pour l'environnement prod"""
        config = BotConfig()
        trading_config = config.get_trading_config("prod")
        
        # Vérifier les valeurs spécifiques à la prod
        self.assertEqual(trading_config["api_key"], "prod_api_key")
        self.assertEqual(trading_config["api_secret"], "prod_api_secret")
        self.assertNotEqual(trading_config["base_url"], "https://testnet.binance.vision")
    
    def test_sniper_config(self):
        """Test de la configuration du sniper bot"""
        config = BotConfig()
        sniper_config = config.get_sniper_config()
        
        # Vérifier les champs obligatoires
        required_fields = [
            "invest_amount", "slippage", "dex_screener_api",
            "private_key", "wallet_address", "bsc_scan_api_key"
        ]
        
        for field in required_fields:
            self.assertIn(field, sniper_config)
        
        # Vérifier les types
        self.assertIsInstance(sniper_config["invest_amount"], (int, float))
        self.assertIsInstance(sniper_config["slippage"], (int, float))
        self.assertIsInstance(sniper_config["dex_screener_api"], str)
    
    def test_config_validation(self):
        """Test de la validation de configuration"""
        config = BotConfig()

        # Avec toutes les variables définies, la config devrait être valide
        self.assertTrue(config.is_valid())

        # Créer une config avec un fichier inexistant pour tester la validation
        config_invalid = BotConfig("fichier_inexistant.env")
        # Note: Le test de validation complète nécessiterait de mocker les variables d'environnement
        # Pour l'instant, on teste juste que la méthode is_valid() fonctionne
        self.assertIsInstance(config_invalid.is_valid(), bool)

if __name__ == "__main__":
    unittest.main()
