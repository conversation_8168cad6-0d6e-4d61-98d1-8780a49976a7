"""
🛡️ Gestionnaire d'erreurs avancé pour botCrypto
Gestion robuste des erreurs avec retry automatique et fallback strategies
"""

import time
import logging
import functools
import traceback
from typing import Dict, List, Optional, Callable, Any, Union
from datetime import datetime, timedelta
from enum import Enum
import requests
from pathlib import Path
import sys

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from utils.notifications import notifier

class ErrorSeverity(Enum):
    """Niveaux de sévérité des erreurs"""
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"

class ErrorCategory(Enum):
    """Catégories d'erreurs"""
    NETWORK = "NETWORK"
    API = "API"
    TRADING = "TRADING"
    DATA = "DATA"
    SYSTEM = "SYSTEM"
    VALIDATION = "VALIDATION"

class RetryStrategy:
    """Configuration de stratégie de retry"""
    
    def __init__(self, max_attempts: int = 3, base_delay: float = 1.0, 
                 max_delay: float = 60.0, exponential_backoff: bool = True,
                 jitter: bool = True):
        self.max_attempts = max_attempts
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.exponential_backoff = exponential_backoff
        self.jitter = jitter
    
    def get_delay(self, attempt: int) -> float:
        """Calcule le délai pour une tentative donnée"""
        if self.exponential_backoff:
            delay = self.base_delay * (2 ** (attempt - 1))
        else:
            delay = self.base_delay
        
        # Limiter le délai maximum
        delay = min(delay, self.max_delay)
        
        # Ajouter du jitter pour éviter les thundering herds
        if self.jitter:
            import random
            delay *= (0.5 + random.random() * 0.5)
        
        return delay

class ErrorHandler:
    """Gestionnaire d'erreurs centralisé"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.error_counts = {}  # Compteurs d'erreurs par type
        self.last_errors = {}   # Dernières erreurs par catégorie
        self.circuit_breakers = {}  # Circuit breakers par service
        self.fallback_strategies = {}  # Stratégies de fallback
        
        # Configuration par défaut des retry strategies
        self.default_strategies = {
            ErrorCategory.NETWORK: RetryStrategy(max_attempts=5, base_delay=2.0),
            ErrorCategory.API: RetryStrategy(max_attempts=3, base_delay=1.0),
            ErrorCategory.TRADING: RetryStrategy(max_attempts=2, base_delay=0.5),
            ErrorCategory.DATA: RetryStrategy(max_attempts=4, base_delay=1.5),
            ErrorCategory.SYSTEM: RetryStrategy(max_attempts=1, base_delay=0.0),
            ErrorCategory.VALIDATION: RetryStrategy(max_attempts=1, base_delay=0.0)
        }
        
        # Seuils pour les circuit breakers
        self.circuit_breaker_thresholds = {
            ErrorCategory.NETWORK: 10,  # 10 erreurs réseau consécutives
            ErrorCategory.API: 5,       # 5 erreurs API consécutives
            ErrorCategory.TRADING: 3,   # 3 erreurs de trading consécutives
        }
        
        self.logger.info("🛡️ Gestionnaire d'erreurs initialisé")
    
    def handle_error(self, error: Exception, category: ErrorCategory, 
                    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                    context: Dict = None, notify: bool = True) -> Dict[str, Any]:
        """
        Gère une erreur avec logging, notification et comptage
        
        Args:
            error: Exception à gérer
            category: Catégorie de l'erreur
            severity: Sévérité de l'erreur
            context: Contexte additionnel
            notify: Envoyer une notification
            
        Returns:
            Dict avec les informations de l'erreur
        """
        error_info = {
            'timestamp': datetime.now(),
            'error_type': type(error).__name__,
            'error_message': str(error),
            'category': category.value,
            'severity': severity.value,
            'context': context or {},
            'traceback': traceback.format_exc()
        }
        
        # Incrémenter le compteur d'erreurs
        error_key = f"{category.value}_{type(error).__name__}"
        self.error_counts[error_key] = self.error_counts.get(error_key, 0) + 1
        
        # Enregistrer la dernière erreur pour cette catégorie
        self.last_errors[category] = error_info
        
        # Logging selon la sévérité
        log_message = f"[{category.value}] {type(error).__name__}: {str(error)}"
        if context:
            log_message += f" | Context: {context}"
        
        if severity == ErrorSeverity.CRITICAL:
            self.logger.critical(log_message)
        elif severity == ErrorSeverity.HIGH:
            self.logger.error(log_message)
        elif severity == ErrorSeverity.MEDIUM:
            self.logger.warning(log_message)
        else:
            self.logger.info(log_message)
        
        # Vérifier les circuit breakers
        self._check_circuit_breaker(category, error_info)
        
        # Notification si demandée et sévérité suffisante
        if notify and severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]:
            self._send_error_notification(error_info)
        
        return error_info
    
    def _check_circuit_breaker(self, category: ErrorCategory, error_info: Dict):
        """Vérifie et active les circuit breakers si nécessaire"""
        if category not in self.circuit_breaker_thresholds:
            return
        
        threshold = self.circuit_breaker_thresholds[category]
        error_key = f"{category.value}_{error_info['error_type']}"
        error_count = self.error_counts.get(error_key, 0)
        
        if error_count >= threshold:
            # Activer le circuit breaker
            self.circuit_breakers[category] = {
                'activated_at': datetime.now(),
                'error_count': error_count,
                'last_error': error_info
            }
            
            self.logger.critical(f"🚨 Circuit breaker activé pour {category.value} "
                               f"({error_count} erreurs)")
            
            # Notification critique
            notifier.send_telegram(
                f"🚨 <b>CIRCUIT BREAKER ACTIVÉ</b>\n"
                f"📂 Catégorie: {category.value}\n"
                f"🔢 Erreurs: {error_count}\n"
                f"⚠️ Service temporairement désactivé"
            )
    
    def _send_error_notification(self, error_info: Dict):
        """Envoie une notification d'erreur"""
        severity_emoji = {
            'LOW': '💡',
            'MEDIUM': '⚠️',
            'HIGH': '❌',
            'CRITICAL': '🚨'
        }
        
        emoji = severity_emoji.get(error_info['severity'], '❓')
        
        message = (
            f"{emoji} <b>Erreur {error_info['severity']}</b>\n"
            f"📂 Catégorie: {error_info['category']}\n"
            f"🔧 Type: {error_info['error_type']}\n"
            f"💬 Message: {error_info['error_message'][:100]}...\n"
            f"🕒 Heure: {error_info['timestamp'].strftime('%H:%M:%S')}"
        )
        
        if error_info['context']:
            context_str = str(error_info['context'])[:50]
            message += f"\n📋 Contexte: {context_str}..."
        
        notifier.send_telegram(message)
    
    def is_circuit_breaker_active(self, category: ErrorCategory) -> bool:
        """Vérifie si un circuit breaker est actif"""
        if category not in self.circuit_breakers:
            return False
        
        breaker = self.circuit_breakers[category]
        
        # Circuit breaker expire après 5 minutes
        if datetime.now() - breaker['activated_at'] > timedelta(minutes=5):
            del self.circuit_breakers[category]
            self.logger.info(f"🔄 Circuit breaker désactivé pour {category.value}")
            return False
        
        return True
    
    def reset_circuit_breaker(self, category: ErrorCategory):
        """Remet à zéro un circuit breaker"""
        if category in self.circuit_breakers:
            del self.circuit_breakers[category]
            self.logger.info(f"🔄 Circuit breaker réinitialisé pour {category.value}")
        
        # Réinitialiser les compteurs d'erreurs pour cette catégorie
        keys_to_reset = [k for k in self.error_counts.keys() if k.startswith(category.value)]
        for key in keys_to_reset:
            self.error_counts[key] = 0
    
    def get_error_stats(self) -> Dict[str, Any]:
        """Récupère les statistiques d'erreurs"""
        return {
            'error_counts': self.error_counts.copy(),
            'last_errors': {k.value: v for k, v in self.last_errors.items()},
            'active_circuit_breakers': {k.value: v for k, v in self.circuit_breakers.items()},
            'total_errors': sum(self.error_counts.values())
        }
    
    def register_fallback_strategy(self, category: ErrorCategory, 
                                 fallback_func: Callable, priority: int = 1):
        """Enregistre une stratégie de fallback"""
        if category not in self.fallback_strategies:
            self.fallback_strategies[category] = []
        
        self.fallback_strategies[category].append({
            'function': fallback_func,
            'priority': priority
        })
        
        # Trier par priorité (plus petit = plus prioritaire)
        self.fallback_strategies[category].sort(key=lambda x: x['priority'])
        
        self.logger.info(f"📋 Stratégie de fallback enregistrée pour {category.value}")
    
    def execute_fallback(self, category: ErrorCategory, *args, **kwargs) -> Any:
        """Exécute les stratégies de fallback pour une catégorie"""
        if category not in self.fallback_strategies:
            self.logger.warning(f"⚠️ Aucune stratégie de fallback pour {category.value}")
            return None
        
        for strategy in self.fallback_strategies[category]:
            try:
                self.logger.info(f"🔄 Tentative de fallback pour {category.value}")
                result = strategy['function'](*args, **kwargs)
                self.logger.info(f"✅ Fallback réussi pour {category.value}")
                return result
            except Exception as e:
                self.logger.warning(f"❌ Échec fallback pour {category.value}: {e}")
                continue
        
        self.logger.error(f"💥 Tous les fallbacks ont échoué pour {category.value}")
        return None

def with_retry(category: ErrorCategory = ErrorCategory.SYSTEM,
               severity: ErrorSeverity = ErrorSeverity.MEDIUM,
               retry_strategy: Optional[RetryStrategy] = None,
               fallback_on_failure: bool = False):
    """
    Décorateur pour ajouter la gestion d'erreurs avec retry automatique
    
    Args:
        category: Catégorie de l'erreur
        severity: Sévérité par défaut
        retry_strategy: Stratégie de retry personnalisée
        fallback_on_failure: Utiliser les fallbacks en cas d'échec
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Utiliser la stratégie par défaut si non spécifiée
            strategy = retry_strategy or error_handler.default_strategies.get(
                category, RetryStrategy()
            )
            
            last_exception = None
            
            for attempt in range(1, strategy.max_attempts + 1):
                try:
                    # Vérifier le circuit breaker
                    if error_handler.is_circuit_breaker_active(category):
                        raise RuntimeError(f"Circuit breaker actif pour {category.value}")
                    
                    # Exécuter la fonction
                    return func(*args, **kwargs)
                    
                except Exception as e:
                    last_exception = e
                    
                    # Gérer l'erreur
                    context = {
                        'function': func.__name__,
                        'attempt': attempt,
                        'max_attempts': strategy.max_attempts,
                        'args_count': len(args),
                        'kwargs_keys': list(kwargs.keys())
                    }
                    
                    error_handler.handle_error(
                        e, category, severity, context, 
                        notify=(attempt == strategy.max_attempts)  # Notifier seulement au dernier échec
                    )
                    
                    # Si c'est la dernière tentative, ne pas attendre
                    if attempt == strategy.max_attempts:
                        break
                    
                    # Attendre avant la prochaine tentative
                    delay = strategy.get_delay(attempt)
                    if delay > 0:
                        time.sleep(delay)
            
            # Toutes les tentatives ont échoué
            if fallback_on_failure:
                fallback_result = error_handler.execute_fallback(category, *args, **kwargs)
                if fallback_result is not None:
                    return fallback_result
            
            # Relancer la dernière exception
            raise last_exception
        
        return wrapper
    return decorator

def safe_execute(func: Callable, *args, default_return=None, 
                category: ErrorCategory = ErrorCategory.SYSTEM,
                severity: ErrorSeverity = ErrorSeverity.LOW, **kwargs) -> Any:
    """
    Exécute une fonction de manière sécurisée
    
    Args:
        func: Fonction à exécuter
        default_return: Valeur par défaut en cas d'erreur
        category: Catégorie de l'erreur
        severity: Sévérité de l'erreur
        
    Returns:
        Résultat de la fonction ou valeur par défaut
    """
    try:
        return func(*args, **kwargs)
    except Exception as e:
        error_handler.handle_error(e, category, severity, {
            'function': func.__name__ if hasattr(func, '__name__') else str(func),
            'default_return': default_return
        }, notify=False)
        return default_return

# Instance globale
error_handler = ErrorHandler()
