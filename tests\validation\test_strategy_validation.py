"""
🧪 Tests de validation des stratégies
Tests complets du framework de validation des stratégies de trading
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
import sys
from pathlib import Path

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent.parent))

from tests.validation.strategy_validator import StrategyValidator, ValidationResult

@pytest.mark.unit
class TestStrategyValidation:
    """Tests du validateur de stratégies"""
    
    def setup_method(self):
        """Configuration avant chaque test"""
        self.validator = StrategyValidator()
    
    def create_sample_trades(self, num_trades: int = 100, win_rate: float = 0.6,
                           avg_profit: float = 100.0, avg_loss: float = -50.0) -> list:
        """Crée des trades d'exemple"""
        trades = []
        base_time = datetime.now() - timedelta(days=num_trades)
        
        for i in range(num_trades):
            is_winning = i < (num_trades * win_rate)
            pnl = avg_profit if is_winning else avg_loss
            
            # Ajouter de la variation
            pnl *= (0.8 + (i % 5) * 0.1)  # Variation de ±20%
            
            trade = {
                'id': f'trade_{i}',
                'symbol': f'TOKEN{i % 5}/USDC',  # 5 symboles différents
                'pnl': pnl,
                'size': 1.0,
                'price': 100.0 + (i % 20),
                'timestamp': (base_time + timedelta(days=i)).isoformat(),
                'capital': 10000.0
            }
            trades.append(trade)
        
        return trades
    
    def create_sample_strategy_data(self, **kwargs) -> dict:
        """Crée des données de stratégie d'exemple"""
        defaults = {
            'num_trades': 100,
            'win_rate': 0.6,
            'avg_profit': 100.0,
            'avg_loss': -50.0
        }
        defaults.update(kwargs)
        
        trades = self.create_sample_trades(**defaults)
        
        return {
            'trades': trades,
            'prices': [{'timestamp': t['timestamp'], 'price': t['price']} for t in trades],
            'positions': [{'size': t['size'], 'price': t['price']} for t in trades[:10]],
            'strategy_name': 'Test Strategy',
            'start_date': trades[0]['timestamp'],
            'end_date': trades[-1]['timestamp']
        }
    
    @pytest.mark.asyncio
    async def test_successful_strategy_validation(self):
        """Test de validation d'une stratégie réussie"""
        # Créer une stratégie performante
        strategy_data = self.create_sample_strategy_data(
            num_trades=150,
            win_rate=0.75,
            avg_profit=150.0,
            avg_loss=-30.0
        )
        
        # Valider la stratégie
        report = await self.validator.validate_strategy("Excellent Strategy", strategy_data)
        
        # Vérifications
        assert report.strategy_name == "Excellent Strategy"
        assert report.total_tests > 0
        assert report.passed_tests > report.failed_tests
        assert report.overall_score > 70.0
        assert "APPROUVÉ" in report.recommendation
        
        # Vérifier les métriques de performance
        performance_metrics = {m.name: m for m in report.performance_metrics}
        assert 'total_return' in performance_metrics
        assert 'win_rate' in performance_metrics
        assert 'sharpe_ratio' in performance_metrics
        
        # Le win rate doit être correct
        win_rate_metric = performance_metrics['win_rate']
        assert abs(win_rate_metric.value - 75.0) < 1.0  # Tolérance de 1%
        assert win_rate_metric.result == ValidationResult.PASS
    
    @pytest.mark.asyncio
    async def test_failing_strategy_validation(self):
        """Test de validation d'une stratégie défaillante"""
        # Créer une stratégie peu performante
        strategy_data = self.create_sample_strategy_data(
            num_trades=50,  # Peu de trades
            win_rate=0.3,   # Mauvais win rate
            avg_profit=20.0,  # Petits profits
            avg_loss=-100.0   # Grosses pertes
        )
        
        # Valider la stratégie
        report = await self.validator.validate_strategy("Poor Strategy", strategy_data)
        
        # Vérifications
        assert report.failed_tests > 0
        assert report.overall_score < 50.0
        assert "REJETÉ" in report.recommendation or "RÉVISION" in report.recommendation
        assert len(report.critical_issues) > 0
        
        # Vérifier que les métriques échouent
        performance_metrics = {m.name: m for m in report.performance_metrics}
        win_rate_metric = performance_metrics['win_rate']
        assert win_rate_metric.result == ValidationResult.FAIL
    
    @pytest.mark.asyncio
    async def test_risk_metrics_validation(self):
        """Test de validation des métriques de risque"""
        # Créer une stratégie avec des risques élevés
        trades = []
        base_time = datetime.now() - timedelta(days=100)
        
        # Ajouter des trades avec forte volatilité
        for i in range(100):
            # Alternance entre gros gains et grosses pertes
            pnl = 500.0 if i % 2 == 0 else -400.0
            
            trade = {
                'id': f'trade_{i}',
                'symbol': 'VOLATILE/USDC',
                'pnl': pnl,
                'size': 10.0,  # Grosses positions
                'price': 100.0,
                'timestamp': (base_time + timedelta(days=i)).isoformat(),
                'capital': 10000.0
            }
            trades.append(trade)
        
        strategy_data = {
            'trades': trades,
            'prices': [],
            'positions': [{'size': 10.0, 'price': 100.0} for _ in range(10)]
        }
        
        # Valider
        report = await self.validator.validate_strategy("Risky Strategy", strategy_data)
        
        # Vérifier les métriques de risque
        risk_metrics = {m.name: m for m in report.risk_metrics}
        
        # La volatilité doit être élevée
        if 'volatility' in risk_metrics:
            assert risk_metrics['volatility'].value > 20.0
        
        # Les pertes consécutives doivent être détectées
        if 'max_consecutive_losses' in risk_metrics:
            consecutive_losses = risk_metrics['max_consecutive_losses']
            assert consecutive_losses.value > 0
    
    @pytest.mark.asyncio
    async def test_robustness_metrics_validation(self):
        """Test de validation des métriques de robustesse"""
        # Créer une stratégie avec peu de données
        strategy_data = self.create_sample_strategy_data(
            num_trades=20,  # Très peu de trades
            win_rate=0.8    # Bon win rate mais peu de données
        )
        
        # Valider
        report = await self.validator.validate_strategy("Limited Data Strategy", strategy_data)
        
        # Vérifier les métriques de robustesse
        robustness_metrics = {m.name: m for m in report.robustness_metrics}
        
        # Le nombre de trades doit échouer
        num_trades_metric = robustness_metrics['num_trades']
        assert num_trades_metric.result == ValidationResult.FAIL
        assert num_trades_metric.value == 20
        
        # La période de test doit être courte
        if 'test_period_days' in robustness_metrics:
            period_metric = robustness_metrics['test_period_days']
            assert period_metric.value < 90  # Moins de 90 jours
    
    @pytest.mark.asyncio
    async def test_custom_thresholds(self):
        """Test avec des seuils personnalisés"""
        strategy_data = self.create_sample_strategy_data(win_rate=0.55)
        
        # Seuils personnalisés plus stricts
        custom_thresholds = {
            'min_win_rate': 60.0,  # Plus strict que 50% par défaut
            'min_sharpe_ratio': 2.0,  # Plus strict que 1.0 par défaut
            'max_drawdown': 10.0   # Plus strict que 20% par défaut
        }
        
        # Valider avec seuils personnalisés
        report = await self.validator.validate_strategy(
            "Custom Thresholds Strategy", 
            strategy_data, 
            custom_thresholds
        )
        
        # Vérifier que les seuils personnalisés sont utilisés
        performance_metrics = {m.name: m for m in report.performance_metrics}
        win_rate_metric = performance_metrics['win_rate']
        
        assert win_rate_metric.threshold == 60.0  # Seuil personnalisé
        assert win_rate_metric.result == ValidationResult.FAIL  # 55% < 60%
    
    @pytest.mark.asyncio
    async def test_stress_tests(self):
        """Test des tests de stress"""
        strategy_data = self.create_sample_strategy_data()
        
        # Valider avec tests de stress
        report = await self.validator.validate_strategy("Stress Test Strategy", strategy_data)
        
        # Vérifier que les tests de stress ont été exécutés
        stress_results = report.stress_test_results
        
        assert 'bear_market_pnl' in stress_results
        assert 'high_volatility_pnl' in stress_results
        assert 'low_liquidity_impact' in stress_results
        assert 'high_fees_impact' in stress_results
        
        # Les résultats doivent être numériques
        for key, value in stress_results.items():
            assert isinstance(value, (int, float))
    
    @pytest.mark.asyncio
    async def test_empty_data_handling(self):
        """Test de gestion des données vides"""
        # Données vides
        empty_strategy_data = {
            'trades': [],
            'prices': [],
            'positions': []
        }
        
        # Valider
        report = await self.validator.validate_strategy("Empty Strategy", empty_strategy_data)
        
        # Vérifier que l'absence de données est détectée
        assert report.failed_tests > 0
        assert report.overall_score == 0.0
        assert "REJETÉ" in report.recommendation
        
        # Vérifier qu'il y a une métrique pour les trades manquants
        performance_metrics = {m.name: m for m in report.performance_metrics}
        assert 'trades_available' in performance_metrics
        assert performance_metrics['trades_available'].result == ValidationResult.FAIL
    
    def test_metric_creation(self):
        """Test de création de métriques"""
        # Test métrique qui passe
        metric_pass = self.validator._create_metric(
            "test_metric", 75.0, 50.0, "gte", "Test message"
        )
        assert metric_pass.result == ValidationResult.PASS
        assert metric_pass.value == 75.0
        assert metric_pass.threshold == 50.0
        
        # Test métrique qui échoue
        metric_fail = self.validator._create_metric(
            "test_metric", 25.0, 50.0, "gte", "Test message"
        )
        assert metric_fail.result == ValidationResult.FAIL
        
        # Test différents types de comparaison
        metric_lte = self.validator._create_metric(
            "test_metric", 10.0, 20.0, "lte", "Test message"
        )
        assert metric_lte.result == ValidationResult.PASS
        
        metric_eq = self.validator._create_metric(
            "test_metric", 50.0, 50.0, "eq", "Test message"
        )
        assert metric_eq.result == ValidationResult.PASS
    
    def test_overall_score_calculation(self):
        """Test du calcul du score global"""
        from tests.validation.strategy_validator import ValidationMetric
        
        # Créer des métriques de test
        metrics = [
            ValidationMetric("m1", 100, 50, "gte", ValidationResult.PASS, "Pass 1"),
            ValidationMetric("m2", 75, 50, "gte", ValidationResult.PASS, "Pass 2"),
            ValidationMetric("m3", 25, 50, "gte", ValidationResult.FAIL, "Fail 1"),
            ValidationMetric("m4", 40, 50, "gte", ValidationResult.WARNING, "Warning 1")
        ]
        
        score = self.validator._calculate_overall_score(metrics)
        
        # Score attendu: (1.0 + 1.0 + 0.0 + 0.5) / 4 * 100 = 62.5%
        assert abs(score - 62.5) < 0.1
    
    def test_recommendations_generation(self):
        """Test de génération de recommandations"""
        from tests.validation.strategy_validator import ValidationMetric
        
        # Métriques avec problèmes critiques
        critical_metrics = [
            ValidationMetric("max_drawdown", 30, 20, "lte", ValidationResult.FAIL, "Drawdown élevé"),
            ValidationMetric("total_return", 2, 5, "gte", ValidationResult.FAIL, "Rendement faible"),
            ValidationMetric("win_rate", 80, 50, "gte", ValidationResult.PASS, "Bon win rate")
        ]
        
        recommendation, critical_issues = self.validator._generate_recommendations(critical_metrics)
        
        assert "REJETÉ" in recommendation
        assert len(critical_issues) == 2  # 2 problèmes critiques
        assert any("Risque élevé" in issue for issue in critical_issues)
        assert any("Performance insuffisante" in issue for issue in critical_issues)
    
    def test_monthly_returns_calculation(self):
        """Test du calcul des rendements mensuels"""
        # Créer des trades sur plusieurs mois
        trades = []
        base_date = datetime(2024, 1, 1)
        
        for month in range(1, 4):  # 3 mois
            for day in range(1, 11):  # 10 trades par mois
                trade = {
                    'timestamp': datetime(2024, month, day).isoformat(),
                    'pnl': 100.0 if day % 2 == 0 else -50.0
                }
                trades.append(trade)
        
        monthly_returns = self.validator._calculate_monthly_returns(trades)
        
        # Doit avoir 3 mois de données
        assert len(monthly_returns) == 3
        
        # Chaque mois: 5 trades à +100 et 5 trades à -50 = +250
        for monthly_return in monthly_returns:
            assert abs(monthly_return - 250.0) < 0.1
    
    @pytest.mark.asyncio
    async def test_validation_with_real_bot_data(self):
        """Test de validation avec des données réelles de bot"""
        # Simuler des données d'un bot de scalping
        scalping_trades = []
        base_time = datetime.now() - timedelta(days=30)
        
        for i in range(200):  # 200 trades sur 30 jours
            # Scalping: petits profits fréquents, quelques pertes
            if i % 10 == 0:  # 10% de pertes
                pnl = -25.0
            else:  # 90% de profits
                pnl = 8.0
            
            trade = {
                'id': f'scalp_{i}',
                'symbol': 'ETH/USDC',
                'pnl': pnl,
                'size': 0.1,
                'price': 2000.0 + (i % 100),  # Prix variable
                'timestamp': (base_time + timedelta(hours=i*3.6)).isoformat(),
                'capital': 10000.0
            }
            scalping_trades.append(trade)
        
        strategy_data = {
            'trades': scalping_trades,
            'prices': [],
            'positions': []
        }
        
        # Valider
        report = await self.validator.validate_strategy("Scalping Bot", strategy_data)
        
        # Vérifications spécifiques au scalping
        assert report.total_tests > 0
        
        # Le scalping doit avoir un bon win rate
        performance_metrics = {m.name: m for m in report.performance_metrics}
        win_rate_metric = performance_metrics['win_rate']
        assert win_rate_metric.value > 85.0  # 90% attendu
        
        # Beaucoup de trades
        robustness_metrics = {m.name: m for m in report.robustness_metrics}
        num_trades_metric = robustness_metrics['num_trades']
        assert num_trades_metric.result == ValidationResult.PASS
        assert num_trades_metric.value == 200
    
    @pytest.mark.asyncio
    async def test_error_handling(self):
        """Test de gestion d'erreurs"""
        # Données malformées
        malformed_data = {
            'trades': [
                {'invalid': 'data'},
                {'pnl': 'not_a_number'},
                {'timestamp': 'invalid_date'}
            ],
            'prices': [],
            'positions': []
        }
        
        # La validation ne doit pas planter
        report = await self.validator.validate_strategy("Malformed Strategy", malformed_data)
        
        # Doit retourner un rapport même avec des données invalides
        assert report is not None
        assert report.strategy_name == "Malformed Strategy"
        
        # Probablement beaucoup d'échecs à cause des données invalides
        assert report.failed_tests >= 0
