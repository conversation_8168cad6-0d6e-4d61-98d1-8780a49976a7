"""
📈 Collecteur de métriques pour le dashboard
Collecte et agrège les métriques de performance de tous les bots
"""

import asyncio
import time
import json
from decimal import Decimal
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from collections import defaultdict, deque
import logging
from pathlib import Path
import sys

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from utils.error_handler import error_handler, ErrorCategory, ErrorSeverity
from logging_system.central_logger import central_logger, LogCategory
from risk_management.portfolio_manager import portfolio_manager

@dataclass
class MetricPoint:
    """Point de métrique avec timestamp"""
    timestamp: datetime
    value: Union[float, int, str]
    metadata: Dict[str, Any] = None

@dataclass
class BotMetrics:
    """Métriques d'un bot"""
    bot_id: str
    bot_type: str
    status: str
    uptime_seconds: float
    
    # Métriques de trading
    total_trades: int
    successful_trades: int
    failed_trades: int
    win_rate: float
    
    # Métriques financières
    total_pnl: float
    daily_pnl: float
    total_volume: float
    daily_volume: float
    
    # Métriques de risque
    current_drawdown: float
    max_drawdown: float
    sharpe_ratio: float
    
    # Métriques techniques
    avg_execution_time: float
    error_rate: float
    last_error: Optional[str]
    
    # Timestamp
    last_updated: datetime

@dataclass
class SystemMetrics:
    """Métriques système globales"""
    timestamp: datetime
    
    # Performance globale
    total_portfolio_value: float
    total_daily_pnl: float
    total_unrealized_pnl: float
    
    # Activité
    active_bots: int
    total_active_positions: int
    total_daily_trades: int
    
    # Santé système
    system_health_score: float
    avg_response_time: float
    error_rate: float
    
    # Ressources
    cpu_usage: float
    memory_usage: float
    disk_usage: float

class MetricsCollector:
    """Collecteur de métriques pour le dashboard"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Stockage des métriques
        self.bot_metrics: Dict[str, BotMetrics] = {}
        self.system_metrics_history: deque = deque(maxlen=1440)  # 24h de données (1 point/minute)
        self.metric_points: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1440))
        
        # Configuration
        self.collection_interval = 30  # 30 secondes
        self.retention_hours = 24
        
        # Bots enregistrés
        self.registered_bots: Dict[str, Dict[str, Any]] = {}
        
        # Cache des calculs
        self.cache: Dict[str, Any] = {}
        self.cache_ttl = 60  # 1 minute
        self.last_cache_update = 0
        
        central_logger.log(
            level="INFO",
            message="Collecteur de métriques initialisé",
            category=LogCategory.SYSTEM
        )
    
    def register_bot(self, bot_id: str, bot_type: str, bot_instance: Any = None):
        """Enregistre un bot pour la collecte de métriques"""
        try:
            self.registered_bots[bot_id] = {
                'type': bot_type,
                'instance': bot_instance,
                'registered_at': datetime.now(),
                'last_collected': None
            }
            
            central_logger.log(
                level="INFO",
                message=f"Bot enregistré pour métriques: {bot_id}",
                category=LogCategory.SYSTEM,
                bot_type=bot_type
            )
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.MEDIUM, {
                'function': 'register_bot',
                'bot_id': bot_id
            })
    
    def unregister_bot(self, bot_id: str):
        """Désenregistre un bot"""
        try:
            if bot_id in self.registered_bots:
                del self.registered_bots[bot_id]
            
            if bot_id in self.bot_metrics:
                del self.bot_metrics[bot_id]
            
            central_logger.log(
                level="INFO",
                message=f"Bot désenregistré: {bot_id}",
                category=LogCategory.SYSTEM
            )
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.LOW, {
                'function': 'unregister_bot',
                'bot_id': bot_id
            })
    
    async def start_collection(self):
        """Démarre la collecte de métriques"""
        while True:
            try:
                # Collecter les métriques de tous les bots
                await self._collect_bot_metrics()
                
                # Collecter les métriques système
                await self._collect_system_metrics()
                
                # Nettoyer les anciennes données
                self._cleanup_old_metrics()
                
                # Attendre avant la prochaine collecte
                await asyncio.sleep(self.collection_interval)
                
            except Exception as e:
                error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.MEDIUM, {
                    'function': 'start_collection'
                })
                await asyncio.sleep(60)  # Pause plus longue en cas d'erreur
    
    async def _collect_bot_metrics(self):
        """Collecte les métriques de tous les bots"""
        try:
            for bot_id, bot_info in self.registered_bots.items():
                try:
                    metrics = await self._get_bot_metrics(bot_id, bot_info)
                    if metrics:
                        self.bot_metrics[bot_id] = metrics
                        bot_info['last_collected'] = datetime.now()
                        
                        # Ajouter aux séries temporelles
                        self._add_metric_point(f"{bot_id}_pnl", metrics.total_pnl)
                        self._add_metric_point(f"{bot_id}_trades", metrics.total_trades)
                        self._add_metric_point(f"{bot_id}_win_rate", metrics.win_rate)
                
                except Exception as e:
                    error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.LOW, {
                        'function': '_collect_bot_metrics',
                        'bot_id': bot_id
                    })
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.MEDIUM, {
                'function': '_collect_bot_metrics'
            })
    
    async def _get_bot_metrics(self, bot_id: str, bot_info: Dict[str, Any]) -> Optional[BotMetrics]:
        """Récupère les métriques d'un bot spécifique"""
        try:
            bot_type = bot_info['type']
            bot_instance = bot_info.get('instance')
            
            # Métriques par défaut
            metrics = BotMetrics(
                bot_id=bot_id,
                bot_type=bot_type,
                status="unknown",
                uptime_seconds=0,
                total_trades=0,
                successful_trades=0,
                failed_trades=0,
                win_rate=0.0,
                total_pnl=0.0,
                daily_pnl=0.0,
                total_volume=0.0,
                daily_volume=0.0,
                current_drawdown=0.0,
                max_drawdown=0.0,
                sharpe_ratio=0.0,
                avg_execution_time=0.0,
                error_rate=0.0,
                last_error=None,
                last_updated=datetime.now()
            )
            
            # Récupérer les métriques selon le type de bot
            if bot_instance and hasattr(bot_instance, 'get_status'):
                status = bot_instance.get_status()
                metrics.status = "running" if status.get('is_running', False) else "stopped"
                
                # Calculer l'uptime
                start_time = status.get('start_time')
                if start_time:
                    if isinstance(start_time, str):
                        start_time = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                    metrics.uptime_seconds = (datetime.now() - start_time).total_seconds()
            
            # Récupérer les statistiques de performance
            if bot_instance and hasattr(bot_instance, 'get_performance_stats'):
                stats = bot_instance.get_performance_stats()
                
                metrics.total_trades = stats.get('total_executions', stats.get('total_trades', 0))
                metrics.successful_trades = stats.get('successful_executions', stats.get('successful_trades', 0))
                metrics.failed_trades = metrics.total_trades - metrics.successful_trades
                metrics.win_rate = stats.get('success_rate', stats.get('win_rate', 0))
                metrics.total_pnl = stats.get('net_profit', stats.get('total_pnl', 0))
                metrics.total_volume = stats.get('total_copy_volume', stats.get('total_volume', 0))
                metrics.avg_execution_time = stats.get('average_execution_time_seconds', 0)
            
            # Métriques spécifiques selon le type
            if bot_type == "dex_scalping":
                await self._collect_dex_scalping_metrics(metrics, bot_instance)
            elif bot_type == "cross_chain_arbitrage":
                await self._collect_arbitrage_metrics(metrics, bot_instance)
            elif bot_type == "copy_trading":
                await self._collect_copy_trading_metrics(metrics, bot_instance)
            
            return metrics
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.LOW, {
                'function': '_get_bot_metrics',
                'bot_id': bot_id
            })
            return None
    
    async def _collect_dex_scalping_metrics(self, metrics: BotMetrics, bot_instance):
        """Collecte les métriques spécifiques au scalping DEX"""
        try:
            if hasattr(bot_instance, 'scalping_engine') and bot_instance.scalping_engine:
                engine_stats = bot_instance.scalping_engine.get_performance_stats()
                metrics.total_volume = engine_stats.get('total_volume', 0)
                metrics.avg_execution_time = engine_stats.get('avg_execution_time', 0)
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.LOW, {
                'function': '_collect_dex_scalping_metrics'
            })
    
    async def _collect_arbitrage_metrics(self, metrics: BotMetrics, bot_instance):
        """Collecte les métriques spécifiques à l'arbitrage"""
        try:
            if hasattr(bot_instance, 'executor') and bot_instance.executor:
                executor_stats = bot_instance.executor.get_performance_statistics()
                metrics.total_volume = executor_stats.get('total_copy_volume', 0)
                metrics.avg_execution_time = executor_stats.get('average_execution_time_seconds', 0)
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.LOW, {
                'function': '_collect_arbitrage_metrics'
            })
    
    async def _collect_copy_trading_metrics(self, metrics: BotMetrics, bot_instance):
        """Collecte les métriques spécifiques au copy trading"""
        try:
            if hasattr(bot_instance, 'copy_executor') and bot_instance.copy_executor:
                executor_stats = bot_instance.copy_executor.get_performance_statistics()
                metrics.total_volume = executor_stats.get('total_copy_volume', 0)
                
                # Positions actives
                active_trades = len(bot_instance.copy_executor.get_active_copy_trades())
                self._add_metric_point(f"{metrics.bot_id}_active_positions", active_trades)
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.LOW, {
                'function': '_collect_copy_trading_metrics'
            })
    
    async def _collect_system_metrics(self):
        """Collecte les métriques système globales"""
        try:
            # Métriques du portefeuille
            portfolio_value = portfolio_manager.get_portfolio_value()
            daily_pnl = sum(bot.daily_pnl for bot in self.bot_metrics.values())
            unrealized_pnl = sum(bot.total_pnl for bot in self.bot_metrics.values()) - daily_pnl
            
            # Métriques d'activité
            active_bots = len([bot for bot in self.bot_metrics.values() if bot.status == "running"])
            total_trades = sum(bot.total_trades for bot in self.bot_metrics.values())
            
            # Score de santé système (simplifié)
            health_score = self._calculate_system_health()
            
            # Métriques de ressources (simulation)
            import psutil
            cpu_usage = psutil.cpu_percent()
            memory_usage = psutil.virtual_memory().percent
            disk_usage = psutil.disk_usage('/').percent
            
            # Créer les métriques système
            system_metrics = SystemMetrics(
                timestamp=datetime.now(),
                total_portfolio_value=portfolio_value,
                total_daily_pnl=daily_pnl,
                total_unrealized_pnl=unrealized_pnl,
                active_bots=active_bots,
                total_active_positions=0,  # À calculer
                total_daily_trades=total_trades,
                system_health_score=health_score,
                avg_response_time=0.0,  # À implémenter
                error_rate=0.0,  # À calculer
                cpu_usage=cpu_usage,
                memory_usage=memory_usage,
                disk_usage=disk_usage
            )
            
            # Ajouter à l'historique
            self.system_metrics_history.append(system_metrics)
            
            # Ajouter aux séries temporelles
            self._add_metric_point("portfolio_value", portfolio_value)
            self._add_metric_point("daily_pnl", daily_pnl)
            self._add_metric_point("active_bots", active_bots)
            self._add_metric_point("system_health", health_score)
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.MEDIUM, {
                'function': '_collect_system_metrics'
            })
    
    def _calculate_system_health(self) -> float:
        """Calcule un score de santé système"""
        try:
            if not self.bot_metrics:
                return 0.5
            
            health_factors = []
            
            # Facteur 1: Pourcentage de bots actifs
            active_bots = len([bot for bot in self.bot_metrics.values() if bot.status == "running"])
            total_bots = len(self.bot_metrics)
            if total_bots > 0:
                active_ratio = active_bots / total_bots
                health_factors.append(active_ratio)
            
            # Facteur 2: Taux d'erreur moyen
            avg_error_rate = sum(bot.error_rate for bot in self.bot_metrics.values()) / len(self.bot_metrics)
            error_health = max(0, 1 - avg_error_rate)
            health_factors.append(error_health)
            
            # Facteur 3: Performance P&L
            total_pnl = sum(bot.total_pnl for bot in self.bot_metrics.values())
            pnl_health = 0.5 + min(0.5, max(-0.5, total_pnl / 10000))  # Normaliser autour de 0.5
            health_factors.append(pnl_health)
            
            # Score final
            return sum(health_factors) / len(health_factors) if health_factors else 0.5
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.LOW, {
                'function': '_calculate_system_health'
            })
            return 0.5
    
    def _add_metric_point(self, metric_name: str, value: Union[float, int]):
        """Ajoute un point de métrique"""
        try:
            point = MetricPoint(
                timestamp=datetime.now(),
                value=value
            )
            self.metric_points[metric_name].append(point)
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.LOW, {
                'function': '_add_metric_point',
                'metric_name': metric_name
            })
    
    def _cleanup_old_metrics(self):
        """Nettoie les anciennes métriques"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=self.retention_hours)
            
            # Nettoyer les séries temporelles
            for metric_name, points in self.metric_points.items():
                while points and points[0].timestamp < cutoff_time:
                    points.popleft()
            
            # Nettoyer l'historique système
            while (self.system_metrics_history and 
                   self.system_metrics_history[0].timestamp < cutoff_time):
                self.system_metrics_history.popleft()
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.LOW, {
                'function': '_cleanup_old_metrics'
            })
    
    def get_bot_metrics(self, bot_id: str = None) -> Union[BotMetrics, Dict[str, BotMetrics]]:
        """Récupère les métriques d'un bot ou de tous les bots"""
        if bot_id:
            return self.bot_metrics.get(bot_id)
        return self.bot_metrics.copy()
    
    def get_system_metrics(self) -> Optional[SystemMetrics]:
        """Récupère les dernières métriques système"""
        return self.system_metrics_history[-1] if self.system_metrics_history else None
    
    def get_metric_history(self, metric_name: str, hours: int = 1) -> List[MetricPoint]:
        """Récupère l'historique d'une métrique"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            points = self.metric_points.get(metric_name, deque())
            
            return [point for point in points if point.timestamp >= cutoff_time]
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.LOW, {
                'function': 'get_metric_history',
                'metric_name': metric_name
            })
            return []
    
    def get_dashboard_summary(self) -> Dict[str, Any]:
        """Récupère un résumé pour le dashboard"""
        try:
            current_time = time.time()
            
            # Utiliser le cache si disponible
            if (current_time - self.last_cache_update < self.cache_ttl and 
                'dashboard_summary' in self.cache):
                return self.cache['dashboard_summary']
            
            # Calculer le résumé
            system_metrics = self.get_system_metrics()
            bot_metrics = self.get_bot_metrics()
            
            summary = {
                'timestamp': datetime.now().isoformat(),
                'system': {
                    'portfolio_value': system_metrics.total_portfolio_value if system_metrics else 0,
                    'daily_pnl': system_metrics.total_daily_pnl if system_metrics else 0,
                    'active_bots': system_metrics.active_bots if system_metrics else 0,
                    'health_score': system_metrics.system_health_score if system_metrics else 0,
                    'cpu_usage': system_metrics.cpu_usage if system_metrics else 0,
                    'memory_usage': system_metrics.memory_usage if system_metrics else 0
                },
                'bots': {},
                'alerts': self._get_active_alerts()
            }
            
            # Ajouter les métriques de chaque bot
            for bot_id, metrics in bot_metrics.items():
                summary['bots'][bot_id] = {
                    'type': metrics.bot_type,
                    'status': metrics.status,
                    'uptime': metrics.uptime_seconds,
                    'total_trades': metrics.total_trades,
                    'win_rate': metrics.win_rate,
                    'total_pnl': metrics.total_pnl,
                    'daily_pnl': metrics.daily_pnl
                }
            
            # Mettre en cache
            self.cache['dashboard_summary'] = summary
            self.last_cache_update = current_time
            
            return summary
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.MEDIUM, {
                'function': 'get_dashboard_summary'
            })
            return {'error': str(e)}
    
    def _get_active_alerts(self) -> List[Dict[str, Any]]:
        """Récupère les alertes actives"""
        alerts = []
        
        try:
            # Vérifier les alertes système
            system_metrics = self.get_system_metrics()
            if system_metrics:
                if system_metrics.system_health_score < 0.3:
                    alerts.append({
                        'type': 'system',
                        'level': 'critical',
                        'message': 'Santé système critique',
                        'value': system_metrics.system_health_score
                    })
                
                if system_metrics.cpu_usage > 90:
                    alerts.append({
                        'type': 'resource',
                        'level': 'warning',
                        'message': 'Utilisation CPU élevée',
                        'value': system_metrics.cpu_usage
                    })
            
            # Vérifier les alertes des bots
            for bot_id, metrics in self.bot_metrics.items():
                if metrics.status != "running":
                    alerts.append({
                        'type': 'bot',
                        'level': 'warning',
                        'message': f'Bot {bot_id} arrêté',
                        'bot_id': bot_id
                    })
                
                if metrics.error_rate > 0.1:  # Plus de 10% d'erreurs
                    alerts.append({
                        'type': 'bot',
                        'level': 'warning',
                        'message': f'Taux d\'erreur élevé pour {bot_id}',
                        'bot_id': bot_id,
                        'value': metrics.error_rate
                    })
            
            return alerts
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.LOW, {
                'function': '_get_active_alerts'
            })
            return []
    
    def get_performance_analytics(self, hours: int = 24) -> Dict[str, Any]:
        """Génère des analytics de performance"""
        try:
            analytics = {
                'period_hours': hours,
                'generated_at': datetime.now().isoformat(),
                'portfolio': {},
                'bots': {},
                'trends': {}
            }
            
            # Analytics du portefeuille
            portfolio_history = self.get_metric_history('portfolio_value', hours)
            pnl_history = self.get_metric_history('daily_pnl', hours)
            
            if portfolio_history:
                analytics['portfolio'] = {
                    'current_value': portfolio_history[-1].value,
                    'start_value': portfolio_history[0].value,
                    'change_percent': ((portfolio_history[-1].value - portfolio_history[0].value) / portfolio_history[0].value) * 100,
                    'max_value': max(p.value for p in portfolio_history),
                    'min_value': min(p.value for p in portfolio_history)
                }
            
            # Analytics par bot
            for bot_id, metrics in self.bot_metrics.items():
                bot_pnl_history = self.get_metric_history(f"{bot_id}_pnl", hours)
                bot_trades_history = self.get_metric_history(f"{bot_id}_trades", hours)
                
                analytics['bots'][bot_id] = {
                    'type': metrics.bot_type,
                    'performance': {
                        'total_pnl': metrics.total_pnl,
                        'win_rate': metrics.win_rate,
                        'total_trades': metrics.total_trades,
                        'avg_execution_time': metrics.avg_execution_time
                    },
                    'trends': {
                        'pnl_trend': 'up' if len(bot_pnl_history) > 1 and bot_pnl_history[-1].value > bot_pnl_history[0].value else 'down',
                        'activity_level': 'high' if metrics.total_trades > 10 else 'low'
                    }
                }
            
            return analytics
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.MEDIUM, {
                'function': 'get_performance_analytics'
            })
            return {'error': str(e)}
