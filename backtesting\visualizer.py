"""
📊 Visualisateur de résultats de backtesting
Génère des graphiques et rapports pour analyser les performances
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from typing import Dict, List, Optional
import logging
from pathlib import Path
import sys
import os
import json
from datetime import datetime

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

class BacktestVisualizer:
    """Visualisateur de résultats de backtesting"""
    
    def __init__(self, output_dir: str = "assets/results/backtests"):
        self.logger = logging.getLogger(__name__)
        self.output_dir = output_dir
        Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    def plot_equity_curve(self, equity_df: pd.DataFrame, title: str = "Equity Curve", 
                         show_drawdowns: bool = True, save_path: Optional[str] = None):
        """
        Trace la courbe d'équité
        
        Args:
            equity_df: DataFrame avec colonne 'portfolio_value'
            title: Titre du graphique
            show_drawdowns: Afficher les drawdowns
            save_path: Chemin pour sauvegarder l'image
        """
        plt.figure(figsize=(12, 6))
        
        # Tracer la courbe d'équité
        plt.plot(equity_df.index, equity_df['portfolio_value'], 
                 label='Portfolio Value', color='blue', linewidth=2)
        
        # Ajouter les composantes si disponibles
        if 'cash' in equity_df.columns and 'positions_value' in equity_df.columns:
            plt.plot(equity_df.index, equity_df['cash'], 
                     label='Cash', color='green', alpha=0.5, linestyle='--')
            plt.plot(equity_df.index, equity_df['positions_value'], 
                     label='Positions Value', color='orange', alpha=0.5, linestyle='--')
        
        # Afficher les drawdowns
        if show_drawdowns:
            # Calculer les pics cumulés
            peak = equity_df['portfolio_value'].expanding().max()
            drawdown = (equity_df['portfolio_value'] - peak) / peak * 100
            
            # Trouver les périodes de drawdown significatives (>5%)
            significant_dd = drawdown < -5
            
            if significant_dd.any():
                # Colorer les zones de drawdown
                for i in range(len(significant_dd)):
                    if significant_dd.iloc[i]:
                        plt.axvspan(equity_df.index[i], equity_df.index[i], 
                                   alpha=0.2, color='red')
        
        plt.title(title, fontsize=14)
        plt.xlabel('Date', fontsize=12)
        plt.ylabel('Portfolio Value', fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.legend()
        
        # Formater l'axe des dates
        plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        plt.gca().xaxis.set_major_locator(mdates.AutoDateLocator())
        plt.gcf().autofmt_xdate()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"📊 Graphique sauvegardé: {save_path}")
        
        plt.tight_layout()
        plt.show()
    
    def plot_drawdown_chart(self, equity_df: pd.DataFrame, title: str = "Drawdown Analysis", 
                           save_path: Optional[str] = None):
        """
        Trace le graphique de drawdown
        
        Args:
            equity_df: DataFrame avec colonne 'portfolio_value'
            title: Titre du graphique
            save_path: Chemin pour sauvegarder l'image
        """
        plt.figure(figsize=(12, 6))
        
        # Calculer le drawdown
        peak = equity_df['portfolio_value'].expanding().max()
        drawdown = (equity_df['portfolio_value'] - peak) / peak * 100
        
        # Tracer le drawdown
        plt.fill_between(equity_df.index, drawdown, 0, color='red', alpha=0.3)
        plt.plot(equity_df.index, drawdown, color='red', linewidth=1)
        
        plt.title(title, fontsize=14)
        plt.xlabel('Date', fontsize=12)
        plt.ylabel('Drawdown (%)', fontsize=12)
        plt.grid(True, alpha=0.3)
        
        # Ajouter des lignes horizontales pour les niveaux de drawdown
        plt.axhline(y=-5, color='orange', linestyle='--', alpha=0.7, label='-5%')
        plt.axhline(y=-10, color='red', linestyle='--', alpha=0.7, label='-10%')
        plt.axhline(y=-20, color='darkred', linestyle='--', alpha=0.7, label='-20%')
        
        plt.legend()
        
        # Formater l'axe des dates
        plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        plt.gca().xaxis.set_major_locator(mdates.AutoDateLocator())
        plt.gcf().autofmt_xdate()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"📊 Graphique sauvegardé: {save_path}")
        
        plt.tight_layout()
        plt.show()
    
    def plot_trades(self, trades_df: pd.DataFrame, price_df: pd.DataFrame, 
                   title: str = "Trades Analysis", save_path: Optional[str] = None):
        """
        Trace les trades sur le graphique de prix
        
        Args:
            trades_df: DataFrame des trades
            price_df: DataFrame avec les prix
            title: Titre du graphique
            save_path: Chemin pour sauvegarder l'image
        """
        if trades_df.empty:
            self.logger.warning("⚠️ Aucun trade à afficher")
            return
        
        plt.figure(figsize=(12, 6))
        
        # Tracer le prix
        plt.plot(price_df.index, price_df['close'], color='blue', alpha=0.5, linewidth=1)
        
        # Tracer les trades
        for _, trade in trades_df.iterrows():
            marker = '^' if trade['side'] == 'buy' else 'v'
            color = 'green' if trade['side'] == 'buy' else 'red'
            plt.scatter(trade['timestamp'], trade['price'], marker=marker, 
                       s=100, color=color, alpha=0.7)
        
        plt.title(title, fontsize=14)
        plt.xlabel('Date', fontsize=12)
        plt.ylabel('Price', fontsize=12)
        plt.grid(True, alpha=0.3)
        
        # Légende
        buy_marker = plt.Line2D([], [], color='green', marker='^', linestyle='None',
                              markersize=10, label='Buy')
        sell_marker = plt.Line2D([], [], color='red', marker='v', linestyle='None',
                               markersize=10, label='Sell')
        plt.legend(handles=[buy_marker, sell_marker])
        
        # Formater l'axe des dates
        plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        plt.gca().xaxis.set_major_locator(mdates.AutoDateLocator())
        plt.gcf().autofmt_xdate()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"📊 Graphique sauvegardé: {save_path}")
        
        plt.tight_layout()
        plt.show()
    
    def plot_strategy_comparison(self, comparison_df: pd.DataFrame, 
                               title: str = "Strategy Comparison", 
                               save_path: Optional[str] = None):
        """
        Trace un graphique de comparaison des stratégies
        
        Args:
            comparison_df: DataFrame de comparaison
            title: Titre du graphique
            save_path: Chemin pour sauvegarder l'image
        """
        if comparison_df.empty:
            self.logger.warning("⚠️ Aucune stratégie à comparer")
            return
        
        plt.figure(figsize=(14, 8))
        
        # Sélectionner les métriques à afficher
        metrics = ['Total Return (%)', 'Sharpe Ratio', 'Max Drawdown (%)', 'Win Rate (%)']
        
        # Créer un subplot pour chaque métrique
        for i, metric in enumerate(metrics):
            plt.subplot(2, 2, i+1)
            
            # Trier par la métrique actuelle
            if metric == 'Max Drawdown (%)':
                # Pour le drawdown, plus petit est meilleur
                sorted_df = comparison_df.sort_values(metric)
            else:
                # Pour les autres métriques, plus grand est meilleur
                sorted_df = comparison_df.sort_values(metric, ascending=False)
            
            # Créer le bar plot
            bars = plt.bar(sorted_df['Strategy'], sorted_df[metric], 
                          color='skyblue', alpha=0.7)
            
            # Ajouter les valeurs sur les barres
            for bar in bars:
                height = bar.get_height()
                plt.text(bar.get_x() + bar.get_width()/2., height,
                        f'{height:.2f}', ha='center', va='bottom', rotation=0)
            
            plt.title(metric)
            plt.xticks(rotation=45, ha='right')
            plt.grid(True, alpha=0.3, axis='y')
        
        plt.suptitle(title, fontsize=16)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"📊 Graphique sauvegardé: {save_path}")
        
        plt.show()
    
    def generate_report_html(self, backtest_result: Dict, 
                           output_path: Optional[str] = None) -> str:
        """
        Génère un rapport HTML complet
        
        Args:
            backtest_result: Résultat du backtesting
            output_path: Chemin pour sauvegarder le rapport
            
        Returns:
            str: Chemin du rapport généré
        """
        if 'error' in backtest_result:
            self.logger.error(f"❌ Erreur dans le résultat: {backtest_result['error']}")
            return ""
        
        # Créer un nom de fichier basé sur la date et la stratégie
        if not output_path:
            strategy_name = backtest_result['backtest_info']['strategy_name']
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_path = os.path.join(self.output_dir, f"report_{strategy_name}_{timestamp}.html")
        
        # Extraire les données
        performance = backtest_result['performance']
        risk_metrics = backtest_result['risk_metrics']
        trading_metrics = backtest_result['trading_metrics']
        backtest_info = backtest_result['backtest_info']
        
        # Générer les graphiques et les sauvegarder
        graphs_dir = os.path.join(os.path.dirname(output_path), 'graphs')
        Path(graphs_dir).mkdir(parents=True, exist_ok=True)
        
        equity_path = os.path.join(graphs_dir, f"equity_{os.path.basename(output_path).split('.')[0]}.png")
        drawdown_path = os.path.join(graphs_dir, f"drawdown_{os.path.basename(output_path).split('.')[0]}.png")
        
        # Sauvegarder les graphiques (sans affichage)
        plt.ioff()  # Désactiver l'affichage interactif

        self.plot_equity_curve(backtest_result['equity_curve'],
                             f"Equity Curve - {backtest_info['strategy_name']}",
                             save_path=equity_path)
        plt.close('all')

        self.plot_drawdown_chart(backtest_result['equity_curve'],
                               f"Drawdown - {backtest_info['strategy_name']}",
                               save_path=drawdown_path)
        plt.close('all')

        plt.ion()  # Réactiver l'affichage interactif
        
        # Créer le contenu HTML
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Backtest Report - {backtest_info['strategy_name']}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                h1, h2, h3 {{ color: #333; }}
                .container {{ max-width: 1200px; margin: 0 auto; }}
                .section {{ margin-bottom: 30px; }}
                .metrics {{ display: flex; flex-wrap: wrap; }}
                .metric-card {{ background: #f5f5f5; border-radius: 5px; padding: 15px; margin: 10px; flex: 1; min-width: 200px; }}
                .metric-value {{ font-size: 24px; font-weight: bold; margin: 10px 0; }}
                .metric-name {{ font-size: 14px; color: #666; }}
                table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                th, td {{ padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }}
                th {{ background-color: #f2f2f2; }}
                .positive {{ color: green; }}
                .negative {{ color: red; }}
                .chart {{ margin: 20px 0; max-width: 100%; }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>Backtest Report</h1>
                <div class="section">
                    <h2>Strategy Information</h2>
                    <table>
                        <tr><th>Strategy Name</th><td>{backtest_info['strategy_name']}</td></tr>
                        <tr><th>Symbol</th><td>{backtest_info['symbol']}</td></tr>
                        <tr><th>Period</th><td>{backtest_result['backtest_period']['start_date']} to {backtest_result['backtest_period']['end_date']}</td></tr>
                        <tr><th>Duration</th><td>{backtest_result['backtest_period']['duration_days']} days</td></tr>
                    </table>
                </div>
                
                <div class="section">
                    <h2>Performance Summary</h2>
                    <div class="metrics">
                        <div class="metric-card">
                            <div class="metric-name">Total Return</div>
                            <div class="metric-value {('positive' if performance['total_return_percent'] >= 0 else 'negative')}">{performance['total_return_percent']:.2f}%</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-name">Annualized Return</div>
                            <div class="metric-value {('positive' if performance['annualized_return_percent'] >= 0 else 'negative')}">{performance['annualized_return_percent']:.2f}%</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-name">Sharpe Ratio</div>
                            <div class="metric-value">{performance['sharpe_ratio']:.2f}</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-name">Max Drawdown</div>
                            <div class="metric-value negative">{risk_metrics['max_drawdown_percent']:.2f}%</div>
                        </div>
                    </div>
                </div>
                
                <div class="section">
                    <h2>Equity Curve</h2>
                    <img src="{os.path.relpath(equity_path, os.path.dirname(output_path))}" class="chart" alt="Equity Curve">
                </div>
                
                <div class="section">
                    <h2>Drawdown Analysis</h2>
                    <img src="{os.path.relpath(drawdown_path, os.path.dirname(output_path))}" class="chart" alt="Drawdown Chart">
                </div>
                
                <div class="section">
                    <h2>Trading Metrics</h2>
                    <div class="metrics">
                        <div class="metric-card">
                            <div class="metric-name">Total Trades</div>
                            <div class="metric-value">{trading_metrics['total_trades']}</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-name">Win Rate</div>
                            <div class="metric-value">{trading_metrics['win_rate_percent']:.2f}%</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-name">Profit Factor</div>
                            <div class="metric-value">{trading_metrics['profit_factor']:.2f}</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-name">Avg Trade</div>
                            <div class="metric-value {('positive' if trading_metrics['avg_trade'] >= 0 else 'negative')}">{trading_metrics['avg_trade']:.2f}</div>
                        </div>
                    </div>
                </div>
                
                <div class="section">
                    <h2>Risk Metrics</h2>
                    <table>
                        <tr><th>Metric</th><th>Value</th></tr>
                        <tr><td>Volatility</td><td>{performance['volatility_percent']:.2f}%</td></tr>
                        <tr><td>Sortino Ratio</td><td>{performance['sortino_ratio']:.2f}</td></tr>
                        <tr><td>Calmar Ratio</td><td>{performance['calmar_ratio']:.2f}</td></tr>
                        <tr><td>Value at Risk (95%)</td><td>{risk_metrics['var_95']:.2f}%</td></tr>
                        <tr><td>Conditional VaR (95%)</td><td>{risk_metrics['cvar_95']:.2f}%</td></tr>
                    </table>
                </div>
                
                <div class="section">
                    <h2>Trading Costs</h2>
                    <table>
                        <tr><th>Metric</th><th>Value</th></tr>
                        <tr><td>Trading Fee</td><td>{backtest_info['trading_fee_percent']:.2f}%</td></tr>
                        <tr><td>Slippage</td><td>{backtest_info['slippage_percent']:.2f}%</td></tr>
                        <tr><td>Total Fees Paid</td><td>{backtest_info['total_fees_paid']:.2f}</td></tr>
                        <tr><td>Total Slippage Cost</td><td>{backtest_info['total_slippage_cost']:.2f}</td></tr>
                    </table>
                </div>
                
                <div class="section">
                    <h2>Generated on</h2>
                    <p>{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        # Écrire le fichier HTML
        with open(output_path, 'w') as f:
            f.write(html_content)
        
        self.logger.info(f"📄 Rapport HTML généré: {output_path}")
        return output_path
    
    def save_backtest_results(self, backtest_result: Dict, output_path: Optional[str] = None) -> str:
        """
        Sauvegarde les résultats du backtesting au format JSON
        
        Args:
            backtest_result: Résultat du backtesting
            output_path: Chemin pour sauvegarder les résultats
            
        Returns:
            str: Chemin du fichier sauvegardé
        """
        if not output_path:
            strategy_name = backtest_result['backtest_info']['strategy_name']
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_path = os.path.join(self.output_dir, f"results_{strategy_name}_{timestamp}.json")
        
        # Convertir les DataFrames en listes pour JSON
        result_copy = backtest_result.copy()
        
        if 'equity_curve' in result_copy:
            result_copy['equity_curve'] = result_copy['equity_curve'].reset_index().to_dict('records')
        
        if 'trades_df' in result_copy:
            result_copy['trades_df'] = result_copy['trades_df'].to_dict('records')
        
        # Convertir les timestamps en strings
        for key in ['backtest_period']:
            if key in result_copy:
                for date_key in ['start_date', 'end_date']:
                    if date_key in result_copy[key]:
                        result_copy[key][date_key] = str(result_copy[key][date_key])
        
        # Sauvegarder en JSON
        with open(output_path, 'w') as f:
            json.dump(result_copy, f, indent=2)
        
        self.logger.info(f"💾 Résultats sauvegardés: {output_path}")
        return output_path

# Instance globale
visualizer = BacktestVisualizer()
