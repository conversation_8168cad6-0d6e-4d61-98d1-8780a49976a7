# 🧪 Paper Trading botCrypto

## 🎯 Vue d'ensemble

Le système de paper trading permet de tester vos stratégies de trading en temps réel sans risquer de capital réel. Il simule l'exécution de trades avec des données de marché réelles ou simulées, permettant de valider les stratégies avant le déploiement en production.

## 🏗️ Architecture

```
paper_trading/
├── __init__.py              # Module principal
├── simulator.py             # Simulateur de trading
├── session_manager.py       # Gestionnaire de sessions
└── README.md               # Cette documentation
```

## 🚀 Utilisation Rapide

### 1. Démarrer une session simple

```bash
# Via le script principal
python run_bot.py --paper-trading "Grid Trading" --symbol BTC/USDT --capital 1000

# Via le script dédié
python run_paper_trading.py --create "SMA Crossover" --symbol ETH/USDT --capital 5000

# Avec des données de marché réelles
python run_paper_trading.py --create "RSI Mean Reversion" --real-data
```

### 2. Mode interactif

```bash
# Lancer le mode interactif
python run_paper_trading.py --interactive
```

### 3. Gestion des sessions

```bash
# Lister les sessions
python run_paper_trading.py --status

# Démarrer une session
python run_paper_trading.py --start session_1_20241201_143022

# Arrêter une session
python run_paper_trading.py --stop session_1_20241201_143022

# Voir les statistiques
python run_paper_trading.py --summary
```

## 🎮 Fonctionnalités

### ✅ Simulation Réaliste
- **Frais de trading** : 0.1% par défaut (configurable)
- **Slippage** : 0.05% par défaut (configurable)
- **Gestion du capital** : Vérification des fonds disponibles
- **Positions** : Suivi des positions ouvertes

### 📊 Sources de Données
- **Données simulées** : Mouvement brownien géométrique
- **Données réelles** : Prix de marché en temps réel via API
- **Indicateurs techniques** : Automatiquement calculés

### 🔄 Gestion des Sessions
- **Sessions multiples** : Plusieurs stratégies simultanées
- **Persistance** : Sauvegarde automatique des résultats
- **Monitoring** : Suivi en temps réel des performances

## 📈 Stratégies Supportées

Toutes les stratégies du module `backtesting.strategies` sont supportées :

| Stratégie | Description | Recommandé pour |
|-----------|-------------|-----------------|
| **Grid Trading** | Ordres à intervalles réguliers | Marchés latéraux |
| **SMA Crossover** | Croisement moyennes mobiles | Tendances claires |
| **RSI Mean Reversion** | Retour à la moyenne RSI | Marchés volatiles |
| **Bollinger Bands** | Trading sur les bandes | Volatilité normale |
| **MACD** | Signaux MACD/Signal | Tendances moyennes |
| **Buy & Hold** | Référence passive | Comparaison |

## 🔧 Utilisation Programmatique

### Exemple Simple

```python
from paper_trading.session_manager import session_manager

# Créer une session
session_id = session_manager.create_session(
    strategy_name="Grid Trading",
    symbol="BTC/USDT",
    capital=1000,
    use_real_data=False
)

# Démarrer la session
session_manager.start_session(session_id)

# Monitorer (dans une boucle)
import time
for i in range(10):
    time.sleep(30)  # Attendre 30 secondes
    status = session_manager.get_session_status(session_id)
    if status and 'performance' in status:
        perf = status['performance']
        print(f"Return: {perf['total_return_percent']:.2f}%")

# Arrêter la session
session_manager.stop_session(session_id)

# Sauvegarder les résultats
session_manager.save_session_results(session_id)
```

### Stratégie Personnalisée

```python
def my_custom_strategy(engine, data, index, symbol):
    """Stratégie momentum simple"""
    if index < 5:
        return
    
    current_price = data.iloc[index]['close']
    past_price = data.iloc[index-5]['close']
    position = engine.positions.get(symbol, 0)
    
    # Signal d'achat : prix en hausse de 2%
    if current_price > past_price * 1.02 and position == 0:
        quantity = (engine.current_capital * 0.8) / current_price
        engine.execute_trade(symbol, 'buy', quantity, current_price)
    
    # Signal de vente : prix en baisse de 2%
    elif current_price < past_price * 0.98 and position > 0:
        engine.execute_trade(symbol, 'sell', position, current_price)

# Créer une session avec stratégie personnalisée
session_id = session_manager.create_custom_strategy_session(
    strategy_func=my_custom_strategy,
    strategy_name="My Momentum Strategy",
    symbol="ETH/USDT",
    capital=2000
)
```

## 📊 Métriques et Monitoring

### Métriques Calculées
- **Rendement Total** : Performance globale en %
- **Nombre de Trades** : Total des transactions
- **Taux de Réussite** : % de trades gagnants
- **P&L Total** : Profit/Loss cumulé
- **Position Actuelle** : Quantité détenue
- **Prix Actuel** : Dernier prix de marché

### Notifications
- **Démarrage/Arrêt** : Notifications Telegram
- **Trades Exécutés** : Détail de chaque transaction
- **Résumés Horaires** : Performance périodique
- **Alertes d'Erreur** : Problèmes techniques

## 💾 Sauvegarde et Analyse

### Format des Résultats

Les résultats sont sauvegardés au format JSON avec :

```json
{
  "simulation_info": {
    "strategy_name": "Grid Trading",
    "symbol": "BTC/USDT",
    "initial_capital": 1000,
    "start_time": "2024-01-01T10:00:00",
    "end_time": "2024-01-01T12:00:00"
  },
  "performance": {
    "total_return_percent": 2.5,
    "total_trades": 15,
    "win_rate_percent": 60.0,
    "total_pnl": 25.0
  },
  "trades": [
    {
      "timestamp": "2024-01-01T10:05:00",
      "side": "buy",
      "quantity": 0.01,
      "price": 50000,
      "fees": 0.5,
      "pnl": 0
    }
  ]
}
```

### Analyse des Résultats

```python
import json

# Charger les résultats
with open('results.json', 'r') as f:
    results = json.load(f)

# Analyser les performances
performance = results['performance']
print(f"Rendement: {performance['total_return_percent']:.2f}%")
print(f"Sharpe estimé: {performance['total_pnl'] / max(1, performance['volatility'])}") 

# Analyser les trades
trades = results['trades']
winning_trades = [t for t in trades if t['pnl'] > 0]
print(f"Trades gagnants: {len(winning_trades)}/{len(trades)}")
```

## ⚙️ Configuration

### Paramètres du Simulateur

```python
from paper_trading.simulator import PaperTradingSimulator

# Créer un simulateur personnalisé
simulator = PaperTradingSimulator(
    initial_capital=5000,
    symbol='ETH/USDT'
)

# Configurer les frais
simulator.trading_fee = 0.0005  # 0.05%
simulator.slippage = 0.0002     # 0.02%
simulator.update_interval = 30  # 30 secondes

# Configurer la volatilité simulée
simulator.price_volatility = 0.01  # 1%
simulator.trend_factor = 0.0002    # Légère tendance haussière
```

### Variables d'Environnement

```bash
# Dans .env.local
BINANCE_PROD_API_KEY=your_api_key
BINANCE_PROD_API_SECRET=your_api_secret
TELEGRAM_TOKEN=your_telegram_token
TELEGRAM_CHAT_ID=your_chat_id
```

## 🧪 Tests et Validation

### Exécuter les Exemples

```bash
# Exemples d'utilisation
python examples/paper_trading_example.py
```

### Tests Unitaires

```bash
# Tests du simulateur
python -m pytest tests/test_paper_trading.py -v
```

## 📝 Bonnes Pratiques

### 1. Validation Progressive
1. **Backtesting** : Tester sur données historiques
2. **Paper Trading** : Valider en temps réel
3. **Trading Réel** : Déployer avec petit capital

### 2. Paramètres Réalistes
- Utiliser les vrais frais de votre exchange
- Inclure le slippage réaliste
- Tester avec le capital que vous comptez utiliser

### 3. Durée de Test
- **Minimum** : 24-48 heures
- **Recommandé** : 1-2 semaines
- **Idéal** : 1 mois pour différentes conditions de marché

### 4. Analyse des Résultats
- Ne pas se fier qu'au rendement total
- Analyser la régularité des performances
- Vérifier le comportement dans différentes conditions

## 🔮 Évolutions Futures

- [ ] Interface web pour le monitoring
- [ ] Backtesting intégré dans les sessions
- [ ] Optimisation automatique des paramètres
- [ ] Alertes avancées (SMS, Discord, etc.)
- [ ] Support multi-exchange
- [ ] Analyse de corrélation entre stratégies

## 🆘 Dépannage

### Problèmes Courants

1. **Session ne démarre pas**
   - Vérifier que la stratégie existe
   - Contrôler les logs d'erreur

2. **Pas de trades exécutés**
   - Vérifier les conditions d'entrée de la stratégie
   - Augmenter la volatilité simulée

3. **Erreur données réelles**
   - Vérifier les clés API Binance
   - Tester d'abord avec données simulées

### Logs et Debug

```bash
# Logs détaillés
python run_paper_trading.py --create "Grid Trading" --verbose

# Consulter les logs
tail -f paper_trading.log
```

---

**💡 Conseil** : Le paper trading est essentiel pour valider vos stratégies. Prenez le temps de bien tester avant de passer au trading réel !
