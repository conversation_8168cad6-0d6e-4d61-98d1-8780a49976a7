# 📊 Système de Backtesting botCrypto

## 🎯 Vue d'ensemble

Le système de backtesting de botCrypto permet de tester et valider vos stratégies de trading sur des données historiques avant de risquer du capital réel. Il fournit des métriques de performance détaillées, des visualisations et des rapports complets.

## 🏗️ Architecture

```
backtesting/
├── __init__.py              # Module principal
├── data_provider.py         # Récupération de données historiques
├── engine.py               # Moteur de backtesting
├── metrics.py              # Calcul des métriques de performance
├── strategies.py           # Stratégies de trading prêtes à l'emploi
├── visualizer.py           # Génération de graphiques et rapports
└── README.md              # Cette documentation
```

## 🚀 Utilisation Rapide

### 1. Tester une stratégie unique

```bash
# Tester la stratégie Grid Trading sur BTC/USDT
python run_bot.py --backtest "Grid Trading" --symbol BTC/USDT --days 30 --capital 1000

# Tester avec des paramètres personnalisés
python run_bot.py --backtest "SMA Crossover" --symbol ETH/USDT --days 60 --capital 5000
```

### 2. Comparer toutes les stratégies

```bash
# Comparer toutes les stratégies disponibles
python run_bot.py --backtest-compare --symbol BTC/USDT --days 30 --capital 1000
```

### 3. Script dédié au backtesting

```bash
# Utiliser le script spécialisé
python run_backtest.py --strategy "Grid Trading" --symbol BTC/USDT --days 30

# Comparer toutes les stratégies
python run_backtest.py --compare --symbol ETH/USDT --days 60

# Lister les stratégies disponibles
python run_backtest.py --list
```

## 📈 Stratégies Disponibles

| Stratégie | Description | Paramètres Clés |
|-----------|-------------|-----------------|
| **Grid Trading** | Place des ordres à intervalles réguliers | Espacement 2%, Position max 5 |
| **SMA Crossover** | Croisement moyennes mobiles 20/50 | SMA courte/longue |
| **RSI Mean Reversion** | Achat survente, vente surachat | RSI < 30 / > 70 |
| **Bollinger Bands** | Trading sur les bandes (2σ) | Bandes de Bollinger |
| **MACD** | Signaux MACD/Signal | Croisements MACD |
| **Buy & Hold** | Stratégie de référence | Achat et conservation |

## 📊 Métriques de Performance

### Métriques de Rendement
- **Rendement Total** : Performance globale en %
- **Rendement Annualisé** : Performance extrapolée sur 1 an
- **Ratio de Sharpe** : Rendement ajusté du risque
- **Ratio de Sortino** : Sharpe avec downside deviation
- **Ratio de Calmar** : Rendement / Drawdown maximum

### Métriques de Risque
- **Volatilité** : Écart-type des rendements
- **Drawdown Maximum** : Plus grande perte depuis un pic
- **Value at Risk (VaR)** : Perte potentielle à 95%
- **Conditional VaR** : VaR conditionnel

### Métriques de Trading
- **Nombre de Trades** : Total des transactions
- **Taux de Réussite** : % de trades gagnants
- **Profit Factor** : Gains totaux / Pertes totales
- **Trade Moyen** : P&L moyen par trade

## 🔧 Utilisation Programmatique

### Exemple Simple

```python
from backtesting.engine import backtest_engine
from backtesting.data_provider import data_provider
from backtesting.strategies import trading_strategies

# Récupérer les données
data = data_provider.get_historical_data('BTC/USDT', '1h', 30)
data = data_provider.add_technical_indicators(data)

# Configurer le moteur
backtest_engine.initial_capital = 1000

# Récupérer une stratégie
strategies = trading_strategies.get_all_strategies()
strategy_func = strategies['Grid Trading']

# Exécuter le backtesting
result = backtest_engine.run_strategy(strategy_func, data, 'BTC/USDT', 'Grid Trading')

# Afficher les résultats
print(f"Rendement: {result['performance']['total_return_percent']:.2f}%")
```

### Stratégie Personnalisée

```python
def ma_custom_strategy(engine, data, index, symbol):
    """Stratégie personnalisée basée sur moyennes mobiles"""
    if index < 20:
        return
    
    # Calculer les moyennes mobiles
    ma_short = data.iloc[index-9:index+1]['close'].mean()  # MA 10
    ma_long = data.iloc[index-19:index+1]['close'].mean()  # MA 20
    
    current_price = data.iloc[index]['close']
    position = engine.positions.get(symbol, 0)
    
    # Signaux de trading
    if ma_short > ma_long and position == 0:
        # Signal d'achat
        quantity = (engine.current_capital * 0.8) / current_price
        engine.execute_trade(symbol, 'buy', quantity, current_price, 
                           data.index[index], 'CustomMA')
    
    elif ma_short < ma_long and position > 0:
        # Signal de vente
        engine.execute_trade(symbol, 'sell', position, current_price, 
                           data.index[index], 'CustomMA')

# Utiliser la stratégie personnalisée
result = backtest_engine.run_strategy(ma_custom_strategy, data, 'BTC/USDT', 'Custom MA')
```

## 📊 Visualisations et Rapports

### Graphiques Générés
- **Courbe d'Équité** : Évolution de la valeur du portefeuille
- **Analyse de Drawdown** : Périodes de pertes
- **Trades sur Prix** : Signaux d'achat/vente sur le graphique de prix
- **Comparaison de Stratégies** : Tableau comparatif

### Rapport HTML
Un rapport HTML complet est généré automatiquement avec :
- Résumé de performance
- Graphiques interactifs
- Métriques détaillées
- Analyse des coûts de trading

## ⚙️ Configuration Avancée

### Frais et Slippage

```python
# Configurer les frais de trading
backtest_engine.set_fees(
    trading_fee=0.001,  # 0.1% par trade
    slippage=0.0005     # 0.05% de slippage
)
```

### Indicateurs Techniques

Le système ajoute automatiquement ces indicateurs :
- Moyennes mobiles (SMA 20/50, EMA 12/26)
- MACD et signal
- RSI (14 périodes)
- Bandes de Bollinger (20, 2σ)
- Volatilité
- Ratios de volume

## 🧪 Tests et Validation

### Exécuter les Tests

```bash
# Tests unitaires du système de backtesting
python -m pytest tests/test_backtesting.py -v

# Tests avec couverture
python -m pytest tests/test_backtesting.py --cov=backtesting
```

### Exemples d'Utilisation

```bash
# Exécuter les exemples
python examples/backtest_example.py
```

## 📝 Bonnes Pratiques

### 1. Validation des Données
- Vérifiez la qualité des données historiques
- Utilisez suffisamment de données (minimum 30 jours)
- Attention aux gaps et données manquantes

### 2. Paramètres Réalistes
- Incluez les frais de trading réels
- Considérez le slippage
- Utilisez un capital réaliste

### 3. Analyse des Résultats
- Ne vous fiez pas qu'au rendement total
- Analysez le drawdown et la volatilité
- Vérifiez la robustesse sur différentes périodes

### 4. Éviter l'Overfitting
- Testez sur des périodes différentes
- Validez sur des données out-of-sample
- Gardez les stratégies simples

## 🔮 Développements Futurs

- [ ] Backtesting multi-assets
- [ ] Optimisation de paramètres
- [ ] Walk-forward analysis
- [ ] Monte Carlo simulations
- [ ] Backtesting sur données tick
- [ ] Intégration avec paper trading

## 🆘 Support et Dépannage

### Problèmes Courants

1. **Erreur de données** : Vérifiez votre connexion internet et les clés API
2. **Mémoire insuffisante** : Réduisez la période ou le timeframe
3. **Stratégie ne trade pas** : Vérifiez les conditions d'entrée

### Logs et Debug

```bash
# Mode verbose pour plus de détails
python run_backtest.py --strategy "Grid Trading" --verbose

# Consulter les logs
tail -f backtest.log
```

---

**💡 Conseil** : Commencez toujours par tester vos stratégies en backtesting avant de les déployer en mode paper trading, puis en trading réel.
