# 👥 Bot de Copy Trading

## 🎯 Vue d'ensemble

Bot automatisé pour analyser et répliquer les trades de wallets performants. Utilise l'intelligence artificielle pour identifier les meilleurs traders et copier leurs stratégies avec une gestion avancée des risques.

## 🏗️ Architecture

```
bots/copy_trading/
├── __init__.py                  # Module principal
├── wallet_analyzer.py           # Analyse des performances des wallets
├── signal_detector.py           # Détection de signaux de trading
├── copy_executor.py             # Exécution des copy trades
├── copy_trading_bot.py          # Bot principal intégré
└── README.md                    # Cette documentation
```

## 🚀 Fonctionnalités

### Analyse de Wallets
- **Tracking automatique** de wallets performants
- **Métriques avancées** : Win rate, P&L, Sharpe ratio, drawdown
- **Scoring de performance** multi-facteurs
- **Détection de patterns** de trading

### Détection de Signaux
- **Monitoring temps réel** des transactions
- **Filtrage intelligent** des opportunités
- **Scoring de confiance** et d'urgence
- **Contexte de marché** intégré

### Exécution Automatisée
- **Copy trading** avec position sizing adaptatif
- **Gestion des risques** intégrée
- **Stop-loss et take-profit** automatiques
- **Monitoring des positions** en temps réel

### Gestion des Risques
- **Position sizing** basé sur la performance
- **Limites de capital** par trade et quotidiennes
- **Diversification** automatique
- **Arrêt d'urgence** en cas de pertes importantes

## 📊 Métriques de Performance

### Analyse de Wallets

| Métrique | Description | Utilisation |
|----------|-------------|-------------|
| **Win Rate** | % de trades gagnants | Sélection des wallets |
| **Total P&L** | Profit/Perte total | Ranking des performances |
| **Sharpe Ratio** | Rendement ajusté du risque | Évaluation qualité |
| **Max Drawdown** | Perte maximale | Gestion des risques |
| **Consistency Score** | Régularité des gains | Fiabilité du trader |
| **Risk Score** | Niveau de risque | Filtrage sécuritaire |

### Signaux de Trading

```python
# Exemple de signal détecté
signal = {
    'wallet_address': '0x1234...',
    'action': 'buy',
    'token': 'WETH',
    'amount_usd': 5000,
    'confidence_score': 0.85,
    'urgency': 'high',
    'reasoning': [
        'Wallet très performant (78% win rate)',
        'Trade 2.3x plus gros que la moyenne',
        'Historique positif sur WETH'
    ]
}
```

## 🛠️ Installation et Configuration

### 1. Configuration de Base

```python
from bots.copy_trading.copy_trading_bot import CopyTradingBot, CopyTradingBotConfig

config = CopyTradingBotConfig(
    tracked_wallets=[
        {
            'address': '******************************************',
            'label': 'Whale Trader #1',
            'min_trade_size': 5000.0
        }
    ],
    default_copy_percentage=2.0,      # 2% du portefeuille
    max_copy_percentage=5.0,          # 5% maximum
    max_concurrent_copies=10,         # 10 positions max
    enable_stop_loss=True,
    default_stop_loss_percentage=10.0,
    enable_notifications=True
)

bot = CopyTradingBot(config)
await bot.start()
```

### 2. Configuration Avancée

```json
{
  "tracked_wallets": [
    {
      "address": "******************************************",
      "label": "DeFi Expert",
      "min_trade_size": 2000.0
    }
  ],
  "min_signal_confidence": 0.8,
  "required_urgency_levels": ["high"],
  "default_copy_percentage": 1.5,
  "max_copy_percentage": 3.0,
  "max_concurrent_copies": 8,
  "enable_stop_loss": true,
  "default_stop_loss_percentage": 8.0,
  "enable_take_profit": true,
  "default_take_profit_percentage": 15.0,
  "max_daily_copy_amount": 25000.0,
  "max_copies_per_wallet": 2,
  "analysis_interval_minutes": 60,
  "enable_notifications": true,
  "notification_min_profit": 100.0
}
```

## 🔍 Sélection des Wallets

### Critères de Qualité

1. **Performance Historique**
   - Win rate > 60%
   - P&L positif sur 30+ trades
   - Consistency score > 0.6

2. **Profil de Risque**
   - Risk score < 0.8
   - Max drawdown < 30%
   - Pas de trades extrêmes

3. **Activité Récente**
   - Trades dans les 7 derniers jours
   - Volume minimum par trade
   - Régularité des transactions

### Sources de Wallets

- **Leaderboards DEX** (Uniswap, SushiSwap)
- **Analyseurs on-chain** (Nansen, Dune Analytics)
- **Communautés crypto** (Twitter, Discord)
- **Plateformes de copy trading** existantes

## ⚡ Processus de Copy Trading

### 1. Détection de Signal

```mermaid
graph TD
    A[Transaction détectée] --> B[Vérification wallet]
    B --> C[Analyse du trade]
    C --> D[Calcul confiance]
    D --> E{Confiance > seuil?}
    E -->|Oui| F[Génération signal]
    E -->|Non| G[Ignorer]
    F --> H[Ajout à la queue]
```

### 2. Exécution du Copy

```mermaid
graph TD
    A[Signal reçu] --> B[Calcul position size]
    B --> C[Vérification limites]
    C --> D[Exécution trade]
    D --> E[Définition stop/take profit]
    E --> F[Monitoring position]
```

### 3. Gestion de Position

- **Monitoring continu** du P&L
- **Ajustement automatique** des stops
- **Fermeture** selon conditions prédéfinies
- **Reporting** des performances

## 💰 Position Sizing

### Calcul Adaptatif

```python
# Formule de position sizing
base_amount = portfolio_value * (default_copy_percentage / 100)

# Ajustements
performance_multiplier = (
    (win_rate / 100) * 0.4 +
    consistency_score * 0.3 +
    (1 - risk_score) * 0.3
)

confidence_multiplier = signal_confidence_score
urgency_multiplier = {'high': 1.2, 'medium': 1.0, 'low': 0.8}[urgency]

final_amount = (base_amount * 
               performance_multiplier * 
               confidence_multiplier * 
               urgency_multiplier)

# Application des limites
final_amount = min(final_amount, max_trade_amount)
final_amount = max(final_amount, min_trade_amount)
```

### Exemples de Sizing

| Wallet Performance | Signal Confidence | Urgency | Base 2% | Final Amount |
|-------------------|-------------------|---------|---------|--------------|
| **Excellent** (90% win) | 0.9 | High | $1,000 | $1,944 |
| **Bon** (70% win) | 0.8 | Medium | $1,000 | $1,280 |
| **Moyen** (60% win) | 0.7 | Low | $1,000 | $896 |

## 🛡️ Gestion des Risques

### Limites de Sécurité

```python
risk_limits = {
    'max_position_size': 5.0,         # % du portefeuille
    'max_concurrent_positions': 10,    # Nombre de positions
    'max_daily_copy_amount': 50000,    # USD par jour
    'max_copies_per_wallet': 3,        # Par wallet par jour
    'max_drawdown_stop': 15.0,         # % de perte totale
    'emergency_stop_loss': 20.0        # % de perte d'urgence
}
```

### Protection contre les Risques

- **Wash Trading Detection** : Filtrage des faux volumes
- **Rug Pull Protection** : Éviter les tokens suspects
- **Slippage Control** : Limites strictes d'exécution
- **Liquidity Check** : Vérification avant trade
- **Correlation Analysis** : Éviter la sur-concentration

## 📈 Métriques de Performance

### KPIs Principaux

```python
performance_metrics = {
    'total_copies_executed': 156,
    'successful_copies': 142,
    'success_rate': 91.0,              # %
    'total_copy_volume': 125000,       # USD
    'total_realized_pnl': 8750,        # USD
    'average_pnl_per_trade': 61.6,     # USD
    'best_copy_profit': 450,           # USD
    'worst_copy_loss': -120,           # USD
    'average_hold_time': 4.2,          # heures
    'sharpe_ratio': 2.1
}
```

### Analyse par Wallet

| Wallet | Copies | Win Rate | Avg P&L | Total P&L |
|--------|--------|----------|---------|-----------|
| **Whale #1** | 45 | 87% | $75 | $3,375 |
| **DeFi Expert** | 32 | 94% | $52 | $1,664 |
| **Smart Money** | 28 | 82% | $68 | $1,904 |

## 🔧 Configuration Optimale

### Configuration Conservative

```python
conservative_config = CopyTradingBotConfig(
    default_copy_percentage=1.0,      # 1% seulement
    max_copy_percentage=2.0,          # 2% maximum
    min_signal_confidence=0.85,       # Confiance élevée
    required_urgency_levels=['high'], # Signaux urgents seulement
    max_concurrent_copies=5,          # Peu de positions
    default_stop_loss_percentage=5.0  # Stop serré
)
```

### Configuration Agressive

```python
aggressive_config = CopyTradingBotConfig(
    default_copy_percentage=3.0,      # 3% par défaut
    max_copy_percentage=8.0,          # 8% maximum
    min_signal_confidence=0.65,       # Confiance plus basse
    required_urgency_levels=['high', 'medium', 'low'],
    max_concurrent_copies=15,         # Beaucoup de positions
    default_stop_loss_percentage=15.0 # Stop plus large
)
```

## 🧪 Tests et Validation

### Tests Unitaires

```bash
# Tester les composants
python -m pytest tests/test_wallet_analyzer.py
python -m pytest tests/test_signal_detector.py
python -m pytest tests/test_copy_executor.py
```

### Simulation

```bash
# Lancer l'exemple
python examples/copy_trading_example.py

# Test avec configuration custom
python examples/copy_trading_example.py --config config.json
```

### Backtesting

```python
from backtesting.backtest_engine import BacktestEngine

backtest = BacktestEngine()
results = await backtest.run_copy_trading_strategy(
    start_date="2024-01-01",
    end_date="2024-01-31",
    tracked_wallets=['0x1234...', '0x5678...'],
    initial_capital=10000,
    config=copy_config
)
```

## 📊 Monitoring et Alertes

### Dashboard Temps Réel

- **Wallets suivis** : Performance et activité
- **Signaux actifs** : Confiance et urgence
- **Positions ouvertes** : P&L et risque
- **Performance globale** : Métriques clés

### Notifications

```python
# Types d'alertes
notifications = {
    'new_high_confidence_signal': confidence > 0.9,
    'large_copy_executed': amount > $5000,
    'stop_loss_triggered': always,
    'take_profit_hit': always,
    'daily_performance_summary': end_of_day,
    'wallet_performance_change': significant_change,
    'risk_limit_approached': risk > 80%
}
```

## ⚠️ Risques et Limitations

### Risques Techniques

- **Latency Risk** : Délai entre signal et exécution
- **Slippage Risk** : Prix défavorable à l'exécution
- **Smart Contract Risk** : Bugs dans les protocoles
- **Front-running** : Autres bots plus rapides

### Risques de Marché

- **Correlation Risk** : Tous les wallets perdent ensemble
- **Liquidity Risk** : Manque de liquidité sur certains tokens
- **Volatility Risk** : Mouvements de prix extrêmes
- **Regulatory Risk** : Changements réglementaires

### Risques Comportementaux

- **Wash Trading** : Faux volumes pour tromper
- **Pump & Dump** : Manipulation de prix
- **Insider Trading** : Information privilégiée
- **Strategy Decay** : Performance qui se dégrade

## 📞 Support et Maintenance

### Monitoring

```bash
# Logs en temps réel
tail -f logs/copy_trading/copy_trading.log

# Métriques de performance
curl http://localhost:8080/api/copy-trading/metrics

# Statut des wallets suivis
curl http://localhost:8080/api/copy-trading/wallets
```

### Maintenance

- **Analyse des wallets** : Quotidienne
- **Nettoyage des signaux** : Toutes les heures
- **Mise à jour des performances** : Continue
- **Backup des données** : Hebdomadaire

---

**💡 Conseil** : Le copy trading nécessite une sélection rigoureuse des wallets et une surveillance constante. Commencez avec de petits montants et augmentez progressivement selon les performances.
