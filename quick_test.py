#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
⚡ Test Rapide - Lancement Express du Bot Optimisé
Script pour lancer rapidement des tests avec paramètres prédéfinis
"""

import os
import sys
import argparse
from pathlib import Path

# Ajouter le répertoire du projet au path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from bots.optimized_safe_bot import OptimizedSafeBot

def quick_test_1h():
    """Test rapide de 1 heure"""
    print("⚡ LANCEMENT TEST RAPIDE - 1 HEURE")
    print("=" * 50)
    print("🎯 Objectif: Test rapide de la stratégie")
    print("⏱️ Durée: 1 heure")
    print("💰 Mode: Testnet (argent fictif)")
    print("🔧 Optimisation: Activée")
    
    try:
        bot = OptimizedSafeBot(mode="testnet")
        bot.run_optimization_test(1)
    except KeyboardInterrupt:
        print("\n🛑 Test interrompu")
    except Exception as e:
        print(f"❌ Erreur: {e}")

def quick_test_6h():
    """Test moyen de 6 heures"""
    print("🕕 LANCEMENT TEST MOYEN - 6 HEURES")
    print("=" * 50)
    print("🎯 Objectif: Test approfondi de la stratégie")
    print("⏱️ Durée: 6 heures")
    print("💰 Mode: Testnet (argent fictif)")
    print("🔧 Optimisation: Activée toutes les heures")
    
    try:
        bot = OptimizedSafeBot(mode="testnet")
        bot.run_optimization_test(6)
    except KeyboardInterrupt:
        print("\n🛑 Test interrompu")
    except Exception as e:
        print(f"❌ Erreur: {e}")

def quick_test_24h():
    """Test long de 24 heures"""
    print("📅 LANCEMENT TEST LONG - 24 HEURES")
    print("=" * 50)
    print("🎯 Objectif: Test complet sur une journée")
    print("⏱️ Durée: 24 heures")
    print("💰 Mode: Testnet (argent fictif)")
    print("🔧 Optimisation: Activée toutes les heures")
    print("📊 Métriques: Collecte complète")
    
    confirm = input("\n🚀 Lancer le test de 24h ? (oui/non): ").lower()
    if confirm not in ['oui', 'o', 'yes', 'y']:
        print("❌ Test annulé")
        return
    
    try:
        bot = OptimizedSafeBot(mode="testnet")
        bot.run_optimization_test(24)
    except KeyboardInterrupt:
        print("\n🛑 Test interrompu")
    except Exception as e:
        print(f"❌ Erreur: {e}")

def quick_test_week():
    """Test très long d'une semaine"""
    print("📆 LANCEMENT TEST TRÈS LONG - 1 SEMAINE")
    print("=" * 50)
    print("🎯 Objectif: Test complet sur une semaine")
    print("⏱️ Durée: 168 heures (7 jours)")
    print("💰 Mode: Testnet (argent fictif)")
    print("🔧 Optimisation: Activée toutes les heures")
    print("📊 Métriques: Collecte complète avec rotation des logs")
    print("⚠️ ATTENTION: Test très long, assurez-vous de la stabilité")
    
    confirm = input("\n🚀 Lancer le test d'une semaine ? (oui/non): ").lower()
    if confirm not in ['oui', 'o', 'yes', 'y']:
        print("❌ Test annulé")
        return
    
    try:
        bot = OptimizedSafeBot(mode="testnet")
        bot.run_optimization_test(168)
    except KeyboardInterrupt:
        print("\n🛑 Test interrompu")
    except Exception as e:
        print(f"❌ Erreur: {e}")

def production_deploy():
    """Déploiement rapide en production"""
    print("🚀 DÉPLOIEMENT RAPIDE EN PRODUCTION")
    print("=" * 50)
    
    # Recherche de la meilleure configuration
    config_files = list(Path(".").glob("optimal_config_testnet_*.json"))
    
    if not config_files:
        print("❌ Aucune configuration testnet trouvée")
        print("💡 Lancez d'abord un test avec: python quick_test.py --test-1h")
        return
    
    # Trouver la meilleure configuration
    best_config = None
    best_pnl = -float('inf')
    
    for config_file in config_files:
        try:
            import json
            with open(config_file, 'r') as f:
                config_data = json.load(f)
            
            metrics = config_data.get('performance_metrics', {})
            pnl = metrics.get('pnl_percentage', -100)
            
            if pnl > best_pnl:
                best_pnl = pnl
                best_config = config_file
                
        except Exception:
            continue
    
    if not best_config or best_pnl <= 0:
        print(f"❌ Aucune configuration rentable trouvée (meilleur P&L: {best_pnl:.2f}%)")
        print("💡 Lancez plus de tests pour obtenir une stratégie rentable")
        return
    
    print(f"✅ Meilleure configuration trouvée: {best_config.name}")
    print(f"📊 P&L: {best_pnl:+.2f}%")
    
    print("\n⚠️ ATTENTION: DÉPLOIEMENT EN PRODUCTION")
    print("💰 Trading avec de l'argent réel")
    print("🛡️ Capital limité pour sécurité")
    
    confirm = input("\n🚀 Confirmer le déploiement ? (oui/non): ").lower()
    if confirm not in ['oui', 'o', 'yes', 'y']:
        print("❌ Déploiement annulé")
        return
    
    try:
        bot = OptimizedSafeBot(mode="production")
        bot.replicate_to_production(str(best_config))
    except Exception as e:
        print(f"❌ Erreur déploiement: {e}")

def show_status():
    """Affiche le statut rapide"""
    print("📊 STATUT RAPIDE")
    print("=" * 30)
    
    # Vérifier les logs actifs
    log_files = list(Path(".").glob("optimized_safe_bot_*.log"))
    config_files = list(Path(".").glob("optimal_config_*.json"))
    
    print(f"📁 Logs disponibles: {len(log_files)}")
    print(f"📁 Configurations: {len(config_files)}")
    
    if config_files:
        print("\n🏆 Meilleures performances:")
        
        configs_with_pnl = []
        for config_file in config_files:
            try:
                import json
                with open(config_file, 'r') as f:
                    config_data = json.load(f)
                
                metrics = config_data.get('performance_metrics', {})
                pnl = metrics.get('pnl_percentage', 0)
                trades = metrics.get('total_trades', 0)
                win_rate = metrics.get('win_rate', 0)
                
                configs_with_pnl.append({
                    'file': config_file.name,
                    'pnl': pnl,
                    'trades': trades,
                    'win_rate': win_rate
                })
                
            except Exception:
                continue
        
        # Trier par P&L
        configs_with_pnl.sort(key=lambda x: x['pnl'], reverse=True)
        
        for i, config in enumerate(configs_with_pnl[:3], 1):
            print(f"{i}. {config['file']}")
            print(f"   📊 P&L: {config['pnl']:+.2f}% | Trades: {config['trades']} | Win: {config['win_rate']:.1f}%")
    
    # Vérifier si un bot est en cours
    import psutil
    bot_running = False
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if 'optimized_safe_bot.py' in ' '.join(proc.info['cmdline'] or []):
                bot_running = True
                print(f"\n🤖 Bot en cours d'exécution (PID: {proc.info['pid']})")
                break
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    if not bot_running:
        print("\n💤 Aucun bot en cours d'exécution")

def main():
    """Fonction principale"""
    parser = argparse.ArgumentParser(description="Test Rapide du Bot Optimisé")
    parser.add_argument("--test-1h", action="store_true", help="Test rapide de 1 heure")
    parser.add_argument("--test-6h", action="store_true", help="Test moyen de 6 heures")
    parser.add_argument("--test-24h", action="store_true", help="Test long de 24 heures")
    parser.add_argument("--test-week", action="store_true", help="Test très long d'une semaine")
    parser.add_argument("--deploy", action="store_true", help="Déploiement rapide en production")
    parser.add_argument("--status", action="store_true", help="Afficher le statut")
    
    args = parser.parse_args()
    
    if args.test_1h:
        quick_test_1h()
    elif args.test_6h:
        quick_test_6h()
    elif args.test_24h:
        quick_test_24h()
    elif args.test_week:
        quick_test_week()
    elif args.deploy:
        production_deploy()
    elif args.status:
        show_status()
    else:
        # Menu interactif si aucun argument
        print("⚡ TEST RAPIDE - BOT OPTIMISÉ")
        print("=" * 40)
        print("1. 🕐 Test rapide (1h)")
        print("2. 🕕 Test moyen (6h)")
        print("3. 📅 Test long (24h)")
        print("4. 📆 Test très long (1 semaine)")
        print("5. 🚀 Déployer en production")
        print("6. 📊 Voir le statut")
        print("0. 🚪 Quitter")
        
        choice = input("\nVotre choix: ").strip()
        
        if choice == '1':
            quick_test_1h()
        elif choice == '2':
            quick_test_6h()
        elif choice == '3':
            quick_test_24h()
        elif choice == '4':
            quick_test_week()
        elif choice == '5':
            production_deploy()
        elif choice == '6':
            show_status()
        elif choice == '0':
            print("👋 Au revoir!")
        else:
            print("❌ Choix invalide")

if __name__ == "__main__":
    main()
