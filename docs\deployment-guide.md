# 🔧 Guide de Déploiement

## 🎯 Vue d'ensemble

Ce guide couvre tous les aspects du déploiement en production : de l'environnement local au cloud, avec les meilleures pratiques de sécurité et de monitoring.

## 🏠 Déploiement Local

### Configuration de Production

```bash
# Créer l'environnement de production
mkdir -p /opt/botcrypto
cd /opt/botcrypto

# Cloner et configurer
git clone https://github.com/JeremieN6/botCrypto.git .
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# Configuration sécurisée
cp config/config.example.json config/config.prod.json
chmod 600 config/config.prod.json
```

### Variables d'Environnement

```bash
# Créer /opt/botcrypto/.env.prod
cat > .env.prod << 'EOF'
# Environnement
ENVIRONMENT=production
DEBUG=false

# Blockchain
WEB3_PROVIDER_URL=https://mainnet.infura.io/v3/YOUR_INFURA_KEY
WEB3_PROVIDER_BACKUP=https://eth-mainnet.alchemyapi.io/v2/YOUR_ALCHEMY_KEY
PRIVATE_KEY=your_encrypted_private_key

# Base de données
DATABASE_URL=postgresql://botcrypto:secure_password@localhost:5432/botcrypto_prod

# Redis (cache)
REDIS_URL=redis://localhost:6379/0

# Monitoring
SENTRY_DSN=https://<EMAIL>/project_id
PROMETHEUS_PORT=9090

# Notifications
TELEGRAM_BOT_TOKEN=your_production_bot_token
TELEGRAM_CHAT_ID=your_production_chat_id
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/...

# Sécurité
JWT_SECRET=your_very_secure_jwt_secret
API_KEY=your_secure_api_key
ENCRYPTION_KEY=your_32_byte_encryption_key
EOF

chmod 600 .env.prod
```

### Service Systemd

```bash
# Créer /etc/systemd/system/botcrypto.service
sudo tee /etc/systemd/system/botcrypto.service << 'EOF'
[Unit]
Description=BotCrypto Trading System
After=network.target postgresql.service redis.service
Wants=postgresql.service redis.service

[Service]
Type=simple
User=botcrypto
Group=botcrypto
WorkingDirectory=/opt/botcrypto
Environment=PATH=/opt/botcrypto/venv/bin
EnvironmentFile=/opt/botcrypto/.env.prod
ExecStart=/opt/botcrypto/venv/bin/python dashboard/dashboard_manager.py
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=botcrypto

# Sécurité
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/botcrypto/logs /opt/botcrypto/data

[Install]
WantedBy=multi-user.target
EOF

# Activer et démarrer
sudo systemctl daemon-reload
sudo systemctl enable botcrypto
sudo systemctl start botcrypto
```

## 🐳 Déploiement Docker

### Dockerfile

```dockerfile
# Créer Dockerfile
FROM python:3.10-slim

# Métadonnées
LABEL maintainer="<EMAIL>"
LABEL version="1.0.0"
LABEL description="BotCrypto Trading System"

# Variables d'environnement
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV ENVIRONMENT=production

# Utilisateur non-root
RUN groupadd -r botcrypto && useradd -r -g botcrypto botcrypto

# Dépendances système
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libffi-dev \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*

# Répertoire de travail
WORKDIR /app

# Copier les requirements
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copier le code
COPY . .

# Permissions
RUN chown -R botcrypto:botcrypto /app
USER botcrypto

# Volumes
VOLUME ["/app/logs", "/app/data", "/app/config"]

# Ports
EXPOSE 8080 9090

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/api/health || exit 1

# Commande par défaut
CMD ["python", "dashboard/dashboard_manager.py"]
```

### Docker Compose

```yaml
# Créer docker-compose.prod.yml
version: '3.8'

services:
  botcrypto:
    build: .
    container_name: botcrypto
    restart: unless-stopped
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=postgresql://botcrypto:${DB_PASSWORD}@postgres:5432/botcrypto
      - REDIS_URL=redis://redis:6379/0
    env_file:
      - .env.prod
    ports:
      - "8080:8080"
      - "9090:9090"
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
      - ./config:/app/config
    depends_on:
      - postgres
      - redis
    networks:
      - botcrypto-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  postgres:
    image: postgres:14-alpine
    container_name: botcrypto-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: botcrypto
      POSTGRES_USER: botcrypto
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - botcrypto-network

  redis:
    image: redis:7-alpine
    container_name: botcrypto-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    networks:
      - botcrypto-network

  nginx:
    image: nginx:alpine
    container_name: botcrypto-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - botcrypto
    networks:
      - botcrypto-network

  prometheus:
    image: prom/prometheus:latest
    container_name: botcrypto-prometheus
    restart: unless-stopped
    ports:
      - "9091:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - botcrypto-network

  grafana:
    image: grafana/grafana:latest
    container_name: botcrypto-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana:/etc/grafana/provisioning
    networks:
      - botcrypto-network

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  botcrypto-network:
    driver: bridge
```

### Déploiement Docker

```bash
# Construire et déployer
docker-compose -f docker-compose.prod.yml up -d

# Vérifier les logs
docker-compose -f docker-compose.prod.yml logs -f botcrypto

# Mise à jour
docker-compose -f docker-compose.prod.yml pull
docker-compose -f docker-compose.prod.yml up -d --force-recreate
```

## ☁️ Déploiement Cloud

### AWS ECS

```yaml
# task-definition.json
{
  "family": "botcrypto",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "1024",
  "memory": "2048",
  "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
  "taskRoleArn": "arn:aws:iam::account:role/ecsTaskRole",
  "containerDefinitions": [
    {
      "name": "botcrypto",
      "image": "your-account.dkr.ecr.region.amazonaws.com/botcrypto:latest",
      "portMappings": [
        {
          "containerPort": 8080,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "ENVIRONMENT",
          "value": "production"
        }
      ],
      "secrets": [
        {
          "name": "DATABASE_URL",
          "valueFrom": "arn:aws:secretsmanager:region:account:secret:botcrypto/database-url"
        },
        {
          "name": "PRIVATE_KEY",
          "valueFrom": "arn:aws:secretsmanager:region:account:secret:botcrypto/private-key"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/botcrypto",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "ecs"
        }
      },
      "healthCheck": {
        "command": ["CMD-SHELL", "curl -f http://localhost:8080/api/health || exit 1"],
        "interval": 30,
        "timeout": 5,
        "retries": 3
      }
    }
  ]
}
```

### Kubernetes

```yaml
# k8s/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: botcrypto

---
# k8s/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: botcrypto-config
  namespace: botcrypto
data:
  config.json: |
    {
      "trading": {
        "max_position_size_pct": 10.0,
        "max_daily_loss_pct": 5.0
      },
      "monitoring": {
        "dashboard_port": 8080,
        "enable_notifications": true
      }
    }

---
# k8s/secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: botcrypto-secrets
  namespace: botcrypto
type: Opaque
data:
  database-url: <base64-encoded-database-url>
  private-key: <base64-encoded-private-key>
  telegram-token: <base64-encoded-telegram-token>

---
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: botcrypto
  namespace: botcrypto
spec:
  replicas: 2
  selector:
    matchLabels:
      app: botcrypto
  template:
    metadata:
      labels:
        app: botcrypto
    spec:
      containers:
      - name: botcrypto
        image: botcrypto:latest
        ports:
        - containerPort: 8080
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: botcrypto-secrets
              key: database-url
        - name: PRIVATE_KEY
          valueFrom:
            secretKeyRef:
              name: botcrypto-secrets
              key: private-key
        volumeMounts:
        - name: config
          mountPath: /app/config
        - name: logs
          mountPath: /app/logs
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /api/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /api/health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 10
      volumes:
      - name: config
        configMap:
          name: botcrypto-config
      - name: logs
        emptyDir: {}

---
# k8s/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: botcrypto-service
  namespace: botcrypto
spec:
  selector:
    app: botcrypto
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: LoadBalancer

---
# k8s/hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: botcrypto-hpa
  namespace: botcrypto
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: botcrypto
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

## 🔒 Sécurité en Production

### 1. Chiffrement des Clés

```python
# utils/encryption.py
from cryptography.fernet import Fernet
import os

def encrypt_private_key(private_key: str, encryption_key: bytes) -> str:
    """Chiffre une clé privée"""
    f = Fernet(encryption_key)
    encrypted_key = f.encrypt(private_key.encode())
    return encrypted_key.decode()

def decrypt_private_key(encrypted_key: str, encryption_key: bytes) -> str:
    """Déchiffre une clé privée"""
    f = Fernet(encryption_key)
    decrypted_key = f.decrypt(encrypted_key.encode())
    return decrypted_key.decode()

# Utilisation
encryption_key = os.environ.get('ENCRYPTION_KEY').encode()
private_key = decrypt_private_key(encrypted_private_key, encryption_key)
```

### 2. Configuration Nginx

```nginx
# nginx/nginx.conf
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    # Sécurité
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req zone=api burst=20 nodelay;

    location / {
        proxy_pass http://botcrypto:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    location /api/ {
        proxy_pass http://botcrypto:8080/api/;
        
        # API rate limiting
        limit_req zone=api burst=10 nodelay;
        
        # CORS
        add_header Access-Control-Allow-Origin "https://your-domain.com";
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
        add_header Access-Control-Allow-Headers "Authorization, Content-Type";
    }
}
```

## 📊 Monitoring en Production

### Configuration Prometheus

```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

scrape_configs:
  - job_name: 'botcrypto'
    static_configs:
      - targets: ['botcrypto:9090']
    metrics_path: /metrics
    scrape_interval: 10s

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

### Alertes Prometheus

```yaml
# monitoring/alert_rules.yml
groups:
- name: botcrypto
  rules:
  - alert: BotCryptoDown
    expr: up{job="botcrypto"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "BotCrypto is down"
      description: "BotCrypto has been down for more than 1 minute."

  - alert: HighErrorRate
    expr: rate(botcrypto_errors_total[5m]) > 0.1
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "High error rate detected"
      description: "Error rate is {{ $value }} errors per second."

  - alert: LowProfitability
    expr: botcrypto_daily_pnl < -1000
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "Daily loss exceeds threshold"
      description: "Daily P&L is {{ $value }} USD."
```

## 🚀 CI/CD Pipeline

### GitHub Actions

```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]
  release:
    types: [published]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
    
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install pytest pytest-cov
    
    - name: Run tests
      run: |
        python tests/run_tests.py --all
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3

  build:
    needs: test
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Build Docker image
      run: |
        docker build -t botcrypto:${{ github.sha }} .
        docker tag botcrypto:${{ github.sha }} botcrypto:latest
    
    - name: Push to registry
      run: |
        echo ${{ secrets.DOCKER_PASSWORD }} | docker login -u ${{ secrets.DOCKER_USERNAME }} --password-stdin
        docker push botcrypto:${{ github.sha }}
        docker push botcrypto:latest

  deploy:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
    - name: Deploy to production
      run: |
        # Déploiement selon votre infrastructure
        # AWS ECS, Kubernetes, etc.
        echo "Deploying to production..."
```

---

**🎯 Résultat :** Avec ce guide, vous avez toutes les informations pour déployer BotCrypto en production de manière sécurisée et scalable.
