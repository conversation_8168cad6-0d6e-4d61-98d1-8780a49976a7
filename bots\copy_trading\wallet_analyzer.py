"""
🔍 Analyseur de wallets pour copy trading
Analyse des performances et patterns de trading des wallets suivis
"""

import asyncio
import time
import json
from decimal import Decimal, ROUND_DOWN
from typing import Dict, List, Optional, Tuple, Any, Set
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import defaultdict, deque
import numpy as np
import logging
from pathlib import Path
import sys

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent.parent))

from utils.error_handler import error_handler, ErrorCategory, ErrorSeverity
from logging_system.central_logger import central_logger, LogCategory

@dataclass
class WalletTransaction:
    """Transaction d'un wallet"""
    tx_hash: str
    timestamp: datetime
    wallet_address: str
    token_in: str
    token_out: str
    amount_in: float
    amount_out: float
    gas_used: int
    gas_price: int
    dex: str
    action: str  # 'buy', 'sell', 'swap'
    usd_value: float
    profit_loss: Optional[float] = None

@dataclass
class WalletPerformance:
    """Métriques de performance d'un wallet"""
    wallet_address: str
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    total_pnl: float
    total_volume: float
    average_trade_size: float
    largest_win: float
    largest_loss: float
    max_drawdown: float
    sharpe_ratio: float
    profit_factor: float
    average_hold_time: float  # en heures
    last_activity: datetime
    risk_score: float  # 0-1, 1 = très risqué
    consistency_score: float  # 0-1, 1 = très consistant
    
    # Métriques par token
    favorite_tokens: Dict[str, int]  # token -> nombre de trades
    token_performance: Dict[str, float]  # token -> pnl
    
    # Patterns de trading
    trading_hours: Dict[int, int]  # heure -> nombre de trades
    trading_days: Dict[str, int]  # jour -> nombre de trades
    average_trades_per_day: float

@dataclass
class WalletSignal:
    """Signal de trading d'un wallet"""
    wallet_address: str
    timestamp: datetime
    action: str  # 'buy', 'sell'
    token_symbol: str
    token_address: str
    amount_usd: float
    confidence_score: float  # 0-1
    urgency: str  # 'low', 'medium', 'high'
    reasoning: List[str]  # Raisons du signal
    
    # Contexte du trade
    wallet_performance: WalletPerformance
    recent_trades: List[WalletTransaction]
    market_context: Dict[str, Any]

class WalletAnalyzer:
    """Analyseur de wallets pour copy trading"""
    
    def __init__(self, web3_provider: str = None):
        self.logger = logging.getLogger(__name__)
        
        # Configuration
        self.web3_provider = web3_provider
        
        # Wallets suivis
        self.tracked_wallets: Dict[str, Dict[str, Any]] = {}
        self.wallet_performances: Dict[str, WalletPerformance] = {}
        
        # Historique des transactions
        self.transaction_history: Dict[str, List[WalletTransaction]] = defaultdict(list)
        self.max_history_per_wallet = 1000
        
        # Cache des données
        self.token_prices_cache: Dict[str, float] = {}
        self.cache_ttl = 300  # 5 minutes
        self.last_cache_update = 0
        
        # Signaux générés
        self.active_signals: List[WalletSignal] = []
        self.signal_history: deque = deque(maxlen=10000)
        
        # Statistiques
        self.total_wallets_analyzed = 0
        self.total_signals_generated = 0
        self.successful_signals = 0
        
        central_logger.log(
            level="INFO",
            message="Analyseur de wallets initialisé",
            category=LogCategory.STRATEGY
        )
    
    def add_wallet_to_track(self, wallet_address: str, label: str = "", 
                           min_trade_size: float = 100.0, 
                           performance_threshold: float = 0.6) -> bool:
        """Ajoute un wallet à suivre"""
        try:
            wallet_address = wallet_address.lower()
            
            # Vérifier si déjà suivi
            if wallet_address in self.tracked_wallets:
                self.logger.warning(f"Wallet {wallet_address} déjà suivi")
                return False
            
            # Ajouter à la liste
            self.tracked_wallets[wallet_address] = {
                'label': label,
                'added_date': datetime.now(),
                'min_trade_size': min_trade_size,
                'performance_threshold': performance_threshold,
                'is_active': True,
                'last_analyzed': None
            }
            
            central_logger.log(
                level="INFO",
                message=f"Wallet ajouté au suivi: {label or wallet_address}",
                category=LogCategory.STRATEGY,
                wallet_address=wallet_address,
                label=label
            )
            
            return True
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.MEDIUM, {
                'function': 'add_wallet_to_track',
                'wallet_address': wallet_address
            })
            return False
    
    def remove_wallet_from_tracking(self, wallet_address: str) -> bool:
        """Retire un wallet du suivi"""
        try:
            wallet_address = wallet_address.lower()
            
            if wallet_address in self.tracked_wallets:
                del self.tracked_wallets[wallet_address]
                
                # Nettoyer les données associées
                if wallet_address in self.wallet_performances:
                    del self.wallet_performances[wallet_address]
                
                if wallet_address in self.transaction_history:
                    del self.transaction_history[wallet_address]
                
                central_logger.log(
                    level="INFO",
                    message=f"Wallet retiré du suivi: {wallet_address}",
                    category=LogCategory.STRATEGY,
                    wallet_address=wallet_address
                )
                
                return True
            
            return False
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.MEDIUM, {
                'function': 'remove_wallet_from_tracking',
                'wallet_address': wallet_address
            })
            return False
    
    async def analyze_wallet_transactions(self, wallet_address: str, 
                                        days_back: int = 30) -> Optional[WalletPerformance]:
        """Analyse les transactions d'un wallet"""
        try:
            wallet_address = wallet_address.lower()
            
            # Récupérer les transactions (simulation)
            transactions = await self._fetch_wallet_transactions(wallet_address, days_back)
            
            if not transactions:
                return None
            
            # Stocker dans l'historique
            self.transaction_history[wallet_address] = transactions
            
            # Calculer les métriques de performance
            performance = self._calculate_wallet_performance(wallet_address, transactions)
            
            # Stocker les performances
            self.wallet_performances[wallet_address] = performance
            
            # Mettre à jour la date d'analyse
            if wallet_address in self.tracked_wallets:
                self.tracked_wallets[wallet_address]['last_analyzed'] = datetime.now()
            
            self.total_wallets_analyzed += 1
            
            central_logger.log(
                level="INFO",
                message=f"Analyse wallet terminée: {wallet_address}",
                category=LogCategory.STRATEGY,
                wallet_address=wallet_address,
                total_trades=performance.total_trades,
                win_rate=performance.win_rate,
                total_pnl=performance.total_pnl
            )
            
            return performance
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.DATA, ErrorSeverity.MEDIUM, {
                'function': 'analyze_wallet_transactions',
                'wallet_address': wallet_address
            })
            return None
    
    async def _fetch_wallet_transactions(self, wallet_address: str, 
                                       days_back: int) -> List[WalletTransaction]:
        """Récupère les transactions d'un wallet (simulation)"""
        try:
            # En production, utiliser des APIs comme Etherscan, Moralis, etc.
            # Ici on simule des données réalistes
            
            transactions = []
            start_date = datetime.now() - timedelta(days=days_back)
            
            # Simuler 20-100 transactions selon l'activité
            num_transactions = np.random.randint(20, 101)
            
            tokens = ['WETH', 'USDC', 'USDT', 'WBTC', 'LINK', 'UNI', 'AAVE', 'COMP']
            dexes = ['uniswap_v2', 'uniswap_v3', 'sushiswap', '1inch']
            
            for i in range(num_transactions):
                # Timestamp aléatoire dans la période
                random_seconds = np.random.randint(0, int(days_back * 24 * 3600))
                tx_time = start_date + timedelta(seconds=random_seconds)
                
                # Tokens aléatoires
                token_in = np.random.choice(tokens)
                token_out = np.random.choice([t for t in tokens if t != token_in])
                
                # Montants réalistes
                amount_in = np.random.uniform(0.1, 50.0)  # ETH ou équivalent
                
                # Simuler un prix avec spread
                base_rate = np.random.uniform(0.95, 1.05)
                amount_out = amount_in * base_rate
                
                # Valeur USD (simulation)
                usd_value = amount_in * np.random.uniform(1500, 4000)  # Prix ETH simulé
                
                transaction = WalletTransaction(
                    tx_hash=f"0x{''.join(np.random.choice('0123456789abcdef') for _ in range(64))}",
                    timestamp=tx_time,
                    wallet_address=wallet_address,
                    token_in=token_in,
                    token_out=token_out,
                    amount_in=amount_in,
                    amount_out=amount_out,
                    gas_used=np.random.randint(100000, 300000),
                    gas_price=np.random.randint(20, 100) * 10**9,  # gwei
                    dex=np.random.choice(dexes),
                    action='buy' if token_out in ['WETH', 'WBTC'] else 'sell',
                    usd_value=usd_value
                )
                
                transactions.append(transaction)
            
            # Trier par timestamp
            transactions.sort(key=lambda x: x.timestamp)
            
            # Calculer les P&L (simulation)
            self._calculate_transaction_pnl(transactions)
            
            return transactions
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.DATA, ErrorSeverity.MEDIUM, {
                'function': '_fetch_wallet_transactions',
                'wallet_address': wallet_address
            })
            return []
    
    def _calculate_transaction_pnl(self, transactions: List[WalletTransaction]):
        """Calcule le P&L pour chaque transaction (simulation)"""
        try:
            # Simuler des P&L réalistes
            for tx in transactions:
                # 60% de trades gagnants, 40% perdants
                is_winning = np.random.random() < 0.6
                
                if is_winning:
                    # Gain entre 1% et 20%
                    pnl_pct = np.random.uniform(0.01, 0.20)
                else:
                    # Perte entre -1% et -15%
                    pnl_pct = np.random.uniform(-0.15, -0.01)
                
                tx.profit_loss = tx.usd_value * pnl_pct
                
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.DATA, ErrorSeverity.LOW, {
                'function': '_calculate_transaction_pnl'
            })
    
    def _calculate_wallet_performance(self, wallet_address: str, 
                                    transactions: List[WalletTransaction]) -> WalletPerformance:
        """Calcule les métriques de performance d'un wallet"""
        try:
            if not transactions:
                return self._empty_performance(wallet_address)
            
            # Métriques de base
            total_trades = len(transactions)
            winning_trades = len([tx for tx in transactions if tx.profit_loss and tx.profit_loss > 0])
            losing_trades = len([tx for tx in transactions if tx.profit_loss and tx.profit_loss < 0])
            
            win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0
            
            # P&L total
            total_pnl = sum(tx.profit_loss for tx in transactions if tx.profit_loss)
            total_volume = sum(tx.usd_value for tx in transactions)
            average_trade_size = total_volume / total_trades if total_trades > 0 else 0
            
            # Plus gros gain/perte
            pnl_values = [tx.profit_loss for tx in transactions if tx.profit_loss]
            largest_win = max(pnl_values) if pnl_values else 0
            largest_loss = min(pnl_values) if pnl_values else 0
            
            # Calcul du drawdown
            max_drawdown = self._calculate_max_drawdown(transactions)
            
            # Sharpe ratio (simplifié)
            returns = [tx.profit_loss / tx.usd_value for tx in transactions if tx.profit_loss and tx.usd_value > 0]
            sharpe_ratio = self._calculate_sharpe_ratio(returns)
            
            # Profit factor
            gross_profit = sum(pnl for pnl in pnl_values if pnl > 0)
            gross_loss = abs(sum(pnl for pnl in pnl_values if pnl < 0))
            profit_factor = gross_profit / gross_loss if gross_loss > 0 else 0
            
            # Temps de détention moyen
            average_hold_time = self._calculate_average_hold_time(transactions)
            
            # Dernière activité
            last_activity = max(tx.timestamp for tx in transactions)
            
            # Scores de risque et consistance
            risk_score = self._calculate_risk_score(transactions)
            consistency_score = self._calculate_consistency_score(transactions)
            
            # Analyse des tokens favoris
            favorite_tokens = defaultdict(int)
            token_performance = defaultdict(float)
            
            for tx in transactions:
                favorite_tokens[tx.token_out] += 1
                if tx.profit_loss:
                    token_performance[tx.token_out] += tx.profit_loss
            
            # Patterns de trading
            trading_hours = defaultdict(int)
            trading_days = defaultdict(int)
            
            for tx in transactions:
                trading_hours[tx.timestamp.hour] += 1
                trading_days[tx.timestamp.strftime('%A')] += 1
            
            # Trades par jour
            days_span = (last_activity - transactions[0].timestamp).days or 1
            average_trades_per_day = total_trades / days_span
            
            return WalletPerformance(
                wallet_address=wallet_address,
                total_trades=total_trades,
                winning_trades=winning_trades,
                losing_trades=losing_trades,
                win_rate=win_rate,
                total_pnl=total_pnl,
                total_volume=total_volume,
                average_trade_size=average_trade_size,
                largest_win=largest_win,
                largest_loss=largest_loss,
                max_drawdown=max_drawdown,
                sharpe_ratio=sharpe_ratio,
                profit_factor=profit_factor,
                average_hold_time=average_hold_time,
                last_activity=last_activity,
                risk_score=risk_score,
                consistency_score=consistency_score,
                favorite_tokens=dict(favorite_tokens),
                token_performance=dict(token_performance),
                trading_hours=dict(trading_hours),
                trading_days=dict(trading_days),
                average_trades_per_day=average_trades_per_day
            )
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.DATA, ErrorSeverity.MEDIUM, {
                'function': '_calculate_wallet_performance',
                'wallet_address': wallet_address
            })
            return self._empty_performance(wallet_address)
    
    def _empty_performance(self, wallet_address: str) -> WalletPerformance:
        """Retourne une performance vide"""
        return WalletPerformance(
            wallet_address=wallet_address,
            total_trades=0,
            winning_trades=0,
            losing_trades=0,
            win_rate=0,
            total_pnl=0,
            total_volume=0,
            average_trade_size=0,
            largest_win=0,
            largest_loss=0,
            max_drawdown=0,
            sharpe_ratio=0,
            profit_factor=0,
            average_hold_time=0,
            last_activity=datetime.now(),
            risk_score=0.5,
            consistency_score=0.5,
            favorite_tokens={},
            token_performance={},
            trading_hours={},
            trading_days={},
            average_trades_per_day=0
        )
    
    def _calculate_max_drawdown(self, transactions: List[WalletTransaction]) -> float:
        """Calcule le drawdown maximum"""
        try:
            if not transactions:
                return 0
            
            # Calculer la courbe de P&L cumulé
            cumulative_pnl = []
            running_total = 0
            
            for tx in transactions:
                if tx.profit_loss:
                    running_total += tx.profit_loss
                cumulative_pnl.append(running_total)
            
            if not cumulative_pnl:
                return 0
            
            # Calculer le drawdown
            peak = cumulative_pnl[0]
            max_drawdown = 0
            
            for value in cumulative_pnl:
                if value > peak:
                    peak = value
                
                drawdown = (peak - value) / abs(peak) if peak != 0 else 0
                max_drawdown = max(max_drawdown, drawdown)
            
            return max_drawdown * 100  # En pourcentage
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.DATA, ErrorSeverity.LOW, {
                'function': '_calculate_max_drawdown'
            })
            return 0
    
    def _calculate_sharpe_ratio(self, returns: List[float]) -> float:
        """Calcule le ratio de Sharpe simplifié"""
        try:
            if not returns or len(returns) < 2:
                return 0
            
            mean_return = np.mean(returns)
            std_return = np.std(returns)
            
            if std_return == 0:
                return 0
            
            # Sharpe ratio annualisé (approximation)
            sharpe = (mean_return / std_return) * np.sqrt(252)  # 252 jours de trading
            
            return sharpe
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.DATA, ErrorSeverity.LOW, {
                'function': '_calculate_sharpe_ratio'
            })
            return 0
    
    def _calculate_average_hold_time(self, transactions: List[WalletTransaction]) -> float:
        """Calcule le temps de détention moyen (simulation)"""
        try:
            # Simulation basée sur les patterns de trading
            if not transactions:
                return 0
            
            # Estimer basé sur la fréquence de trading
            total_days = (transactions[-1].timestamp - transactions[0].timestamp).days or 1
            trades_per_day = len(transactions) / total_days
            
            # Plus il y a de trades par jour, plus le hold time est court
            if trades_per_day > 10:
                return np.random.uniform(0.5, 2.0)  # 30min - 2h
            elif trades_per_day > 5:
                return np.random.uniform(2.0, 8.0)  # 2h - 8h
            elif trades_per_day > 1:
                return np.random.uniform(8.0, 48.0)  # 8h - 2 jours
            else:
                return np.random.uniform(48.0, 168.0)  # 2 jours - 1 semaine
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.DATA, ErrorSeverity.LOW, {
                'function': '_calculate_average_hold_time'
            })
            return 24.0  # 1 jour par défaut
    
    def _calculate_risk_score(self, transactions: List[WalletTransaction]) -> float:
        """Calcule un score de risque (0-1, 1 = très risqué)"""
        try:
            if not transactions:
                return 0.5
            
            risk_factors = []
            
            # Facteur 1: Taille des trades
            trade_sizes = [tx.usd_value for tx in transactions]
            avg_size = np.mean(trade_sizes)
            max_size = max(trade_sizes)
            
            size_risk = min(1.0, max_size / (avg_size * 10)) if avg_size > 0 else 0
            risk_factors.append(size_risk)
            
            # Facteur 2: Volatilité des P&L
            pnl_values = [tx.profit_loss for tx in transactions if tx.profit_loss]
            if pnl_values:
                pnl_volatility = np.std(pnl_values) / np.mean(np.abs(pnl_values))
                volatility_risk = min(1.0, pnl_volatility / 2.0)
                risk_factors.append(volatility_risk)
            
            # Facteur 3: Fréquence de trading
            total_days = (transactions[-1].timestamp - transactions[0].timestamp).days or 1
            trades_per_day = len(transactions) / total_days
            frequency_risk = min(1.0, trades_per_day / 20.0)  # 20+ trades/jour = risqué
            risk_factors.append(frequency_risk)
            
            # Score final
            return np.mean(risk_factors)
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.DATA, ErrorSeverity.LOW, {
                'function': '_calculate_risk_score'
            })
            return 0.5
    
    def _calculate_consistency_score(self, transactions: List[WalletTransaction]) -> float:
        """Calcule un score de consistance (0-1, 1 = très consistant)"""
        try:
            if not transactions:
                return 0.5
            
            consistency_factors = []
            
            # Facteur 1: Régularité des gains
            pnl_values = [tx.profit_loss for tx in transactions if tx.profit_loss]
            if pnl_values:
                positive_pnl = [pnl for pnl in pnl_values if pnl > 0]
                win_rate = len(positive_pnl) / len(pnl_values)
                consistency_factors.append(win_rate)
            
            # Facteur 2: Stabilité des tailles de trade
            trade_sizes = [tx.usd_value for tx in transactions]
            if len(trade_sizes) > 1:
                size_cv = np.std(trade_sizes) / np.mean(trade_sizes)  # Coefficient de variation
                size_consistency = max(0, 1 - size_cv)
                consistency_factors.append(size_consistency)
            
            # Facteur 3: Régularité temporelle
            if len(transactions) > 2:
                time_intervals = []
                for i in range(1, len(transactions)):
                    interval = (transactions[i].timestamp - transactions[i-1].timestamp).total_seconds()
                    time_intervals.append(interval)
                
                if time_intervals:
                    time_cv = np.std(time_intervals) / np.mean(time_intervals)
                    time_consistency = max(0, 1 - (time_cv / 2))
                    consistency_factors.append(time_consistency)
            
            return np.mean(consistency_factors) if consistency_factors else 0.5
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.DATA, ErrorSeverity.LOW, {
                'function': '_calculate_consistency_score'
            })
            return 0.5
    
    def get_top_performers(self, limit: int = 10, 
                          min_trades: int = 10) -> List[WalletPerformance]:
        """Retourne les wallets les plus performants"""
        try:
            # Filtrer les wallets avec assez de trades
            eligible_wallets = [
                perf for perf in self.wallet_performances.values()
                if perf.total_trades >= min_trades
            ]
            
            # Trier par score composite
            def performance_score(perf: WalletPerformance) -> float:
                # Score basé sur plusieurs facteurs
                pnl_score = max(0, perf.total_pnl / 1000)  # Normaliser
                win_rate_score = perf.win_rate / 100
                consistency_score = perf.consistency_score
                risk_penalty = 1 - perf.risk_score
                
                return (pnl_score * 0.4 + win_rate_score * 0.3 + 
                       consistency_score * 0.2 + risk_penalty * 0.1)
            
            sorted_wallets = sorted(eligible_wallets, key=performance_score, reverse=True)
            
            return sorted_wallets[:limit]
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.DATA, ErrorSeverity.MEDIUM, {
                'function': 'get_top_performers'
            })
            return []
    
    def get_wallet_performance(self, wallet_address: str) -> Optional[WalletPerformance]:
        """Récupère les performances d'un wallet"""
        return self.wallet_performances.get(wallet_address.lower())
    
    def get_tracked_wallets(self) -> Dict[str, Dict[str, Any]]:
        """Retourne la liste des wallets suivis"""
        return self.tracked_wallets.copy()
    
    def get_analyzer_statistics(self) -> Dict[str, Any]:
        """Retourne les statistiques de l'analyseur"""
        active_wallets = len([w for w in self.tracked_wallets.values() if w['is_active']])
        
        return {
            'total_wallets_tracked': len(self.tracked_wallets),
            'active_wallets': active_wallets,
            'total_wallets_analyzed': self.total_wallets_analyzed,
            'total_signals_generated': self.total_signals_generated,
            'successful_signals': self.successful_signals,
            'signal_success_rate': (self.successful_signals / max(1, self.total_signals_generated)) * 100,
            'wallets_with_performance': len(self.wallet_performances),
            'active_signals': len(self.active_signals)
        }
