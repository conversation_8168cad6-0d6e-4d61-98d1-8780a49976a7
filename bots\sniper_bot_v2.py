#!/usr/bin/env python3
"""
🎯 Sniper Bot v2.0 - Version Sécurisée
Bot de snipe de nouveaux tokens avec validations de sécurité
"""

import os
import sys
import time
import json
import logging
from pathlib import Path

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from web3 import Web3
import requests
from config.settings import config
from utils.notifications import notifier
from utils.security import security
from utils.error_handler import with_retry, ErrorCategory, ErrorSeverity
from utils.network_handler import network_handler
from utils.trading_handler import trading_handler

class SniperBotV2:
    """Bot de snipe sécurisé avec validations"""
    
    def __init__(self):
        self.logger = self._setup_logging()
        self.web3 = None
        self.router = None
        self.config = config.get_sniper_config()
        
        # Statistiques
        self.stats = {
            "tokens_scanned": 0,
            "tokens_rejected": 0,
            "trades_attempted": 0,
            "trades_successful": 0,
            "start_time": time.time()
        }
        
        self._init_web3()
        self._load_abis()
        self._validate_config()
    
    def _setup_logging(self):
        """Configure le logging"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('sniper_bot_v2.log'),
                logging.StreamHandler()
            ]
        )
        return logging.getLogger(__name__)
    
    def _init_web3(self):
        """Initialise la connexion Web3"""
        try:
            self.web3 = Web3(Web3.HTTPProvider(config.bsc_rpc_url))
            if not self.web3.is_connected():
                raise Exception("Impossible de se connecter à BSC")
            self.logger.info("✅ Connexion Web3 établie")
        except Exception as e:
            self.logger.error(f"❌ Erreur Web3: {e}")
            raise
    
    def _load_abis(self):
        """Charge les ABI nécessaires"""
        try:
            # Charger les ABI depuis le répertoire racine
            project_root = Path(__file__).parent.parent
            
            with open(project_root / "pancakeswap_router_abi.json", "r") as f:
                router_abi = json.load(f)
            
            with open(project_root / "erc20_abi.json", "r") as f:
                self.erc20_abi = json.load(f)
            
            # Initialiser le contrat router
            self.router = self.web3.eth.contract(
                address=Web3.to_checksum_address(self.config["pancakeswap_router"]),
                abi=router_abi
            )
            
            self.logger.info("✅ ABI chargés avec succès")
            
        except Exception as e:
            self.logger.error(f"❌ Erreur chargement ABI: {e}")
            raise
    
    def _validate_config(self):
        """Valide la configuration"""
        required_fields = [
            "private_key", "wallet_address", "pancakeswap_router", 
            "wrapped_bnb", "invest_amount"
        ]
        
        missing_fields = []
        for field in required_fields:
            if not self.config.get(field):
                missing_fields.append(field)
        
        if missing_fields:
            error_msg = f"Configuration incomplète: {missing_fields}"
            self.logger.error(error_msg)
            notifier.send_error(error_msg, "SniperBot")
            raise ValueError(error_msg)
        
        self.logger.info("✅ Configuration validée")
    
    @with_retry(
        category=ErrorCategory.API,
        severity=ErrorSeverity.MEDIUM,
        fallback_on_failure=True
    )
    def get_new_tokens(self) -> list:
        """Récupère les nouveaux tokens depuis DexScreener avec gestion d'erreurs robuste"""
        try:
            # Vérifier la connectivité réseau avant l'appel
            if not network_handler.check_connectivity(['api.dexscreener.com']).get('api.dexscreener.com', False):
                self.logger.warning("⚠️ Connectivité DexScreener indisponible")
                return []

            response = network_handler.get(self.config["dex_screener_api"])

            data = response.json()
            new_tokens = []

            for item in data:
                if isinstance(item, dict) and 'tokenAddress' in item:
                    token_address = item['tokenAddress']
                    if self._is_new_token(token_address):
                        new_tokens.append(token_address)

            self.stats["tokens_scanned"] += len(new_tokens)
            self.logger.info(f"🔍 {len(new_tokens)} nouveaux tokens trouvés")

            return new_tokens

        except Exception as e:
            self.logger.error(f"❌ Erreur récupération tokens: {e}")
            # Le décorateur with_retry gère automatiquement les tentatives
            raise
    
    def _is_new_token(self, token_address: str) -> bool:
        """Vérifie si un token est nouveau (pas encore traité)"""
        # Ici on pourrait implémenter une base de données des tokens déjà vus
        # Pour l'instant, on considère tous les tokens comme nouveaux
        return True
    
    def validate_token(self, token_address: str) -> dict:
        """Valide un token avant achat"""
        self.logger.info(f"🔍 Validation du token: {token_address}")
        
        # Vérification complète de sécurité
        validation_result = security.comprehensive_token_check(
            token_address, 
            self.config["invest_amount"]
        )
        
        if not validation_result["is_safe_to_trade"]:
            self.stats["tokens_rejected"] += 1
            self.logger.warning(f"🚫 Token rejeté: {token_address}")
            self.logger.warning(f"Raisons: {validation_result['warnings']}")
            
            # Ajouter à la blacklist si risque élevé
            if validation_result["risk_level"] == "HIGH":
                security.add_to_blacklist(token_address, "High risk detected")
        
        return validation_result
    
    def buy_token(self, token_address: str) -> bool:
        """Achète un token après validation"""
        try:
            # Validation de sécurité
            validation = self.validate_token(token_address)
            if not validation["is_safe_to_trade"]:
                return False
            
            self.logger.info(f"💰 Tentative d'achat: {token_address}")
            notifier.send_snipe_attempt(token_address, self.config["invest_amount"])
            
            # Préparer la transaction
            token_address_checksum = Web3.to_checksum_address(token_address)
            wallet_address = Web3.to_checksum_address(self.config["wallet_address"])
            wrapped_bnb = Web3.to_checksum_address(self.config["wrapped_bnb"])
            
            # Calculer amount_out_min avec slippage
            invest_amount_wei = self.web3.to_wei(self.config["invest_amount"], "ether")
            slippage_factor = (100 - self.config["slippage"]) / 100
            amount_out_min = int(invest_amount_wei * slippage_factor)
            
            # Construire la transaction
            txn = self.router.functions.swapExactETHForTokens(
                amount_out_min,
                [wrapped_bnb, token_address_checksum],
                wallet_address,
                int(time.time()) + 300  # 5 minutes deadline
            ).build_transaction({
                "from": wallet_address,
                "value": invest_amount_wei,
                "gas": 300000,  # Gas plus élevé pour éviter les échecs
                "gasPrice": self.web3.to_wei("10", "gwei"),  # Gas price plus élevé
                "nonce": self.web3.eth.get_transaction_count(wallet_address)
            })
            
            # Signer et envoyer la transaction
            signed_txn = self.web3.eth.account.sign_transaction(
                txn, 
                private_key=self.config["private_key"]
            )
            
            tx_hash = self.web3.eth.send_raw_transaction(signed_txn.rawTransaction)
            tx_hash_hex = self.web3.to_hex(tx_hash)
            
            self.stats["trades_attempted"] += 1
            self.logger.info(f"✅ Transaction envoyée: {tx_hash_hex}")
            
            # Attendre la confirmation (optionnel)
            try:
                receipt = self.web3.eth.wait_for_transaction_receipt(tx_hash, timeout=60)
                if receipt.status == 1:
                    self.stats["trades_successful"] += 1
                    self.logger.info(f"🎉 Achat réussi: {token_address}")
                    notifier.send_snipe_success(token_address, tx_hash_hex)
                    return True
                else:
                    self.logger.error(f"❌ Transaction échouée: {tx_hash_hex}")
                    return False
            except Exception as e:
                self.logger.warning(f"⏰ Timeout confirmation: {e}")
                # La transaction peut quand même réussir
                return True
            
        except Exception as e:
            self.logger.error(f"❌ Erreur achat {token_address}: {e}")
            notifier.send_error(f"Erreur achat {token_address}: {str(e)}", "SniperBot")
            return False
    
    def get_stats_summary(self) -> str:
        """Retourne un résumé des statistiques"""
        runtime = time.time() - self.stats["start_time"]
        runtime_hours = runtime / 3600
        
        return (
            f"📊 Statistiques SniperBot:\n"
            f"⏱️ Durée: {runtime_hours:.1f}h\n"
            f"🔍 Tokens scannés: {self.stats['tokens_scanned']}\n"
            f"🚫 Tokens rejetés: {self.stats['tokens_rejected']}\n"
            f"💰 Trades tentés: {self.stats['trades_attempted']}\n"
            f"✅ Trades réussis: {self.stats['trades_successful']}\n"
            f"📈 Taux de réussite: {(self.stats['trades_successful']/max(1,self.stats['trades_attempted'])*100):.1f}%"
        )
    
    def run(self):
        """Lance le bot en mode continu"""
        self.logger.info("🚀 Démarrage du SniperBot v2.0")
        notifier.send_telegram("🎯 SniperBot v2.0 démarré avec validations de sécurité")
        
        last_stats_time = time.time()
        
        try:
            while True:
                try:
                    # Récupérer les nouveaux tokens
                    new_tokens = self.get_new_tokens()
                    
                    # Traiter chaque token
                    for token in new_tokens:
                        self.buy_token(token)
                        time.sleep(2)  # Pause entre les trades
                    
                    # Envoyer les stats toutes les heures
                    if time.time() - last_stats_time >= 3600:
                        stats_msg = self.get_stats_summary()
                        self.logger.info(stats_msg)
                        notifier.send_telegram(stats_msg)
                        last_stats_time = time.time()
                    
                    # Pause avant le prochain scan
                    time.sleep(60)
                    
                except KeyboardInterrupt:
                    self.logger.info("🛑 Arrêt demandé par l'utilisateur")
                    break
                except Exception as e:
                    self.logger.error(f"❌ Erreur dans la boucle principale: {e}")
                    notifier.send_error(f"Erreur boucle principale: {str(e)}", "SniperBot")
                    time.sleep(30)  # Pause plus longue en cas d'erreur
        
        finally:
            # Statistiques finales
            final_stats = self.get_stats_summary()
            self.logger.info(f"🏁 SniperBot arrêté\n{final_stats}")
            notifier.send_telegram(f"🏁 SniperBot arrêté\n{final_stats}")

def main():
    """Point d'entrée principal"""
    try:
        bot = SniperBotV2()
        bot.run()
    except Exception as e:
        logging.error(f"❌ Erreur fatale: {e}")
        notifier.send_error(f"Erreur fatale SniperBot: {str(e)}", "SniperBot")
        sys.exit(1)

if __name__ == "__main__":
    main()
