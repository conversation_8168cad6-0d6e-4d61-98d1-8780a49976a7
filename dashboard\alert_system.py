"""
🚨 Système d'alertes intelligent pour le dashboard
Détection automatique d'anomalies et notifications multi-canal
"""

import asyncio
import time
import json
from decimal import Decimal
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from enum import Enum
from collections import defaultdict, deque
import logging
from pathlib import Path
import sys

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from dashboard.metrics_collector import MetricsCollector, BotMetrics, SystemMetrics
from utils.error_handler import error_handler, ErrorCategory, ErrorSeverity
from logging_system.central_logger import central_logger, LogCategory
from utils.notifications import notifier

class AlertLevel(Enum):
    """Niveaux d'alerte"""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"
    EMERGENCY = "emergency"

class AlertType(Enum):
    """Types d'alerte"""
    SYSTEM = "system"
    BOT = "bot"
    PERFORMANCE = "performance"
    RISK = "risk"
    TECHNICAL = "technical"

@dataclass
class AlertRule:
    """Règle d'alerte"""
    id: str
    name: str
    description: str
    alert_type: AlertType
    level: AlertLevel
    condition: Callable[[Any], bool]
    threshold: Optional[float] = None
    enabled: bool = True
    cooldown_minutes: int = 15
    notification_channels: List[str] = None

@dataclass
class Alert:
    """Alerte générée"""
    id: str
    rule_id: str
    alert_type: AlertType
    level: AlertLevel
    title: str
    message: str
    value: Optional[float]
    threshold: Optional[float]
    bot_id: Optional[str]
    timestamp: datetime
    acknowledged: bool = False
    resolved: bool = False
    resolved_at: Optional[datetime] = None

class AlertSystem:
    """Système d'alertes intelligent"""
    
    def __init__(self, metrics_collector: MetricsCollector):
        self.logger = logging.getLogger(__name__)
        self.metrics_collector = metrics_collector
        
        # Alertes actives et historique
        self.active_alerts: Dict[str, Alert] = {}
        self.alert_history: deque = deque(maxlen=10000)
        
        # Règles d'alerte
        self.alert_rules: Dict[str, AlertRule] = {}
        
        # Cooldown des alertes
        self.alert_cooldowns: Dict[str, datetime] = {}
        
        # Configuration
        self.check_interval = 30  # 30 secondes
        self.max_alerts_per_hour = 50
        self.alert_counts: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        
        # Initialiser les règles par défaut
        self._setup_default_rules()
        
        central_logger.log(
            level="INFO",
            message="Système d'alertes initialisé",
            category=LogCategory.SYSTEM,
            rules_count=len(self.alert_rules)
        )
    
    def _setup_default_rules(self):
        """Configure les règles d'alerte par défaut"""
        
        # Règles système
        self.add_rule(AlertRule(
            id="system_health_critical",
            name="Santé système critique",
            description="Score de santé système inférieur à 30%",
            alert_type=AlertType.SYSTEM,
            level=AlertLevel.CRITICAL,
            condition=lambda metrics: metrics and metrics.system_health_score < 0.3,
            threshold=0.3,
            notification_channels=["telegram", "email"]
        ))
        
        self.add_rule(AlertRule(
            id="cpu_usage_high",
            name="Utilisation CPU élevée",
            description="Utilisation CPU supérieure à 90%",
            alert_type=AlertType.SYSTEM,
            level=AlertLevel.WARNING,
            condition=lambda metrics: metrics and metrics.cpu_usage > 90,
            threshold=90,
            cooldown_minutes=10
        ))
        
        self.add_rule(AlertRule(
            id="memory_usage_high",
            name="Utilisation mémoire élevée",
            description="Utilisation mémoire supérieure à 85%",
            alert_type=AlertType.SYSTEM,
            level=AlertLevel.WARNING,
            condition=lambda metrics: metrics and metrics.memory_usage > 85,
            threshold=85,
            cooldown_minutes=10
        ))
        
        # Règles de performance
        self.add_rule(AlertRule(
            id="daily_loss_significant",
            name="Perte quotidienne importante",
            description="Perte quotidienne supérieure à $1000",
            alert_type=AlertType.PERFORMANCE,
            level=AlertLevel.WARNING,
            condition=lambda metrics: metrics and metrics.total_daily_pnl < -1000,
            threshold=-1000,
            notification_channels=["telegram"]
        ))
        
        self.add_rule(AlertRule(
            id="portfolio_drawdown_high",
            name="Drawdown portefeuille élevé",
            description="Drawdown du portefeuille supérieur à 10%",
            alert_type=AlertType.RISK,
            level=AlertLevel.CRITICAL,
            condition=lambda value: value and value > 10,
            threshold=10,
            notification_channels=["telegram", "email"]
        ))
        
        # Règles bot
        self.add_rule(AlertRule(
            id="bot_stopped",
            name="Bot arrêté",
            description="Un bot s'est arrêté de manière inattendue",
            alert_type=AlertType.BOT,
            level=AlertLevel.WARNING,
            condition=lambda bot: bot and bot.status != "running",
            cooldown_minutes=5
        ))
        
        self.add_rule(AlertRule(
            id="bot_error_rate_high",
            name="Taux d'erreur bot élevé",
            description="Taux d'erreur d'un bot supérieur à 20%",
            alert_type=AlertType.TECHNICAL,
            level=AlertLevel.WARNING,
            condition=lambda bot: bot and bot.error_rate > 0.2,
            threshold=0.2,
            cooldown_minutes=20
        ))
        
        self.add_rule(AlertRule(
            id="bot_performance_poor",
            name="Performance bot dégradée",
            description="Win rate d'un bot inférieur à 40%",
            alert_type=AlertType.PERFORMANCE,
            level=AlertLevel.WARNING,
            condition=lambda bot: bot and bot.total_trades > 10 and bot.win_rate < 40,
            threshold=40,
            cooldown_minutes=60
        ))
    
    def add_rule(self, rule: AlertRule):
        """Ajoute une règle d'alerte"""
        try:
            if rule.notification_channels is None:
                rule.notification_channels = ["telegram"]
            
            self.alert_rules[rule.id] = rule
            
            central_logger.log(
                level="INFO",
                message=f"Règle d'alerte ajoutée: {rule.name}",
                category=LogCategory.SYSTEM,
                rule_id=rule.id,
                level=rule.level.value
            )
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.MEDIUM, {
                'function': 'add_rule',
                'rule_id': rule.id
            })
    
    def remove_rule(self, rule_id: str):
        """Supprime une règle d'alerte"""
        try:
            if rule_id in self.alert_rules:
                del self.alert_rules[rule_id]
                
                central_logger.log(
                    level="INFO",
                    message=f"Règle d'alerte supprimée: {rule_id}",
                    category=LogCategory.SYSTEM
                )
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.LOW, {
                'function': 'remove_rule',
                'rule_id': rule_id
            })
    
    async def start_monitoring(self):
        """Démarre le monitoring des alertes"""
        while True:
            try:
                # Vérifier les règles système
                await self._check_system_rules()
                
                # Vérifier les règles des bots
                await self._check_bot_rules()
                
                # Nettoyer les alertes résolues
                self._cleanup_resolved_alerts()
                
                # Attendre avant la prochaine vérification
                await asyncio.sleep(self.check_interval)
                
            except Exception as e:
                error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.MEDIUM, {
                    'function': 'start_monitoring'
                })
                await asyncio.sleep(60)
    
    async def _check_system_rules(self):
        """Vérifie les règles d'alerte système"""
        try:
            system_metrics = self.metrics_collector.get_system_metrics()
            
            for rule in self.alert_rules.values():
                if (rule.alert_type == AlertType.SYSTEM and 
                    rule.enabled and 
                    not self._is_in_cooldown(rule.id)):
                    
                    try:
                        if rule.condition(system_metrics):
                            await self._trigger_alert(rule, system_metrics)
                    except Exception as e:
                        error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.LOW, {
                            'function': '_check_system_rules',
                            'rule_id': rule.id
                        })
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.MEDIUM, {
                'function': '_check_system_rules'
            })
    
    async def _check_bot_rules(self):
        """Vérifie les règles d'alerte des bots"""
        try:
            bot_metrics = self.metrics_collector.get_bot_metrics()
            
            for bot_id, bot_metric in bot_metrics.items():
                for rule in self.alert_rules.values():
                    if (rule.alert_type in [AlertType.BOT, AlertType.PERFORMANCE, AlertType.TECHNICAL] and
                        rule.enabled and
                        not self._is_in_cooldown(f"{rule.id}_{bot_id}")):
                        
                        try:
                            if rule.condition(bot_metric):
                                await self._trigger_alert(rule, bot_metric, bot_id)
                        except Exception as e:
                            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.LOW, {
                                'function': '_check_bot_rules',
                                'rule_id': rule.id,
                                'bot_id': bot_id
                            })
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.MEDIUM, {
                'function': '_check_bot_rules'
            })
    
    def _is_in_cooldown(self, key: str) -> bool:
        """Vérifie si une alerte est en cooldown"""
        if key not in self.alert_cooldowns:
            return False
        
        cooldown_end = self.alert_cooldowns[key]
        return datetime.now() < cooldown_end
    
    async def _trigger_alert(self, rule: AlertRule, data: Any, bot_id: str = None):
        """Déclenche une alerte"""
        try:
            # Vérifier les limites de taux
            if not self._check_rate_limit(rule.id):
                return
            
            # Générer l'ID de l'alerte
            alert_id = f"{rule.id}_{int(time.time())}"
            if bot_id:
                alert_id = f"{rule.id}_{bot_id}_{int(time.time())}"
            
            # Extraire la valeur selon le type de données
            value = None
            if isinstance(data, SystemMetrics):
                value = self._extract_system_value(rule, data)
            elif isinstance(data, BotMetrics):
                value = self._extract_bot_value(rule, data)
            
            # Créer l'alerte
            alert = Alert(
                id=alert_id,
                rule_id=rule.id,
                alert_type=rule.alert_type,
                level=rule.level,
                title=rule.name,
                message=self._generate_alert_message(rule, data, bot_id),
                value=value,
                threshold=rule.threshold,
                bot_id=bot_id,
                timestamp=datetime.now()
            )
            
            # Ajouter aux alertes actives
            self.active_alerts[alert_id] = alert
            self.alert_history.append(alert)
            
            # Définir le cooldown
            cooldown_key = rule.id if not bot_id else f"{rule.id}_{bot_id}"
            self.alert_cooldowns[cooldown_key] = (
                datetime.now() + timedelta(minutes=rule.cooldown_minutes)
            )
            
            # Envoyer les notifications
            await self._send_notifications(alert, rule)
            
            # Logger l'alerte
            central_logger.log(
                level="WARNING" if rule.level in [AlertLevel.WARNING, AlertLevel.INFO] else "ERROR",
                message=f"Alerte déclenchée: {rule.name}",
                category=LogCategory.ALERT,
                alert_id=alert_id,
                rule_id=rule.id,
                level=rule.level.value,
                bot_id=bot_id,
                value=value,
                threshold=rule.threshold
            )
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.MEDIUM, {
                'function': '_trigger_alert',
                'rule_id': rule.id
            })
    
    def _check_rate_limit(self, rule_id: str) -> bool:
        """Vérifie les limites de taux d'alerte"""
        try:
            now = datetime.now()
            one_hour_ago = now - timedelta(hours=1)
            
            # Nettoyer les anciens compteurs
            self.alert_counts[rule_id] = deque(
                [timestamp for timestamp in self.alert_counts[rule_id] if timestamp > one_hour_ago],
                maxlen=100
            )
            
            # Vérifier la limite
            if len(self.alert_counts[rule_id]) >= self.max_alerts_per_hour:
                return False
            
            # Ajouter le timestamp actuel
            self.alert_counts[rule_id].append(now)
            return True
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.LOW, {
                'function': '_check_rate_limit',
                'rule_id': rule_id
            })
            return True
    
    def _extract_system_value(self, rule: AlertRule, data: SystemMetrics) -> Optional[float]:
        """Extrait la valeur pertinente des métriques système"""
        value_map = {
            "system_health_critical": data.system_health_score,
            "cpu_usage_high": data.cpu_usage,
            "memory_usage_high": data.memory_usage,
            "daily_loss_significant": data.total_daily_pnl
        }
        return value_map.get(rule.id)
    
    def _extract_bot_value(self, rule: AlertRule, data: BotMetrics) -> Optional[float]:
        """Extrait la valeur pertinente des métriques de bot"""
        value_map = {
            "bot_error_rate_high": data.error_rate,
            "bot_performance_poor": data.win_rate
        }
        return value_map.get(rule.id)
    
    def _generate_alert_message(self, rule: AlertRule, data: Any, bot_id: str = None) -> str:
        """Génère le message d'alerte"""
        try:
            base_message = rule.description
            
            if isinstance(data, SystemMetrics):
                if rule.id == "system_health_critical":
                    return f"Score de santé système: {data.system_health_score:.1%}"
                elif rule.id == "cpu_usage_high":
                    return f"Utilisation CPU: {data.cpu_usage:.1f}%"
                elif rule.id == "memory_usage_high":
                    return f"Utilisation mémoire: {data.memory_usage:.1f}%"
                elif rule.id == "daily_loss_significant":
                    return f"Perte quotidienne: ${data.total_daily_pnl:.2f}"
            
            elif isinstance(data, BotMetrics):
                bot_prefix = f"Bot {bot_id}: " if bot_id else ""
                
                if rule.id == "bot_stopped":
                    return f"{bot_prefix}Statut: {data.status}"
                elif rule.id == "bot_error_rate_high":
                    return f"{bot_prefix}Taux d'erreur: {data.error_rate:.1%}"
                elif rule.id == "bot_performance_poor":
                    return f"{bot_prefix}Win rate: {data.win_rate:.1f}% ({data.total_trades} trades)"
            
            return base_message
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.LOW, {
                'function': '_generate_alert_message',
                'rule_id': rule.id
            })
            return rule.description
    
    async def _send_notifications(self, alert: Alert, rule: AlertRule):
        """Envoie les notifications pour une alerte"""
        try:
            # Icônes selon le niveau
            level_icons = {
                AlertLevel.INFO: "ℹ️",
                AlertLevel.WARNING: "⚠️",
                AlertLevel.CRITICAL: "🚨",
                AlertLevel.EMERGENCY: "🆘"
            }
            
            icon = level_icons.get(alert.level, "⚠️")
            
            # Message formaté
            message = f"{icon} <b>{alert.title}</b>\n"
            message += f"📊 {alert.message}\n"
            message += f"🕐 {alert.timestamp.strftime('%H:%M:%S')}"
            
            if alert.bot_id:
                message += f"\n🤖 Bot: {alert.bot_id}"
            
            # Envoyer selon les canaux configurés
            for channel in rule.notification_channels:
                try:
                    if channel == "telegram":
                        await notifier.send_telegram(message)
                    elif channel == "email":
                        await notifier.send_email(
                            subject=f"Alerte {alert.level.value}: {alert.title}",
                            body=alert.message
                        )
                except Exception as e:
                    error_handler.handle_error(e, ErrorCategory.NOTIFICATION, ErrorSeverity.LOW, {
                        'function': '_send_notifications',
                        'channel': channel,
                        'alert_id': alert.id
                    })
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.NOTIFICATION, ErrorSeverity.MEDIUM, {
                'function': '_send_notifications',
                'alert_id': alert.id
            })
    
    def _cleanup_resolved_alerts(self):
        """Nettoie les alertes résolues"""
        try:
            # Marquer comme résolues les alertes qui ne sont plus actives
            current_time = datetime.now()
            
            # Alertes à résoudre automatiquement après 1 heure
            auto_resolve_time = current_time - timedelta(hours=1)
            
            resolved_alerts = []
            for alert_id, alert in self.active_alerts.items():
                if (not alert.resolved and 
                    alert.timestamp < auto_resolve_time and
                    alert.level in [AlertLevel.INFO, AlertLevel.WARNING]):
                    
                    alert.resolved = True
                    alert.resolved_at = current_time
                    resolved_alerts.append(alert_id)
            
            # Supprimer les alertes résolues des alertes actives
            for alert_id in resolved_alerts:
                del self.active_alerts[alert_id]
            
            if resolved_alerts:
                central_logger.log(
                    level="INFO",
                    message=f"{len(resolved_alerts)} alertes auto-résolues",
                    category=LogCategory.SYSTEM
                )
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.LOW, {
                'function': '_cleanup_resolved_alerts'
            })
    
    def acknowledge_alert(self, alert_id: str, user: str = "system") -> bool:
        """Acquitte une alerte"""
        try:
            if alert_id in self.active_alerts:
                alert = self.active_alerts[alert_id]
                alert.acknowledged = True
                
                central_logger.log(
                    level="INFO",
                    message=f"Alerte acquittée: {alert.title}",
                    category=LogCategory.ALERT,
                    alert_id=alert_id,
                    user=user
                )
                
                return True
            
            return False
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.LOW, {
                'function': 'acknowledge_alert',
                'alert_id': alert_id
            })
            return False
    
    def resolve_alert(self, alert_id: str, user: str = "system") -> bool:
        """Résout une alerte"""
        try:
            if alert_id in self.active_alerts:
                alert = self.active_alerts[alert_id]
                alert.resolved = True
                alert.resolved_at = datetime.now()
                
                # Supprimer des alertes actives
                del self.active_alerts[alert_id]
                
                central_logger.log(
                    level="INFO",
                    message=f"Alerte résolue: {alert.title}",
                    category=LogCategory.ALERT,
                    alert_id=alert_id,
                    user=user
                )
                
                return True
            
            return False
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.LOW, {
                'function': 'resolve_alert',
                'alert_id': alert_id
            })
            return False
    
    def get_active_alerts(self, level: AlertLevel = None, alert_type: AlertType = None) -> List[Alert]:
        """Récupère les alertes actives avec filtres optionnels"""
        alerts = list(self.active_alerts.values())
        
        if level:
            alerts = [a for a in alerts if a.level == level]
        
        if alert_type:
            alerts = [a for a in alerts if a.alert_type == alert_type]
        
        # Trier par niveau et timestamp
        level_priority = {
            AlertLevel.EMERGENCY: 4,
            AlertLevel.CRITICAL: 3,
            AlertLevel.WARNING: 2,
            AlertLevel.INFO: 1
        }
        
        alerts.sort(key=lambda a: (level_priority.get(a.level, 0), a.timestamp), reverse=True)
        
        return alerts
    
    def get_alert_statistics(self) -> Dict[str, Any]:
        """Récupère les statistiques des alertes"""
        try:
            now = datetime.now()
            last_24h = now - timedelta(hours=24)
            
            # Alertes des dernières 24h
            recent_alerts = [a for a in self.alert_history if a.timestamp > last_24h]
            
            # Statistiques par niveau
            level_counts = defaultdict(int)
            for alert in recent_alerts:
                level_counts[alert.level.value] += 1
            
            # Statistiques par type
            type_counts = defaultdict(int)
            for alert in recent_alerts:
                type_counts[alert.alert_type.value] += 1
            
            return {
                'active_alerts_count': len(self.active_alerts),
                'total_alerts_24h': len(recent_alerts),
                'alerts_by_level': dict(level_counts),
                'alerts_by_type': dict(type_counts),
                'rules_count': len(self.alert_rules),
                'enabled_rules_count': len([r for r in self.alert_rules.values() if r.enabled])
            }
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.LOW, {
                'function': 'get_alert_statistics'
            })
            return {}
