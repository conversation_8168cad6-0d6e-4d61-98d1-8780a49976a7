#!/usr/bin/env python3
"""
🧪 Exemple d'utilisation du paper trading
Démontre comment utiliser le système de paper trading
"""

import sys
import time
from pathlib import Path

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from paper_trading.session_manager import session_manager
from paper_trading.simulator import PaperTradingSimulator
from backtesting.strategies import trading_strategies
import logging

def setup_logging():
    """Configure le logging pour l'exemple"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def example_basic_session():
    """Exemple de session de paper trading basique"""
    print("🧪 EXEMPLE 1: Session de paper trading basique")
    print("="*60)
    
    try:
        # Créer une session avec la stratégie Grid Trading
        session_id = session_manager.create_session(
            strategy_name="Grid Trading",
            symbol="BTC/USDT",
            capital=1000,
            use_real_data=False  # Utiliser des données simulées
        )
        
        print(f"✅ Session créée: {session_id}")
        
        # Démarrer la session
        success = session_manager.start_session(session_id)
        if success:
            print("🚀 Session démarrée, simulation en cours...")
            
            # Laisser tourner pendant 30 secondes
            for i in range(6):
                time.sleep(5)
                status = session_manager.get_session_status(session_id)
                if status and 'performance' in status:
                    perf = status['performance']
                    print(f"📊 Itération {i+1}: Return {perf['total_return_percent']:.2f}%, "
                          f"Trades: {perf['total_trades']}, Prix: {perf['current_price']:.2f}")
            
            # Arrêter la session
            session_manager.stop_session(session_id)
            print("🛑 Session arrêtée")
            
            # Afficher les résultats finaux
            final_status = session_manager.get_session_status(session_id)
            if final_status and 'performance' in final_status:
                perf = final_status['performance']
                print(f"\n📈 RÉSULTATS FINAUX:")
                print(f"   Rendement: {perf['total_return_percent']:.2f}%")
                print(f"   Trades: {perf['total_trades']}")
                print(f"   Taux de réussite: {perf['win_rate_percent']:.1f}%")
                print(f"   P&L: {perf['total_pnl']:.2f}")
            
            # Sauvegarder les résultats
            filepath = session_manager.save_session_results(session_id)
            print(f"💾 Résultats sauvegardés: {filepath}")
        
        return session_id
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return None

def example_multiple_sessions():
    """Exemple avec plusieurs sessions simultanées"""
    print("\n🧪 EXEMPLE 2: Sessions multiples")
    print("="*60)
    
    strategies_to_test = ["SMA Crossover", "RSI Mean Reversion", "Buy & Hold"]
    session_ids = []
    
    try:
        # Créer plusieurs sessions
        for strategy in strategies_to_test:
            session_id = session_manager.create_session(
                strategy_name=strategy,
                symbol="ETH/USDT",
                capital=1000,
                use_real_data=False
            )
            session_ids.append(session_id)
            print(f"✅ Session {strategy}: {session_id}")
        
        # Démarrer toutes les sessions
        for session_id in session_ids:
            session_manager.start_session(session_id)
        
        print(f"🚀 {len(session_ids)} sessions démarrées")
        
        # Monitorer pendant 20 secondes
        for i in range(4):
            time.sleep(5)
            print(f"\n📊 Statut après {(i+1)*5} secondes:")
            
            for session_id in session_ids:
                status = session_manager.get_session_status(session_id)
                if status and 'performance' in status:
                    perf = status['performance']
                    print(f"   {status['strategy_name']}: {perf['total_return_percent']:.2f}% "
                          f"({perf['total_trades']} trades)")
        
        # Arrêter toutes les sessions
        for session_id in session_ids:
            session_manager.stop_session(session_id)
        
        print("\n🛑 Toutes les sessions arrêtées")
        
        # Comparer les résultats
        print("\n📊 COMPARAISON DES RÉSULTATS:")
        print("-" * 60)
        
        results = []
        for session_id in session_ids:
            status = session_manager.get_session_status(session_id)
            if status and 'performance' in status:
                results.append((status['strategy_name'], status['performance']))
        
        # Trier par rendement
        results.sort(key=lambda x: x[1]['total_return_percent'], reverse=True)
        
        for i, (strategy, perf) in enumerate(results):
            rank_emoji = ["🥇", "🥈", "🥉"][i] if i < 3 else "📊"
            print(f"{rank_emoji} {strategy}: {perf['total_return_percent']:.2f}% "
                  f"({perf['total_trades']} trades, {perf['win_rate_percent']:.1f}% win rate)")
        
        return session_ids
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return []

def example_custom_strategy():
    """Exemple avec une stratégie personnalisée"""
    print("\n🧪 EXEMPLE 3: Stratégie personnalisée")
    print("="*60)
    
    def simple_trend_strategy(engine, data, index, symbol):
        """
        Stratégie de suivi de tendance simple:
        - Achat si prix > moyenne mobile 10
        - Vente si prix < moyenne mobile 10
        """
        if index < 10:
            return
        
        # Calculer la moyenne mobile 10
        ma_10 = data.iloc[index-9:index+1]['close'].mean()
        current_price = data.iloc[index]['close']
        position = engine.positions.get(symbol, 0)
        
        # Signal d'achat
        if current_price > ma_10 * 1.01 and position == 0:  # 1% au-dessus de MA
            quantity = (engine.current_capital * 0.9) / current_price
            engine.execute_trade(symbol, 'buy', quantity, current_price)
        
        # Signal de vente
        elif current_price < ma_10 * 0.99 and position > 0:  # 1% en-dessous de MA
            engine.execute_trade(symbol, 'sell', position, current_price)
    
    try:
        # Créer une session avec stratégie personnalisée
        session_id = session_manager.create_custom_strategy_session(
            strategy_func=simple_trend_strategy,
            strategy_name="Simple Trend Following",
            symbol="BTC/USDT",
            capital=1000,
            use_real_data=False
        )
        
        print(f"✅ Session personnalisée créée: {session_id}")
        
        # Démarrer et monitorer
        session_manager.start_session(session_id)
        print("🚀 Stratégie personnalisée en cours...")
        
        # Monitorer pendant 25 secondes
        for i in range(5):
            time.sleep(5)
            status = session_manager.get_session_status(session_id)
            if status and 'performance' in status:
                perf = status['performance']
                print(f"📊 {(i+1)*5}s: Return {perf['total_return_percent']:.2f}%, "
                      f"Trades: {perf['total_trades']}, Position: {perf['current_position']:.4f}")
        
        # Arrêter et afficher les résultats
        session_manager.stop_session(session_id)
        
        final_status = session_manager.get_session_status(session_id)
        if final_status and 'performance' in final_status:
            perf = final_status['performance']
            print(f"\n📈 RÉSULTATS STRATÉGIE PERSONNALISÉE:")
            print(f"   Rendement final: {perf['total_return_percent']:.2f}%")
            print(f"   Nombre de trades: {perf['total_trades']}")
            print(f"   Capital final: {perf['current_capital']:.2f}")
            print(f"   Valeur portfolio: {perf['portfolio_value']:.2f}")
        
        return session_id
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return None

def example_real_data_simulation():
    """Exemple avec des données de marché réelles"""
    print("\n🧪 EXEMPLE 4: Simulation avec données réelles")
    print("="*60)
    
    try:
        # Créer une session avec données réelles
        session_id = session_manager.create_session(
            strategy_name="MACD",
            symbol="BTC/USDT",
            capital=1000,
            use_real_data=True  # Utiliser des données réelles
        )
        
        print(f"✅ Session avec données réelles créée: {session_id}")
        print("🌐 Récupération des prix de marché en temps réel...")
        
        # Démarrer la session
        session_manager.start_session(session_id)
        
        # Monitorer pendant 15 secondes
        for i in range(3):
            time.sleep(5)
            status = session_manager.get_session_status(session_id)
            if status and 'performance' in status:
                perf = status['performance']
                print(f"📊 Prix réel: {perf['current_price']:.2f}, "
                      f"Return: {perf['total_return_percent']:.2f}%, "
                      f"Trades: {perf['total_trades']}")
        
        # Arrêter la session
        session_manager.stop_session(session_id)
        print("🛑 Session avec données réelles terminée")
        
        return session_id
        
    except Exception as e:
        print(f"❌ Erreur (normal si pas d'accès API): {e}")
        return None

def show_session_summary():
    """Affiche un résumé de toutes les sessions"""
    print("\n📋 RÉSUMÉ DES SESSIONS")
    print("="*60)
    
    # Statistiques générales
    stats = session_manager.get_summary_stats()
    print(f"📊 Sessions totales: {stats['total_sessions']}")
    print(f"🟢 En cours: {stats['running_sessions']}")
    print(f"🔴 Arrêtées: {stats['stopped_sessions']}")
    
    if stats['stopped_sessions'] > 0:
        print(f"📈 Rendement moyen: {stats['avg_return_percent']:.2f}%")
        print(f"🔢 Trades moyens: {stats['avg_trades_per_session']:.1f}")
    
    # Liste détaillée
    sessions = session_manager.list_sessions()
    if sessions:
        print(f"\n📋 DÉTAIL DES SESSIONS:")
        for session in sessions[:5]:  # Afficher les 5 plus récentes
            status_emoji = {'created': '⚪', 'running': '🟢', 'stopped': '🔴'}.get(session['status'], '❓')
            print(f"{status_emoji} {session['session_id'][:20]}... - {session['strategy_name']}")
            if 'performance' in session:
                perf = session['performance']
                print(f"   Return: {perf['total_return_percent']:.2f}%, Trades: {perf['total_trades']}")

def main():
    """Fonction principale des exemples"""
    setup_logging()
    
    print("🧪 EXEMPLES D'UTILISATION DU PAPER TRADING")
    print("="*80)
    
    try:
        # Exemple 1: Session basique
        session1 = example_basic_session()
        
        # Exemple 2: Sessions multiples
        sessions2 = example_multiple_sessions()
        
        # Exemple 3: Stratégie personnalisée
        session3 = example_custom_strategy()
        
        # Exemple 4: Données réelles (peut échouer sans API)
        session4 = example_real_data_simulation()
        
        # Résumé final
        show_session_summary()
        
        print("\n✅ Tous les exemples ont été exécutés!")
        print("💡 Consultez les fichiers de résultats dans assets/results/paper_trading/")
        
        # Nettoyer les sessions de test
        cleanup = input("\n🧹 Nettoyer les sessions de test ? (y/N): ").lower().strip()
        if cleanup in ['y', 'yes', 'oui']:
            session_manager.cleanup_old_sessions(max_age_hours=0)  # Supprimer toutes
            print("🧹 Sessions nettoyées")
        
    except KeyboardInterrupt:
        print("\n🛑 Exemples interrompus par l'utilisateur")
        session_manager.stop_all_sessions()
    except Exception as e:
        print(f"\n❌ Erreur inattendue: {e}")

if __name__ == "__main__":
    main()
