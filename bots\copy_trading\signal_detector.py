"""
📡 Détecteur de signaux de trading
Analyse en temps réel des transactions pour générer des signaux de copy trading
"""

import asyncio
import time
import json
from decimal import Decimal, ROUND_DOWN
from typing import Dict, List, Optional, Tuple, Any, Set
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import defaultdict, deque
import numpy as np
import logging
from pathlib import Path
import sys

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent.parent))

from bots.copy_trading.wallet_analyzer import WalletAnalyzer, WalletTransaction, WalletPerformance, WalletSignal
from utils.error_handler import error_handler, ErrorCategory, ErrorSeverity
from logging_system.central_logger import central_logger, LogCategory

@dataclass
class SignalConfig:
    """Configuration du détecteur de signaux"""
    # Seuils de détection
    min_trade_size_usd: float = 1000.0
    max_trade_size_usd: float = 100000.0
    min_wallet_performance_score: float = 0.7
    
    # Filtres de qualité
    min_wallet_trades: int = 20
    min_win_rate: float = 60.0  # %
    max_risk_score: float = 0.8
    min_consistency_score: float = 0.6
    
    # Timing
    signal_timeout_minutes: int = 30
    max_signals_per_wallet_per_hour: int = 5
    
    # Tokens autorisés
    allowed_tokens: Set[str] = field(default_factory=lambda: {
        'WETH', 'WBTC', 'USDC', 'USDT', 'DAI', 'LINK', 'UNI', 'AAVE', 'COMP', 'MKR'
    })
    
    # Tokens interdits (meme coins, etc.)
    blacklisted_tokens: Set[str] = field(default_factory=lambda: {
        'SHIB', 'DOGE', 'PEPE', 'FLOKI'
    })

@dataclass
class MarketContext:
    """Contexte de marché pour un signal"""
    token_symbol: str
    current_price: float
    price_change_24h: float
    volume_24h: float
    market_cap: float
    volatility: float
    trend: str  # 'bullish', 'bearish', 'neutral'
    support_level: Optional[float]
    resistance_level: Optional[float]

class SignalDetector:
    """Détecteur de signaux de copy trading"""
    
    def __init__(self, wallet_analyzer: WalletAnalyzer, config: SignalConfig = None):
        self.logger = logging.getLogger(__name__)
        self.wallet_analyzer = wallet_analyzer
        self.config = config or SignalConfig()
        
        # Signaux actifs et historique
        self.active_signals: Dict[str, WalletSignal] = {}
        self.signal_history: deque = deque(maxlen=10000)
        
        # Cache des transactions récentes par wallet
        self.recent_transactions: Dict[str, deque] = defaultdict(lambda: deque(maxlen=50))
        
        # Limitation des signaux par wallet
        self.wallet_signal_counts: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        
        # Cache du contexte de marché
        self.market_context_cache: Dict[str, MarketContext] = {}
        self.market_cache_ttl = 300  # 5 minutes
        self.last_market_update = 0
        
        # Statistiques
        self.total_signals_detected = 0
        self.signals_by_wallet: Dict[str, int] = defaultdict(int)
        self.signals_by_token: Dict[str, int] = defaultdict(int)
        
        central_logger.log(
            level="INFO",
            message="Détecteur de signaux initialisé",
            category=LogCategory.STRATEGY,
            min_trade_size=self.config.min_trade_size_usd,
            allowed_tokens=len(self.config.allowed_tokens)
        )
    
    async def monitor_wallets(self):
        """Surveille les wallets en continu pour détecter les signaux"""
        while True:
            try:
                # Récupérer les wallets à surveiller
                tracked_wallets = self.wallet_analyzer.get_tracked_wallets()
                
                for wallet_address, wallet_info in tracked_wallets.items():
                    if not wallet_info.get('is_active', True):
                        continue
                    
                    # Vérifier les nouvelles transactions
                    await self._check_wallet_for_signals(wallet_address)
                
                # Nettoyer les signaux expirés
                self._cleanup_expired_signals()
                
                # Attendre avant le prochain cycle
                await asyncio.sleep(10)  # Vérifier toutes les 10 secondes
                
            except Exception as e:
                error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.MEDIUM, {
                    'function': 'monitor_wallets'
                })
                await asyncio.sleep(30)  # Pause plus longue en cas d'erreur
    
    async def _check_wallet_for_signals(self, wallet_address: str):
        """Vérifie un wallet pour de nouveaux signaux"""
        try:
            # Récupérer les performances du wallet
            performance = self.wallet_analyzer.get_wallet_performance(wallet_address)
            if not performance:
                return
            
            # Vérifier si le wallet est éligible
            if not self._is_wallet_eligible(performance):
                return
            
            # Simuler la récupération de nouvelles transactions
            new_transactions = await self._fetch_recent_transactions(wallet_address)
            
            for transaction in new_transactions:
                # Vérifier si c'est un signal valide
                signal = await self._analyze_transaction_for_signal(
                    transaction, performance
                )
                
                if signal:
                    await self._process_new_signal(signal)
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.MEDIUM, {
                'function': '_check_wallet_for_signals',
                'wallet_address': wallet_address
            })
    
    def _is_wallet_eligible(self, performance: WalletPerformance) -> bool:
        """Vérifie si un wallet est éligible pour générer des signaux"""
        return (
            performance.total_trades >= self.config.min_wallet_trades and
            performance.win_rate >= self.config.min_win_rate and
            performance.risk_score <= self.config.max_risk_score and
            performance.consistency_score >= self.config.min_consistency_score
        )
    
    async def _fetch_recent_transactions(self, wallet_address: str) -> List[WalletTransaction]:
        """Récupère les transactions récentes d'un wallet (simulation)"""
        try:
            # En production, utiliser des APIs en temps réel ou des websockets
            # Ici on simule de nouvelles transactions
            
            # Probabilité d'avoir une nouvelle transaction
            if np.random.random() > 0.1:  # 10% de chance
                return []
            
            # Simuler une nouvelle transaction
            tokens = list(self.config.allowed_tokens)
            token_in = np.random.choice(tokens)
            token_out = np.random.choice([t for t in tokens if t != token_in])
            
            amount_in = np.random.uniform(0.5, 20.0)
            usd_value = amount_in * np.random.uniform(1500, 4000)
            
            # Vérifier les seuils de taille
            if usd_value < self.config.min_trade_size_usd or usd_value > self.config.max_trade_size_usd:
                return []
            
            transaction = WalletTransaction(
                tx_hash=f"0x{''.join(np.random.choice('0123456789abcdef') for _ in range(64))}",
                timestamp=datetime.now(),
                wallet_address=wallet_address,
                token_in=token_in,
                token_out=token_out,
                amount_in=amount_in,
                amount_out=amount_in * np.random.uniform(0.98, 1.02),
                gas_used=np.random.randint(100000, 300000),
                gas_price=np.random.randint(20, 100) * 10**9,
                dex=np.random.choice(['uniswap_v3', 'sushiswap', '1inch']),
                action='buy' if token_out in ['WETH', 'WBTC'] else 'sell',
                usd_value=usd_value
            )
            
            return [transaction]
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.DATA, ErrorSeverity.MEDIUM, {
                'function': '_fetch_recent_transactions',
                'wallet_address': wallet_address
            })
            return []
    
    async def _analyze_transaction_for_signal(self, transaction: WalletTransaction,
                                            performance: WalletPerformance) -> Optional[WalletSignal]:
        """Analyse une transaction pour générer un signal"""
        try:
            # Vérifications de base
            if not self._is_transaction_valid(transaction):
                return None
            
            # Vérifier les limites de signaux par wallet
            if not self._check_signal_rate_limit(transaction.wallet_address):
                return None
            
            # Récupérer le contexte de marché
            market_context = await self._get_market_context(transaction.token_out)
            
            # Calculer le score de confiance
            confidence_score = self._calculate_signal_confidence(
                transaction, performance, market_context
            )
            
            if confidence_score < 0.6:  # Seuil minimum de confiance
                return None
            
            # Déterminer l'urgence
            urgency = self._determine_signal_urgency(
                transaction, performance, market_context, confidence_score
            )
            
            # Générer les raisons du signal
            reasoning = self._generate_signal_reasoning(
                transaction, performance, market_context
            )
            
            # Créer le signal
            signal = WalletSignal(
                wallet_address=transaction.wallet_address,
                timestamp=transaction.timestamp,
                action=transaction.action,
                token_symbol=transaction.token_out,
                token_address=self._get_token_address(transaction.token_out),
                amount_usd=transaction.usd_value,
                confidence_score=confidence_score,
                urgency=urgency,
                reasoning=reasoning,
                wallet_performance=performance,
                recent_trades=list(self.recent_transactions[transaction.wallet_address]),
                market_context=market_context.__dict__ if market_context else {}
            )
            
            return signal
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.MEDIUM, {
                'function': '_analyze_transaction_for_signal',
                'tx_hash': transaction.tx_hash
            })
            return None
    
    def _is_transaction_valid(self, transaction: WalletTransaction) -> bool:
        """Vérifie si une transaction est valide pour un signal"""
        # Vérifier les tokens autorisés
        if transaction.token_out not in self.config.allowed_tokens:
            return False
        
        # Vérifier les tokens interdits
        if transaction.token_out in self.config.blacklisted_tokens:
            return False
        
        # Vérifier la taille du trade
        if (transaction.usd_value < self.config.min_trade_size_usd or 
            transaction.usd_value > self.config.max_trade_size_usd):
            return False
        
        # Vérifier que ce n'est pas trop récent (éviter les doublons)
        now = datetime.now()
        if (now - transaction.timestamp).total_seconds() > 300:  # Plus de 5 minutes
            return False
        
        return True
    
    def _check_signal_rate_limit(self, wallet_address: str) -> bool:
        """Vérifie les limites de taux de signaux par wallet"""
        now = datetime.now()
        one_hour_ago = now - timedelta(hours=1)
        
        # Compter les signaux de la dernière heure
        recent_signals = [
            timestamp for timestamp in self.wallet_signal_counts[wallet_address]
            if timestamp > one_hour_ago
        ]
        
        return len(recent_signals) < self.config.max_signals_per_wallet_per_hour
    
    async def _get_market_context(self, token_symbol: str) -> Optional[MarketContext]:
        """Récupère le contexte de marché pour un token"""
        try:
            # Vérifier le cache
            current_time = time.time()
            if (token_symbol in self.market_context_cache and 
                current_time - self.last_market_update < self.market_cache_ttl):
                return self.market_context_cache[token_symbol]
            
            # Simuler la récupération de données de marché
            # En production, utiliser des APIs comme CoinGecko, CoinMarketCap, etc.
            
            base_price = {
                'WETH': 2500, 'WBTC': 45000, 'USDC': 1.0, 'USDT': 1.0,
                'LINK': 15, 'UNI': 8, 'AAVE': 120, 'COMP': 80
            }.get(token_symbol, 100)
            
            # Ajouter de la variance
            current_price = base_price * np.random.uniform(0.95, 1.05)
            price_change_24h = np.random.uniform(-10, 10)  # %
            volume_24h = np.random.uniform(10000000, 1000000000)  # USD
            market_cap = current_price * np.random.uniform(1000000, 100000000)
            volatility = np.random.uniform(0.02, 0.15)  # 2-15%
            
            # Déterminer la tendance
            if price_change_24h > 3:
                trend = 'bullish'
            elif price_change_24h < -3:
                trend = 'bearish'
            else:
                trend = 'neutral'
            
            # Niveaux de support/résistance (simulation)
            support_level = current_price * 0.95
            resistance_level = current_price * 1.05
            
            market_context = MarketContext(
                token_symbol=token_symbol,
                current_price=current_price,
                price_change_24h=price_change_24h,
                volume_24h=volume_24h,
                market_cap=market_cap,
                volatility=volatility,
                trend=trend,
                support_level=support_level,
                resistance_level=resistance_level
            )
            
            # Mettre en cache
            self.market_context_cache[token_symbol] = market_context
            self.last_market_update = current_time
            
            return market_context
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.DATA, ErrorSeverity.MEDIUM, {
                'function': '_get_market_context',
                'token_symbol': token_symbol
            })
            return None
    
    def _calculate_signal_confidence(self, transaction: WalletTransaction,
                                   performance: WalletPerformance,
                                   market_context: Optional[MarketContext]) -> float:
        """Calcule le score de confiance d'un signal"""
        try:
            confidence_factors = []
            
            # Facteur 1: Performance du wallet (40%)
            wallet_score = (
                (performance.win_rate / 100) * 0.4 +
                performance.consistency_score * 0.3 +
                (1 - performance.risk_score) * 0.3
            )
            confidence_factors.append(('wallet_performance', wallet_score, 0.4))
            
            # Facteur 2: Taille du trade (20%)
            # Plus le trade est gros (relativement au wallet), plus c'est significatif
            relative_size = transaction.usd_value / performance.average_trade_size if performance.average_trade_size > 0 else 1
            size_score = min(1.0, relative_size / 3.0)  # Normaliser à 3x la taille moyenne
            confidence_factors.append(('trade_size', size_score, 0.2))
            
            # Facteur 3: Contexte de marché (20%)
            market_score = 0.5  # Score neutre par défaut
            if market_context:
                # Favoriser les signaux dans le sens de la tendance
                if ((transaction.action == 'buy' and market_context.trend == 'bullish') or
                    (transaction.action == 'sell' and market_context.trend == 'bearish')):
                    market_score = 0.8
                elif market_context.trend == 'neutral':
                    market_score = 0.6
                else:
                    market_score = 0.3  # Contre-tendance
                
                # Ajuster selon la volatilité
                if market_context.volatility > 0.1:  # Haute volatilité
                    market_score *= 0.8
            
            confidence_factors.append(('market_context', market_score, 0.2))
            
            # Facteur 4: Historique récent du token pour ce wallet (20%)
            token_history_score = self._calculate_token_history_score(
                transaction.wallet_address, transaction.token_out, performance
            )
            confidence_factors.append(('token_history', token_history_score, 0.2))
            
            # Calculer le score final pondéré
            final_score = sum(score * weight for _, score, weight in confidence_factors)
            
            return min(1.0, max(0.0, final_score))
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.LOW, {
                'function': '_calculate_signal_confidence'
            })
            return 0.5
    
    def _calculate_token_history_score(self, wallet_address: str, token_symbol: str,
                                     performance: WalletPerformance) -> float:
        """Calcule le score basé sur l'historique du token pour ce wallet"""
        try:
            # Vérifier les performances passées sur ce token
            token_pnl = performance.token_performance.get(token_symbol, 0)
            token_trades = performance.favorite_tokens.get(token_symbol, 0)
            
            if token_trades == 0:
                return 0.5  # Pas d'historique
            
            # Score basé sur le P&L moyen par trade
            avg_pnl_per_trade = token_pnl / token_trades
            
            if avg_pnl_per_trade > 100:  # Très profitable
                return 0.9
            elif avg_pnl_per_trade > 50:  # Profitable
                return 0.8
            elif avg_pnl_per_trade > 0:  # Légèrement profitable
                return 0.7
            elif avg_pnl_per_trade > -50:  # Légèrement perdant
                return 0.4
            else:  # Très perdant
                return 0.2
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.LOW, {
                'function': '_calculate_token_history_score'
            })
            return 0.5
    
    def _determine_signal_urgency(self, transaction: WalletTransaction,
                                performance: WalletPerformance,
                                market_context: Optional[MarketContext],
                                confidence_score: float) -> str:
        """Détermine l'urgence d'un signal"""
        try:
            urgency_score = 0
            
            # Facteur 1: Confiance élevée = plus urgent
            urgency_score += confidence_score * 40
            
            # Facteur 2: Taille du trade
            relative_size = transaction.usd_value / performance.average_trade_size if performance.average_trade_size > 0 else 1
            if relative_size > 2:  # Trade 2x plus gros que la moyenne
                urgency_score += 20
            elif relative_size > 1.5:
                urgency_score += 10
            
            # Facteur 3: Contexte de marché
            if market_context:
                if abs(market_context.price_change_24h) > 5:  # Mouvement important
                    urgency_score += 15
                if market_context.volatility > 0.08:  # Haute volatilité
                    urgency_score += 10
            
            # Facteur 4: Performance récente du wallet
            if performance.win_rate > 80:  # Très performant
                urgency_score += 15
            
            # Déterminer le niveau d'urgence
            if urgency_score >= 70:
                return 'high'
            elif urgency_score >= 40:
                return 'medium'
            else:
                return 'low'
                
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.LOW, {
                'function': '_determine_signal_urgency'
            })
            return 'medium'
    
    def _generate_signal_reasoning(self, transaction: WalletTransaction,
                                 performance: WalletPerformance,
                                 market_context: Optional[MarketContext]) -> List[str]:
        """Génère les raisons du signal"""
        reasoning = []
        
        # Raisons liées au wallet
        if performance.win_rate > 75:
            reasoning.append(f"Wallet très performant ({performance.win_rate:.1f}% win rate)")
        
        if performance.consistency_score > 0.8:
            reasoning.append("Wallet très consistant dans ses performances")
        
        if performance.risk_score < 0.3:
            reasoning.append("Wallet avec profil de risque faible")
        
        # Raisons liées au trade
        relative_size = transaction.usd_value / performance.average_trade_size if performance.average_trade_size > 0 else 1
        if relative_size > 2:
            reasoning.append(f"Trade {relative_size:.1f}x plus gros que la moyenne")
        
        # Raisons liées au token
        token_pnl = performance.token_performance.get(transaction.token_out, 0)
        token_trades = performance.favorite_tokens.get(transaction.token_out, 0)
        if token_trades > 0 and token_pnl > 0:
            avg_pnl = token_pnl / token_trades
            reasoning.append(f"Historique positif sur {transaction.token_out} (+${avg_pnl:.0f}/trade)")
        
        # Raisons liées au marché
        if market_context:
            if market_context.trend == 'bullish' and transaction.action == 'buy':
                reasoning.append("Trade dans le sens de la tendance haussière")
            elif market_context.trend == 'bearish' and transaction.action == 'sell':
                reasoning.append("Trade dans le sens de la tendance baissière")
            
            if abs(market_context.price_change_24h) > 5:
                reasoning.append(f"Mouvement de prix significatif ({market_context.price_change_24h:+.1f}%)")
        
        return reasoning
    
    def _get_token_address(self, token_symbol: str) -> str:
        """Retourne l'adresse du contrat du token (simulation)"""
        # En production, utiliser une vraie base de données de tokens
        token_addresses = {
            'WETH': '******************************************',
            'WBTC': '******************************************',
            'USDC': '******************************************',
            'USDT': '******************************************',
            'LINK': '******************************************',
            'UNI': '******************************************',
            'AAVE': '******************************************',
            'COMP': '******************************************'
        }
        return token_addresses.get(token_symbol, '******************************************')
    
    async def _process_new_signal(self, signal: WalletSignal):
        """Traite un nouveau signal"""
        try:
            # Générer un ID unique pour le signal
            signal_id = f"{signal.wallet_address}_{signal.token_symbol}_{int(signal.timestamp.timestamp())}"
            
            # Ajouter aux signaux actifs
            self.active_signals[signal_id] = signal
            
            # Ajouter à l'historique
            self.signal_history.append(signal)
            
            # Mettre à jour les statistiques
            self.total_signals_detected += 1
            self.signals_by_wallet[signal.wallet_address] += 1
            self.signals_by_token[signal.token_symbol] += 1
            
            # Ajouter au rate limiting
            self.wallet_signal_counts[signal.wallet_address].append(signal.timestamp)
            
            # Logger le signal
            central_logger.log(
                level="INFO",
                message=f"Nouveau signal détecté: {signal.action} {signal.token_symbol}",
                category=LogCategory.STRATEGY,
                wallet_address=signal.wallet_address,
                action=signal.action,
                token=signal.token_symbol,
                amount_usd=signal.amount_usd,
                confidence=signal.confidence_score,
                urgency=signal.urgency
            )
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.MEDIUM, {
                'function': '_process_new_signal',
                'signal_id': signal_id if 'signal_id' in locals() else 'unknown'
            })
    
    def _cleanup_expired_signals(self):
        """Nettoie les signaux expirés"""
        try:
            current_time = datetime.now()
            timeout = timedelta(minutes=self.config.signal_timeout_minutes)
            
            expired_signals = [
                signal_id for signal_id, signal in self.active_signals.items()
                if current_time - signal.timestamp > timeout
            ]
            
            for signal_id in expired_signals:
                del self.active_signals[signal_id]
            
            if expired_signals:
                central_logger.log(
                    level="DEBUG",
                    message=f"{len(expired_signals)} signaux expirés nettoyés",
                    category=LogCategory.SYSTEM
                )
                
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.LOW, {
                'function': '_cleanup_expired_signals'
            })
    
    def get_active_signals(self, urgency_filter: Optional[str] = None,
                          min_confidence: Optional[float] = None) -> List[WalletSignal]:
        """Retourne les signaux actifs avec filtres optionnels"""
        signals = list(self.active_signals.values())
        
        if urgency_filter:
            signals = [s for s in signals if s.urgency == urgency_filter]
        
        if min_confidence:
            signals = [s for s in signals if s.confidence_score >= min_confidence]
        
        # Trier par confiance et urgence
        def signal_priority(signal):
            urgency_weights = {'high': 3, 'medium': 2, 'low': 1}
            return signal.confidence_score * urgency_weights.get(signal.urgency, 1)
        
        return sorted(signals, key=signal_priority, reverse=True)
    
    def get_signal_statistics(self) -> Dict[str, Any]:
        """Retourne les statistiques du détecteur"""
        return {
            'total_signals_detected': self.total_signals_detected,
            'active_signals_count': len(self.active_signals),
            'signals_by_wallet': dict(self.signals_by_wallet),
            'signals_by_token': dict(self.signals_by_token),
            'top_signal_wallets': sorted(
                self.signals_by_wallet.items(), 
                key=lambda x: x[1], 
                reverse=True
            )[:5],
            'top_signal_tokens': sorted(
                self.signals_by_token.items(), 
                key=lambda x: x[1], 
                reverse=True
            )[:5]
        }
