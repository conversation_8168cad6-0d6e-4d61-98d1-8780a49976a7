#!/usr/bin/env python3
"""
📊 Exemple d'utilisation du dashboard de monitoring
Démontre l'intégration et l'utilisation du dashboard avec des bots simulés
"""

import asyncio
import time
import json
from pathlib import Path
import sys

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from dashboard.dashboard_manager import DashboardManager
from dashboard.metrics_collector import MetricsCollector
from dashboard.alert_system import AlertSystem, AlertRule, AlertLevel, AlertType
from logging_system.central_logger import central_logger, LogCategory

class MockBot:
    """Bot simulé pour les tests du dashboard"""
    
    def __init__(self, bot_id: str, bot_type: str):
        self.bot_id = bot_id
        self.bot_type = bot_type
        self.is_running = True
        self.start_time = time.time()
        
        # Métriques simulées
        self.total_trades = 0
        self.successful_trades = 0
        self.total_pnl = 0.0
        self.total_volume = 0.0
        
        # Simulation d'activité
        self.last_trade_time = time.time()
        
    def get_status(self):
        """Retourne le statut du bot"""
        return {
            'is_running': self.is_running,
            'start_time': self.start_time,
            'bot_type': self.bot_type
        }
    
    def get_performance_stats(self):
        """Retourne les statistiques de performance"""
        win_rate = (self.successful_trades / max(1, self.total_trades)) * 100
        
        return {
            'total_executions': self.total_trades,
            'successful_executions': self.successful_trades,
            'success_rate': win_rate,
            'net_profit': self.total_pnl,
            'total_volume': self.total_volume,
            'average_execution_time_seconds': 2.5
        }
    
    async def simulate_activity(self):
        """Simule l'activité du bot"""
        while self.is_running:
            # Simuler un trade de temps en temps
            if time.time() - self.last_trade_time > 30:  # Toutes les 30 secondes
                self.total_trades += 1
                
                # 70% de chance de succès
                if time.time() % 10 < 7:
                    self.successful_trades += 1
                    profit = 50 + (time.time() % 100)  # Profit aléatoire
                    self.total_pnl += profit
                else:
                    loss = -(20 + (time.time() % 50))  # Perte aléatoire
                    self.total_pnl += loss
                
                self.total_volume += 1000 + (time.time() % 5000)
                self.last_trade_time = time.time()
            
            await asyncio.sleep(5)

async def test_metrics_collector():
    """Teste le collecteur de métriques"""
    print("\n📈 Test du collecteur de métriques")
    print("="*50)
    
    try:
        collector = MetricsCollector()
        
        # Créer des bots simulés
        bots = [
            MockBot("scalping_bot_1", "dex_scalping"),
            MockBot("arbitrage_bot_1", "cross_chain_arbitrage"),
            MockBot("copy_bot_1", "copy_trading")
        ]
        
        # Enregistrer les bots
        print("🤖 Enregistrement des bots...")
        for bot in bots:
            collector.register_bot(bot.bot_id, bot.bot_type, bot)
            print(f"   ✅ {bot.bot_id} ({bot.bot_type})")
        
        # Démarrer la simulation d'activité
        print("\n⚡ Démarrage de la simulation d'activité...")
        activity_tasks = [asyncio.create_task(bot.simulate_activity()) for bot in bots]
        
        # Démarrer la collecte de métriques
        collection_task = asyncio.create_task(collector.start_collection())
        
        # Laisser tourner pendant 30 secondes
        print("⏳ Collecte de métriques pendant 30 secondes...")
        await asyncio.sleep(30)
        
        # Arrêter les simulations
        for bot in bots:
            bot.is_running = False
        
        for task in activity_tasks:
            task.cancel()
        
        collection_task.cancel()
        
        try:
            await asyncio.gather(*activity_tasks, collection_task, return_exceptions=True)
        except:
            pass
        
        # Afficher les résultats
        print("\n📊 Résultats de la collecte:")
        bot_metrics = collector.get_bot_metrics()
        
        for bot_id, metrics in bot_metrics.items():
            print(f"\n🤖 {bot_id}:")
            print(f"   📊 Trades: {metrics.total_trades}")
            print(f"   🎯 Win rate: {metrics.win_rate:.1f}%")
            print(f"   💰 P&L: ${metrics.total_pnl:.2f}")
            print(f"   📈 Volume: ${metrics.total_volume:.0f}")
            print(f"   ⏱️ Uptime: {metrics.uptime_seconds:.0f}s")
        
        # Métriques système
        system_metrics = collector.get_system_metrics()
        if system_metrics:
            print(f"\n🖥️ Système:")
            print(f"   💰 Portefeuille: ${system_metrics.total_portfolio_value:.2f}")
            print(f"   📈 P&L quotidien: ${system_metrics.total_daily_pnl:.2f}")
            print(f"   🤖 Bots actifs: {system_metrics.active_bots}")
            print(f"   🏥 Santé: {system_metrics.system_health_score:.2f}")
        
        print("\n✅ Test collecteur réussi!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur test collecteur: {e}")
        return False

async def test_alert_system():
    """Teste le système d'alertes"""
    print("\n🚨 Test du système d'alertes")
    print("="*50)
    
    try:
        collector = MetricsCollector()
        alert_system = AlertSystem(collector)
        
        # Créer un bot avec des métriques problématiques
        problem_bot = MockBot("problem_bot", "test")
        problem_bot.is_running = False  # Bot arrêté
        problem_bot.total_trades = 20
        problem_bot.successful_trades = 5  # Mauvais win rate
        
        collector.register_bot("problem_bot", "test", problem_bot)
        
        # Forcer la collecte de métriques
        await collector._collect_bot_metrics()
        
        # Vérifier les règles d'alerte
        print("🔍 Vérification des règles d'alerte...")
        await alert_system._check_bot_rules()
        
        # Afficher les alertes générées
        active_alerts = alert_system.get_active_alerts()
        print(f"\n📊 {len(active_alerts)} alertes générées:")
        
        for alert in active_alerts:
            level_icon = {"info": "ℹ️", "warning": "⚠️", "critical": "🚨", "emergency": "🆘"}
            icon = level_icon.get(alert.level.value, "⚠️")
            
            print(f"\n{icon} {alert.title}")
            print(f"   📝 {alert.message}")
            print(f"   🕐 {alert.timestamp.strftime('%H:%M:%S')}")
            print(f"   🤖 Bot: {alert.bot_id or 'Système'}")
            print(f"   📊 Niveau: {alert.level.value}")
        
        # Tester l'acquittement d'alerte
        if active_alerts:
            first_alert = active_alerts[0]
            print(f"\n✅ Test d'acquittement de l'alerte: {first_alert.id}")
            success = alert_system.acknowledge_alert(first_alert.id)
            print(f"   {'✅' if success else '❌'} Acquittement: {success}")
        
        # Statistiques des alertes
        stats = alert_system.get_alert_statistics()
        print(f"\n📈 Statistiques alertes:")
        print(f"   Alertes actives: {stats['active_alerts_count']}")
        print(f"   Règles configurées: {stats['rules_count']}")
        print(f"   Règles actives: {stats['enabled_rules_count']}")
        
        print("\n✅ Test système d'alertes réussi!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur test alertes: {e}")
        return False

async def test_dashboard_integration():
    """Teste l'intégration complète du dashboard"""
    print("\n📊 Test d'intégration du dashboard")
    print("="*50)
    
    try:
        # Créer le gestionnaire de dashboard
        dashboard = DashboardManager(host="127.0.0.1", port=8081)
        
        # Créer des bots simulés
        bots = [
            MockBot("scalping_eth_usdc", "dex_scalping"),
            MockBot("arbitrage_stable", "cross_chain_arbitrage"),
            MockBot("copy_whale_1", "copy_trading")
        ]
        
        # Enregistrer les bots
        print("🤖 Enregistrement des bots dans le dashboard...")
        for bot in bots:
            dashboard.register_bot(bot.bot_id, bot.bot_type, bot)
            print(f"   ✅ {bot.bot_id}")
        
        # Démarrer la simulation d'activité
        print("\n⚡ Démarrage de la simulation...")
        activity_tasks = [asyncio.create_task(bot.simulate_activity()) for bot in bots]
        
        # Démarrer le dashboard en arrière-plan
        dashboard_task = asyncio.create_task(dashboard.start())
        
        # Laisser tourner pendant 20 secondes
        print("⏳ Dashboard en cours d'exécution...")
        print(f"🌐 Interface disponible sur: http://127.0.0.1:8081")
        print("📊 API disponible sur: http://127.0.0.1:8081/api/summary")
        
        await asyncio.sleep(20)
        
        # Tester les API
        print("\n🔍 Test des métriques du dashboard...")
        status = dashboard.get_dashboard_status()
        print(f"   📊 Statut: {'✅ En cours' if status['is_running'] else '❌ Arrêté'}")
        print(f"   🤖 Bots enregistrés: {status['metrics_collector']['registered_bots']}")
        print(f"   📈 Métriques actives: {status['metrics_collector']['active_metrics']}")
        print(f"   🚨 Alertes actives: {status['alert_system']['active_alerts_count']}")
        
        # Résumé de performance
        summary = dashboard.get_performance_summary()
        print(f"\n📈 Résumé de performance:")
        print(f"   💰 Valeur portefeuille: ${summary['system']['portfolio_value']:.2f}")
        print(f"   📊 P&L quotidien: ${summary['system']['daily_pnl']:.2f}")
        print(f"   🤖 Bots actifs: {summary['trading']['running_bots']}/{summary['trading']['total_bots']}")
        print(f"   📈 Trades totaux: {summary['trading']['total_trades']}")
        print(f"   🎯 Win rate moyen: {summary['trading']['average_win_rate']:.1f}%")
        
        # Envoyer une alerte de test
        print(f"\n🧪 Test d'alerte...")
        test_success = await dashboard.send_test_alert("warning", "Test d'intégration du dashboard")
        print(f"   {'✅' if test_success else '❌'} Alerte de test envoyée")
        
        # Arrêter tout
        print(f"\n🛑 Arrêt du dashboard...")
        
        # Arrêter les bots
        for bot in bots:
            bot.is_running = False
        
        # Annuler les tâches
        for task in activity_tasks:
            task.cancel()
        
        # Arrêter le dashboard
        await dashboard.stop()
        dashboard_task.cancel()
        
        try:
            await asyncio.gather(*activity_tasks, dashboard_task, return_exceptions=True)
        except:
            pass
        
        print("✅ Test d'intégration réussi!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur test intégration: {e}")
        return False

async def demo_dashboard_features():
    """Démontre les fonctionnalités du dashboard"""
    print("\n🎯 Démonstration des fonctionnalités")
    print("="*50)
    
    try:
        print("📊 Fonctionnalités du dashboard:")
        print("   ✅ Collecte de métriques en temps réel")
        print("   ✅ Interface web moderne avec graphiques")
        print("   ✅ WebSocket pour mises à jour temps réel")
        print("   ✅ Système d'alertes intelligent")
        print("   ✅ API REST complète")
        print("   ✅ Monitoring multi-bots")
        print("   ✅ Métriques système et performance")
        
        print(f"\n🌐 Endpoints API disponibles:")
        endpoints = [
            "GET /api/health - Santé de l'API",
            "GET /api/summary - Résumé global",
            "GET /api/bots - Liste des bots",
            "GET /api/bots/{id} - Détails d'un bot",
            "GET /api/system - Métriques système",
            "GET /api/metrics/{name} - Historique métrique",
            "GET /api/analytics - Analytics de performance",
            "GET /api/alerts - Alertes actives",
            "POST /api/alerts/{id}/acknowledge - Acquitter alerte",
            "WebSocket /ws - Mises à jour temps réel"
        ]
        
        for endpoint in endpoints:
            print(f"   📡 {endpoint}")
        
        print(f"\n🚨 Types d'alertes supportées:")
        alert_types = [
            "Santé système critique",
            "Utilisation CPU/mémoire élevée",
            "Bot arrêté de manière inattendue",
            "Taux d'erreur élevé",
            "Performance dégradée",
            "Perte quotidienne importante",
            "Drawdown élevé"
        ]
        
        for alert_type in alert_types:
            print(f"   ⚠️ {alert_type}")
        
        print(f"\n📈 Métriques collectées:")
        metrics = [
            "Trades totaux et taux de réussite",
            "P&L total et quotidien",
            "Volume de trading",
            "Temps d'exécution moyen",
            "Drawdown et Sharpe ratio",
            "Utilisation CPU/mémoire/disque",
            "Santé système globale",
            "Uptime des bots"
        ]
        
        for metric in metrics:
            print(f"   📊 {metric}")
        
        print("\n✅ Démonstration terminée!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur démonstration: {e}")
        return False

async def main():
    """Fonction principale des exemples"""
    print("📊 EXEMPLES DASHBOARD DE MONITORING")
    print("="*80)
    
    try:
        print("🧪 Démarrage des tests...")
        
        # Test 1: Collecteur de métriques
        if not await test_metrics_collector():
            print("❌ Test collecteur échoué")
            return
        
        # Test 2: Système d'alertes
        if not await test_alert_system():
            print("❌ Test alertes échoué")
            return
        
        # Test 3: Intégration complète
        if not await test_dashboard_integration():
            print("❌ Test intégration échoué")
            return
        
        # Démonstration 4: Fonctionnalités
        if not await demo_dashboard_features():
            print("❌ Démonstration échouée")
            return
        
        print("\n" + "="*80)
        print("✅ Tous les tests ont réussi!")
        print("💡 Le dashboard de monitoring est prêt à être utilisé")
        
        print("\n📋 Instructions de démarrage:")
        print("1. Installer les dépendances: pip install fastapi uvicorn websockets")
        print("2. Démarrer le dashboard: python dashboard/dashboard_manager.py")
        print("3. Ouvrir le navigateur: http://localhost:8080")
        print("4. Enregistrer vos bots avec: dashboard_manager.register_bot()")
        
        print("\n⚠️ Rappels importants:")
        print("• Le dashboard collecte les métriques toutes les 30 secondes")
        print("• Les alertes sont vérifiées en continu")
        print("• L'interface web se met à jour automatiquement")
        print("• Les métriques sont conservées 24h")
        print("• Configurez les notifications Telegram/Email")
        
    except KeyboardInterrupt:
        print("\n🛑 Tests interrompus par l'utilisateur")
    except Exception as e:
        print(f"\n❌ Erreur inattendue: {e}")
        central_logger.log(
            level="ERROR",
            message=f"Erreur dans les exemples: {e}",
            category=LogCategory.ERROR
        )

if __name__ == "__main__":
    asyncio.run(main())
