# 🤖 BotCrypto - Système de Trading Automatisé

## 📋 Description
BotCrypto est un système complet de trading automatisé de cryptomonnaies avec support multi-exchange, gestion avancée des risques, et interface de monitoring en temps réel.

## ✨ Fonctionnalités Principales

### 🎯 Bots de Trading
- **Safe Bot** : Grid trading sécurisé avec capital limité
- **Sniper <PERSON>** : Achat rapide de nouveaux tokens
- **Grid Bot v2** : Trading avancé avec gestion des risques
- **Copy Trading** : Réplication de trades d'autres wallets
- **Cross-Chain Arbitrage** : Arbitrage entre différentes blockchains
- **DEX Scalping** : Scalping sur les exchanges décentralisés

### 📊 Monitoring & Analytics
- Dashboard web en temps réel
- Métriques de performance détaillées
- Système d'alertes Telegram
- Audit trail complet
- Backtesting avancé

### 🛡️ Sécurité & Gestion des Risques
- Gestion avancée des risques
- Stop-loss et take-profit automatiques
- Corrélation de portefeuille
- Validation de sécurité intégrée
- Paper trading pour les tests

## 🚀 Installation Rapide

### 1. Prérequis
```bash
# Python 3.8+
python --version

# Git
git --version
```

### 2. Installation
```bash
# Cloner le repository
git clone https://github.com/votre-username/botCrypto.git
cd botCrypto

# Installer les dépendances
pip install -r requirements.txt

# Configurer l'environnement
cp .env.example .env.local
# Éditer .env.local avec vos clés API
```

### 3. Configuration
Éditez `.env.local` avec vos clés API :
```bash
# Binance API Keys
safe_bot_PROD_API_KEY=votre_cle_production
safe_bot_PROD_API_SECRET=votre_secret_production

# Testnet Keys (pour les tests)
safe_bot_TEST_API_KEY=votre_cle_testnet
safe_bot_TEST_API_SECRET=votre_secret_testnet

# Telegram (optionnel)
TELEGRAM_BOT_TOKEN=votre_token_telegram
TELEGRAM_CHAT_ID=votre_chat_id
```

## 🎮 Utilisation

### Lancement Rapide
```bash
# Interface principale
python launcher.py

# Test des connexions
python tools/test_runner.py

# Safe Bot (testnet - recommandé pour débuter)
python bots/safe_bot.py --mode testnet

# Safe Bot (production - argent réel)
python bots/safe_bot.py --mode production
```

### Modes Disponibles
- **Testnet** : Trading avec argent fictif (recommandé pour débuter)
- **Production** : Trading avec argent réel
- **Paper Trading** : Simulation pure sans API

## 📁 Structure du Projet

```
botCrypto/
├── launcher.py              # 🚀 Interface principale
├── bots/                    # 🤖 Bots de trading
│   ├── safe_bot.py         #   ├── Safe Bot unifié
│   ├── sniper_bot_v2.py    #   ├── Sniper Bot
│   ├── grid_bot_v2.py      #   └── Grid Bot avancé
├── tools/                   # 🧪 Utilitaires
│   └── test_runner.py      #   └── Tests de connexion
├── config/                  # ⚙️ Configuration
├── dashboard/               # 📊 Interface web
├── monitoring/              # 📈 Monitoring
├── backtesting/            # 📉 Backtesting
├── paper_trading/          # 🎮 Trading simulé
├── risk_management/        # 🛡️ Gestion des risques
├── utils/                  # 🔧 Utilitaires
├── docs/                   # 📚 Documentation
└── examples/               # 💡 Exemples
```

## 🧪 Tests et Validation

```bash
# Tests complets
python tools/test_runner.py

# Test connexion production
python bots/safe_bot.py --mode testnet

# Validation sécurité
python tools/test_runner.py
```

## 📊 Monitoring

### Dashboard Web
```bash
python dashboard/web_server.py
# Accès : http://localhost:8080
```

### Métriques
- Performance en temps réel
- Historique des trades
- Analyse des risques
- Alertes automatiques

## 🛡️ Sécurité

### Bonnes Pratiques
- ✅ Utilisez toujours le testnet en premier
- ✅ Limitez le capital de trading
- ✅ Surveillez les bots en permanence
- ✅ Gardez vos clés API sécurisées
- ✅ Activez les notifications Telegram

### Fichiers Sensibles
```bash
.env.local          # Clés API (jamais commité)
private-docs/       # Documentation privée
*.log              # Logs de trading
```

## 📈 Stratégies Disponibles

### Safe Bot
- **Grid Trading** sécurisé
- Capital limité (10€ par défaut)
- Stop-loss automatique
- Idéal pour débuter

### Sniper Bot
- Achat rapide de nouveaux tokens
- Analyse automatique des contrats
- Protection anti-rug pull
- Nécessite wallet crypto

### Grid Bot v2
- Trading avancé avec grille
- Gestion multi-paires
- Optimisation automatique
- Analytics détaillées

## 🔧 Configuration Avancée

### Paramètres de Trading
```python
# Dans bots/safe_bot.py
CAPITAL_MAX = 10        # Capital maximum (USDT)
GRID_SPACING = 100      # Espacement de grille
ORDER_SIZE = 0.001      # Taille d'ordre (BTC)
STOP_LOSS_PERCENT = 5   # Stop-loss (%)
```

### Notifications Telegram
1. Créer un bot via @BotFather
2. Obtenir le token et chat ID
3. Configurer dans `.env.local`

## 📚 Documentation

- [Guide de Démarrage](docs/getting-started.md)
- [Configuration des Bots](docs/bot-configuration.md)
- [API Reference](docs/api-reference.md)
- [Guide de Déploiement](docs/deployment-guide.md)

## 🤝 Support

### Problèmes Courants
- **Clés API invalides** : Vérifiez vos permissions Binance
- **Connexion échouée** : Testez avec `tools/test_runner.py`
- **Ordres rejetés** : Vérifiez vos balances et limites

### Logs
```bash
# Logs des bots
tail -f safe_bot_testnet.log
tail -f safe_bot_production.log

# Logs du système
tail -f dashboard/dashboard.log
```

## ⚠️ Avertissements

- **Trading = Risque** : Ne tradez que ce que vous pouvez vous permettre de perdre
- **Testez d'abord** : Utilisez toujours le testnet avant la production
- **Surveillez** : Les bots nécessitent une surveillance constante
- **Sécurité** : Gardez vos clés API privées et sécurisées

## 📄 Licence

Ce projet est sous licence MIT. Voir [LICENSE](LICENSE) pour plus de détails.

## 🚀 Roadmap

- [ ] Support Ethereum/DeFi
- [ ] IA pour optimisation des stratégies
- [ ] Interface mobile
- [ ] Support multi-exchange étendu
- [ ] Marketplace de stratégies

---

**⚡ Commencez dès maintenant :**
```bash
python launcher.py
```
