#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 Fix Optimized Bot Encoding
Corrige spécifiquement les problèmes d'encodage du bot optimisé
"""

import os
import sys
import re

def fix_optimized_bot():
    """Corrige l'encodage du bot optimisé"""
    
    bot_file = "bots/optimized_safe_bot.py"
    
    if not os.path.exists(bot_file):
        print(f"❌ Fichier {bot_file} non trouvé")
        return False
    
    print(f"🔧 Correction de {bot_file}...")
    
    try:
        # Lire le fichier
        with open(bot_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Ajouter les imports nécessaires au début
        if "import sys" not in content:
            content = content.replace("import os", "import os\nimport sys")
        
        # Remplacer toutes les occurrences de self.logger.info avec emojis
        emoji_patterns = [
            (r'self\.logger\.info\(f?"([^"]*[🚀✅❌⚠️📊💰🎯📈📉🔧⏸️🛑💡🔍📝🎉⏱️🧪🔐📦🌐💾🏦🎮📁🔄⚡🎲][^"]*)"', 
             r'self.safe_log("info", f"\1")'),
            (r'self\.logger\.warning\(f?"([^"]*[🚀✅❌⚠️📊💰🎯📈📉🔧⏸️🛑💡🔍📝🎉⏱️🧪🔐📦🌐💾🏦🎮📁🔄⚡🎲][^"]*)"', 
             r'self.safe_log("warning", f"\1")'),
            (r'self\.logger\.error\(f?"([^"]*[🚀✅❌⚠️📊💰🎯📈📉🔧⏸️🛑💡🔍📝🎉⏱️🧪🔐📦🌐💾🏦🎮📁🔄⚡🎲][^"]*)"', 
             r'self.safe_log("error", f"\1")'),
        ]
        
        for pattern, replacement in emoji_patterns:
            content = re.sub(pattern, replacement, content)
        
        # Ajouter les méthodes safe_log et make_unicode_safe après setup_advanced_logging
        safe_methods = '''
    def safe_log(self, level, message):
        """Log un message en gérant les erreurs d'encodage"""
        try:
            # Remplacer les emojis par du texte pour éviter les erreurs
            safe_message = self.make_unicode_safe(str(message))
            getattr(self.logger, level)(safe_message)
        except UnicodeEncodeError:
            # Fallback: message sans caractères spéciaux
            ascii_message = str(message).encode('ascii', 'ignore').decode('ascii')
            getattr(self.logger, level)(f"[UNICODE_ERROR] {ascii_message}")
        except Exception as e:
            # Dernier recours
            print(f"[LOG_ERROR] {message}")
    
    def make_unicode_safe(self, text):
        """Remplace les emojis par du texte lisible"""
        emoji_replacements = {
            '🚀': '[START]', '✅': '[OK]', '❌': '[ERROR]', '⚠️': '[WARNING]',
            '📊': '[STATS]', '💰': '[MONEY]', '🎯': '[TARGET]', '📈': '[UP]',
            '📉': '[DOWN]', '🔧': '[CONFIG]', '⏸️': '[PAUSE]', '🛑': '[STOP]',
            '💡': '[INFO]', '🔍': '[SEARCH]', '📝': '[LOG]', '🎉': '[SUCCESS]',
            '⏱️': '[TIME]', '🧪': '[TEST]', '🔐': '[SECURE]', '📦': '[PACKAGE]',
            '🌐': '[NETWORK]', '💾': '[SAVE]', '🏦': '[BANK]', '🎮': '[GAME]',
            '📁': '[FOLDER]', '🔄': '[REFRESH]', '⚡': '[FAST]', '🎲': '[RANDOM]'
        }
        
        safe_text = str(text)
        for emoji, replacement in emoji_replacements.items():
            safe_text = safe_text.replace(emoji, replacement)
        
        return safe_text
'''
        
        # Insérer les méthodes après setup_advanced_logging
        if "def safe_log" not in content:
            # Trouver la fin de setup_advanced_logging
            pattern = r'(def setup_advanced_logging\(self\):.*?print\(f"📝 Logging avancé configuré: \{log_file\}"\))'
            match = re.search(pattern, content, re.DOTALL)
            if match:
                end_pos = match.end()
                content = content[:end_pos] + safe_methods + content[end_pos:]
        
        # Modifier setup_advanced_logging pour ajouter l'encodage UTF-8
        logging_fix = '''    def setup_advanced_logging(self):
        """Configure le logging rotatif avancé avec support Unicode"""
        log_size_mb = int(os.getenv("LOG_ROTATION_SIZE", 10))
        retention_days = int(os.getenv("LOG_RETENTION_DAYS", 7))
        detailed_logging = os.getenv("DETAILED_LOGGING", "true").lower() == "true"
        
        # Configuration Unicode pour Windows
        if sys.platform.startswith('win'):
            os.environ['PYTHONIOENCODING'] = 'utf-8'
            if hasattr(sys.stdout, 'reconfigure'):
                try:
                    sys.stdout.reconfigure(encoding='utf-8')
                    sys.stderr.reconfigure(encoding='utf-8')
                except:
                    pass
        
        # Logger principal avec rotation et encodage UTF-8
        log_file = f"{self.log_prefix}.log"
        handler = RotatingFileHandler(
            log_file, 
            maxBytes=log_size_mb * 1024 * 1024,  # MB to bytes
            backupCount=retention_days,
            encoding='utf-8'  # Forcer l'encodage UTF-8
        )
        
        # Format détaillé
        if detailed_logging:
            formatter = logging.Formatter(
                '%(asctime)s - %(levelname)s - [%(funcName)s:%(lineno)d] - %(message)s'
            )
        else:
            formatter = logging.Formatter(
                '%(asctime)s - %(levelname)s - %(message)s'
            )
        
        handler.setFormatter(formatter)
        
        # Configuration du logger
        self.logger = logging.getLogger(f"OptimizedSafeBot_{self.mode}")
        self.logger.setLevel(logging.INFO)
        self.logger.addHandler(handler)
        
        # Logger de performance séparé avec encodage UTF-8
        perf_handler = RotatingFileHandler(
            f"{self.log_prefix}_performance.log",
            maxBytes=5 * 1024 * 1024,  # 5MB
            backupCount=3,
            encoding='utf-8'  # Forcer l'encodage UTF-8
        )
        perf_formatter = logging.Formatter(
            '%(asctime)s - PERF - %(message)s'
        )
        perf_handler.setFormatter(perf_formatter)
        
        self.perf_logger = logging.getLogger(f"Performance_{self.mode}")
        self.perf_logger.setLevel(logging.INFO)
        self.perf_logger.addHandler(perf_handler)
        
        print(f"[LOG] Logging avancé configuré: {log_file}")'''
        
        # Remplacer la méthode setup_advanced_logging
        pattern = r'def setup_advanced_logging\(self\):.*?print\(f"📝 Logging avancé configuré: \{log_file\}"\)'
        content = re.sub(pattern, logging_fix, content, flags=re.DOTALL)
        
        # Sauvegarder le fichier corrigé
        with open(bot_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ {bot_file} corrigé avec succès")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la correction: {e}")
        return False

def main():
    """Fonction principale"""
    print("🔧 CORRECTION DU BOT OPTIMISÉ")
    print("=" * 40)
    
    if fix_optimized_bot():
        print("\n✅ CORRECTION TERMINÉE")
        print("\n🚀 COMMANDES POUR TESTER:")
        print("set PYTHONIOENCODING=utf-8")
        print("python quick_test.py --test-1h")
    else:
        print("\n❌ CORRECTION ÉCHOUÉE")

if __name__ == "__main__":
    main()
