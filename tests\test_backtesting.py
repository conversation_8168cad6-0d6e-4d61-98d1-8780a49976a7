#!/usr/bin/env python3
"""
🧪 Tests unitaires pour le système de backtesting
Tests pour valider les fonctionnalités de backtesting
"""

import sys
import unittest
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from backtesting.engine import BacktestEngine
from backtesting.data_provider import DataProvider
from backtesting.metrics import PerformanceMetrics
from backtesting.strategies import TradingStrategies

class TestBacktestEngine(unittest.TestCase):
    """Tests pour le moteur de backtesting"""
    
    def setUp(self):
        """Configuration des tests"""
        self.engine = BacktestEngine(initial_capital=1000)
        
        # Créer des données de test
        dates = pd.date_range(start='2024-01-01', periods=100, freq='H')
        np.random.seed(42)  # Pour des résultats reproductibles
        
        # Générer des prix avec une tendance légèrement haussière
        prices = 50000 + np.cumsum(np.random.randn(100) * 100)
        
        self.test_data = pd.DataFrame({
            'open': prices,
            'high': prices * 1.01,
            'low': prices * 0.99,
            'close': prices,
            'volume': np.random.randint(100, 1000, 100)
        }, index=dates)
    
    def test_engine_initialization(self):
        """Test de l'initialisation du moteur"""
        self.assertEqual(self.engine.initial_capital, 1000)
        self.assertEqual(self.engine.current_capital, 1000)
        self.assertEqual(len(self.engine.positions), 0)
        self.assertEqual(len(self.engine.trades), 0)
    
    def test_execute_trade_buy(self):
        """Test d'exécution d'un trade d'achat"""
        symbol = 'BTC/USDT'
        price = 50000
        quantity = 0.01
        timestamp = datetime.now()
        
        # Mettre à jour les prix
        self.engine.update_prices({symbol: price}, timestamp)
        
        # Exécuter un achat
        success = self.engine.execute_trade(symbol, 'buy', quantity, price, timestamp)
        
        self.assertTrue(success)
        self.assertEqual(len(self.engine.trades), 1)
        self.assertAlmostEqual(self.engine.positions[symbol], quantity, places=6)
        self.assertLess(self.engine.current_capital, 1000)  # Capital réduit
    
    def test_execute_trade_sell(self):
        """Test d'exécution d'un trade de vente"""
        symbol = 'BTC/USDT'
        price = 50000
        quantity = 0.01
        timestamp = datetime.now()
        
        # Mettre à jour les prix
        self.engine.update_prices({symbol: price}, timestamp)
        
        # D'abord acheter
        self.engine.execute_trade(symbol, 'buy', quantity, price, timestamp)
        initial_capital = self.engine.current_capital
        
        # Puis vendre
        success = self.engine.execute_trade(symbol, 'sell', quantity, price * 1.1, timestamp)
        
        self.assertTrue(success)
        self.assertEqual(len(self.engine.trades), 2)
        self.assertEqual(self.engine.positions[symbol], 0)
        self.assertGreater(self.engine.current_capital, initial_capital)  # Profit
    
    def test_insufficient_capital(self):
        """Test avec capital insuffisant"""
        symbol = 'BTC/USDT'
        price = 50000
        quantity = 1  # Trop gros pour le capital disponible
        timestamp = datetime.now()
        
        # Mettre à jour les prix
        self.engine.update_prices({symbol: price}, timestamp)
        
        # Tenter un achat trop important
        success = self.engine.execute_trade(symbol, 'buy', quantity, price, timestamp)
        
        self.assertFalse(success)
        self.assertEqual(len(self.engine.trades), 0)
        self.assertEqual(self.engine.current_capital, 1000)
    
    def test_portfolio_value_calculation(self):
        """Test du calcul de la valeur du portefeuille"""
        symbol = 'BTC/USDT'
        price = 50000
        quantity = 0.01
        timestamp = datetime.now()
        
        # Mettre à jour les prix
        self.engine.update_prices({symbol: price}, timestamp)
        
        # Acheter
        self.engine.execute_trade(symbol, 'buy', quantity, price, timestamp)
        
        # Calculer la valeur du portefeuille
        portfolio_value = self.engine.get_portfolio_value()
        
        # La valeur devrait être proche du capital initial (moins les frais)
        self.assertAlmostEqual(portfolio_value, 1000, delta=10)
    
    def test_simple_strategy_execution(self):
        """Test d'exécution d'une stratégie simple"""
        def simple_buy_strategy(engine, data, index, symbol):
            """Stratégie qui achète une seule fois"""
            if index == 10 and engine.positions.get(symbol, 0) == 0:
                price = data.iloc[index]['close']
                quantity = 0.01
                engine.execute_trade(symbol, 'buy', quantity, price, 
                                   data.index[index], 'TestStrategy')
        
        # Exécuter la stratégie
        result = self.engine.run_strategy(simple_buy_strategy, self.test_data, 
                                        'BTC/USDT', 'TestStrategy')
        
        # Vérifier les résultats
        self.assertIn('performance', result)
        self.assertIn('trading_metrics', result)
        self.assertIn('equity_curve', result)
        self.assertEqual(result['trading_metrics']['total_trades'], 1)

class TestPerformanceMetrics(unittest.TestCase):
    """Tests pour les métriques de performance"""
    
    def setUp(self):
        """Configuration des tests"""
        self.metrics = PerformanceMetrics()
        
        # Créer des données de test
        dates = pd.date_range(start='2024-01-01', periods=100, freq='H')
        np.random.seed(42)
        
        # Equity curve avec tendance haussière
        equity_values = 1000 + np.cumsum(np.random.randn(100) * 10)
        self.equity_curve = pd.Series(equity_values, index=dates)
        
        # Trades de test
        self.test_trades = [
            {'pnl': 10, 'side': 'buy'},
            {'pnl': -5, 'side': 'sell'},
            {'pnl': 15, 'side': 'buy'},
            {'pnl': -3, 'side': 'sell'},
            {'pnl': 8, 'side': 'buy'}
        ]
    
    def test_calculate_returns(self):
        """Test du calcul des rendements"""
        returns = self.metrics.calculate_returns(self.equity_curve)
        
        self.assertEqual(len(returns), len(self.equity_curve) - 1)
        self.assertFalse(returns.isna().any())
    
    def test_calculate_total_return(self):
        """Test du calcul du rendement total"""
        initial_value = 1000
        final_value = 1100
        
        total_return = self.metrics.calculate_total_return(initial_value, final_value)
        
        self.assertAlmostEqual(total_return, 10.0, places=1)
    
    def test_calculate_max_drawdown(self):
        """Test du calcul du drawdown maximum"""
        # Créer une série avec un drawdown connu
        values = [1000, 1100, 1050, 900, 950, 1200]
        equity = pd.Series(values, index=pd.date_range('2024-01-01', periods=6, freq='D'))
        
        dd_info = self.metrics.calculate_max_drawdown(equity)
        
        self.assertIn('max_drawdown_percent', dd_info)
        self.assertGreater(dd_info['max_drawdown_percent'], 0)
    
    def test_calculate_win_rate(self):
        """Test du calcul du taux de réussite"""
        win_rate_info = self.metrics.calculate_win_rate(self.test_trades)
        
        self.assertEqual(win_rate_info['total_trades'], 5)
        self.assertEqual(win_rate_info['winning_trades'], 3)
        self.assertEqual(win_rate_info['losing_trades'], 2)
        self.assertEqual(win_rate_info['win_rate'], 60.0)
    
    def test_calculate_profit_factor(self):
        """Test du calcul du profit factor"""
        profit_factor = self.metrics.calculate_profit_factor(self.test_trades)
        
        # Gains totaux: 10 + 15 + 8 = 33
        # Pertes totales: 5 + 3 = 8
        # Profit factor: 33/8 = 4.125
        self.assertAlmostEqual(profit_factor, 4.125, places=2)
    
    def test_comprehensive_report(self):
        """Test de génération du rapport complet"""
        report = self.metrics.generate_comprehensive_report(
            self.equity_curve, self.test_trades, 1000
        )
        
        # Vérifier la structure du rapport
        required_sections = ['backtest_period', 'performance', 'risk_metrics', 'trading_metrics']
        for section in required_sections:
            self.assertIn(section, report)
        
        # Vérifier quelques métriques clés
        self.assertIn('total_return_percent', report['performance'])
        self.assertIn('sharpe_ratio', report['performance'])
        self.assertIn('max_drawdown_percent', report['risk_metrics'])
        self.assertIn('win_rate_percent', report['trading_metrics'])

class TestDataProvider(unittest.TestCase):
    """Tests pour le fournisseur de données"""
    
    def setUp(self):
        """Configuration des tests"""
        self.data_provider = DataProvider()
    
    def test_clean_data(self):
        """Test du nettoyage des données"""
        # Créer des données avec des problèmes
        dates = pd.date_range(start='2024-01-01', periods=10, freq='H')
        data = pd.DataFrame({
            'open': [100, 101, np.nan, 103, 104, 105, 106, 107, 108, 109],
            'high': [102, 103, 104, 105, 106, 107, 108, 109, 110, 111],
            'low': [99, 100, 101, 102, 103, 104, 105, 106, 107, 108],
            'close': [101, 102, 103, 104, 105, 106, 107, 108, 109, 110],
            'volume': [1000, 1100, 1200, 1300, 1400, 1500, 1600, 1700, 1800, 1900]
        }, index=dates)
        
        # Ajouter une ligne avec OHLC incohérent
        data.loc[dates[5], 'high'] = 100  # High < Low
        
        cleaned_data = self.data_provider._clean_data(data)
        
        # Vérifier que les données problématiques ont été supprimées
        self.assertFalse(cleaned_data.isna().any().any())
        self.assertLess(len(cleaned_data), len(data))
    
    def test_add_technical_indicators(self):
        """Test d'ajout d'indicateurs techniques"""
        # Créer des données de test
        dates = pd.date_range(start='2024-01-01', periods=50, freq='H')
        np.random.seed(42)
        prices = 100 + np.cumsum(np.random.randn(50) * 0.5)
        
        data = pd.DataFrame({
            'open': prices,
            'high': prices * 1.01,
            'low': prices * 0.99,
            'close': prices,
            'volume': np.random.randint(1000, 2000, 50)
        }, index=dates)
        
        enriched_data = self.data_provider.add_technical_indicators(data)
        
        # Vérifier que les indicateurs ont été ajoutés
        expected_indicators = ['sma_20', 'sma_50', 'ema_12', 'ema_26', 'macd', 
                             'rsi', 'bb_upper', 'bb_lower', 'volatility']
        
        for indicator in expected_indicators:
            self.assertIn(indicator, enriched_data.columns)

class TestTradingStrategies(unittest.TestCase):
    """Tests pour les stratégies de trading"""
    
    def setUp(self):
        """Configuration des tests"""
        self.strategies = TradingStrategies()
        self.engine = BacktestEngine(1000)
        
        # Créer des données de test
        dates = pd.date_range(start='2024-01-01', periods=100, freq='H')
        np.random.seed(42)
        prices = 50000 + np.cumsum(np.random.randn(100) * 100)
        
        self.test_data = pd.DataFrame({
            'open': prices,
            'high': prices * 1.01,
            'low': prices * 0.99,
            'close': prices,
            'volume': np.random.randint(100, 1000, 100)
        }, index=dates)
    
    def test_get_all_strategies(self):
        """Test de récupération de toutes les stratégies"""
        strategies = self.strategies.get_all_strategies()
        
        self.assertIsInstance(strategies, dict)
        self.assertGreater(len(strategies), 0)
        
        # Vérifier que toutes les valeurs sont des fonctions
        for strategy_func in strategies.values():
            self.assertTrue(callable(strategy_func))
    
    def test_strategy_descriptions(self):
        """Test des descriptions de stratégies"""
        descriptions = self.strategies.get_strategy_descriptions()
        strategies = self.strategies.get_all_strategies()
        
        # Vérifier que chaque stratégie a une description
        for strategy_name in strategies.keys():
            self.assertIn(strategy_name, descriptions)
            self.assertIsInstance(descriptions[strategy_name], str)
            self.assertGreater(len(descriptions[strategy_name]), 0)
    
    def test_buy_and_hold_strategy(self):
        """Test de la stratégie Buy & Hold"""
        symbol = 'BTC/USDT'
        
        # Simuler l'exécution de la stratégie
        for i in range(len(self.test_data)):
            self.engine.update_prices({symbol: self.test_data.iloc[i]['close']}, 
                                    self.test_data.index[i])
            self.strategies.buy_and_hold_strategy(self.engine, self.test_data, i, symbol)
        
        # Vérifier qu'un trade d'achat a été exécuté
        self.assertGreater(len(self.engine.trades), 0)
        self.assertGreater(self.engine.positions.get(symbol, 0), 0)

class TestPaperTradingIntegration(unittest.TestCase):
    """Tests d'intégration avec le paper trading"""

    def setUp(self):
        """Configuration des tests"""
        from paper_trading.simulator import PaperTradingSimulator
        from paper_trading.session_manager import PaperTradingSessionManager

        self.simulator = PaperTradingSimulator(1000, 'BTC/USDT')
        self.session_manager = PaperTradingSessionManager()

    def test_simulator_initialization(self):
        """Test d'initialisation du simulateur"""
        self.assertEqual(self.simulator.initial_capital, 1000)
        self.assertEqual(self.simulator.symbol, 'BTC/USDT')
        self.assertEqual(self.simulator.current_capital, 1000)
        self.assertFalse(self.simulator.is_running)

    def test_trade_execution(self):
        """Test d'exécution de trade"""
        # Exécuter un achat
        success = self.simulator.execute_trade('buy', 0.01, 50000)

        self.assertTrue(success)
        self.assertGreater(len(self.simulator.trade_history), 0)
        self.assertAlmostEqual(self.simulator.positions.get('BTC/USDT', 0), 0.01, places=6)
        self.assertLess(self.simulator.current_capital, 1000)  # Capital réduit

    def test_session_creation(self):
        """Test de création de session"""
        session_id = self.session_manager.create_session(
            strategy_name="Buy & Hold",
            symbol="BTC/USDT",
            capital=1000,
            use_real_data=False
        )

        self.assertIsNotNone(session_id)
        self.assertIn(session_id, self.session_manager.sessions)

        # Vérifier le statut
        status = self.session_manager.get_session_status(session_id)
        self.assertIsNotNone(status)
        self.assertEqual(status['strategy_name'], "Buy & Hold")
        self.assertEqual(status['status'], 'created')

if __name__ == "__main__":
    # Configuration du logging pour les tests
    import logging
    logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(filename, encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

    # Exécuter tous les tests
    unittest.main(verbosity=2)
