#!/usr/bin/env python3
"""
📊 Exemple d'utilisation du système de backtesting
Démontre comment utiliser les différentes fonctionnalités
"""

import sys
from pathlib import Path

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from backtesting.engine import backtest_engine
from backtesting.data_provider import data_provider
from backtesting.strategies import trading_strategies
from backtesting.visualizer import visualizer
import logging

def setup_logging():
    """Configure le logging pour l'exemple"""
    logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(filename, encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)s - %(levelname)s - %(message)s'
    )

def example_single_strategy():
    """Exemple de backtesting d'une seule stratégie"""
    print("🚀 EXEMPLE 1: Backtesting d'une stratégie unique")
    print("="*60)
    
    # Configuration
    symbol = 'BTC/USDT'
    days = 30
    capital = 1000
    strategy_name = 'Grid Trading'
    
    try:
        # Récupérer les données
        print(f"📊 Récupération des données {symbol} sur {days} jours...")
        data = data_provider.get_historical_data(symbol, '1h', days)
        data = data_provider.add_technical_indicators(data)
        
        # Configurer le moteur
        backtest_engine.initial_capital = capital
        
        # Récupérer la stratégie
        strategies = trading_strategies.get_all_strategies()
        strategy_func = strategies[strategy_name]
        
        # Exécuter le backtesting
        print(f"🔄 Test de la stratégie: {strategy_name}")
        result = backtest_engine.run_strategy(strategy_func, data, symbol, strategy_name)
        
        # Afficher les résultats
        performance = result['performance']
        print(f"\n📈 Résultats:")
        print(f"   Rendement total: {performance['total_return_percent']:.2f}%")
        print(f"   Ratio de Sharpe: {performance['sharpe_ratio']:.2f}")
        print(f"   Nombre de trades: {result['trading_metrics']['total_trades']}")
        
        # Générer les visualisations
        print("📊 Génération des graphiques...")
        visualizer.plot_equity_curve(result['equity_curve'], 
                                   f"Equity Curve - {strategy_name}")
        
        # Générer le rapport HTML
        report_path = visualizer.generate_report_html(result)
        print(f"📄 Rapport généré: {report_path}")
        
        return result
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return None

def example_strategy_comparison():
    """Exemple de comparaison de stratégies"""
    print("\n🚀 EXEMPLE 2: Comparaison de stratégies")
    print("="*60)
    
    # Configuration
    symbol = 'ETH/USDT'
    days = 15
    capital = 1000
    
    try:
        # Récupérer les données
        print(f"📊 Récupération des données {symbol} sur {days} jours...")
        data = data_provider.get_historical_data(symbol, '1h', days)
        data = data_provider.add_technical_indicators(data)
        
        # Configurer le moteur
        backtest_engine.initial_capital = capital
        
        # Sélectionner quelques stratégies pour la comparaison
        all_strategies = trading_strategies.get_all_strategies()
        selected_strategies = {
            'Grid Trading': all_strategies['Grid Trading'],
            'SMA Crossover': all_strategies['SMA Crossover'],
            'RSI Mean Reversion': all_strategies['RSI Mean Reversion'],
            'Buy & Hold': all_strategies['Buy & Hold']
        }
        
        # Exécuter la comparaison
        print(f"🔄 Comparaison de {len(selected_strategies)} stratégies...")
        results = backtest_engine.run_multiple_strategies(selected_strategies, data, symbol)
        
        # Afficher le tableau de comparaison
        comparison_df = results['comparison']
        if not comparison_df.empty:
            print("\n📊 Tableau de comparaison:")
            print(comparison_df.to_string(index=False, float_format='%.2f'))
            
            # Visualiser la comparaison
            visualizer.plot_strategy_comparison(comparison_df, 
                                              f"Strategy Comparison - {symbol}")
            
            # Afficher le résumé
            summary = results['summary']
            print(f"\n🏆 Meilleure stratégie: {summary['best_strategy']}")
        
        return results
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return None

def example_custom_strategy():
    """Exemple de création d'une stratégie personnalisée"""
    print("\n🚀 EXEMPLE 3: Stratégie personnalisée")
    print("="*60)
    
    def simple_momentum_strategy(engine, data, index, symbol):
        """
        Stratégie momentum simple:
        - Achat si le prix actuel > prix d'il y a 5 barres
        - Vente si le prix actuel < prix d'il y a 5 barres
        """
        if index < 5:
            return
        
        current_price = data.iloc[index]['close']
        past_price = data.iloc[index-5]['close']
        current_position = engine.positions.get(symbol, 0)
        
        # Signal d'achat : momentum haussier
        if current_price > past_price * 1.02 and current_position == 0:  # +2%
            order_value = engine.current_capital * 0.8
            quantity = order_value / current_price
            engine.execute_trade(symbol, 'buy', quantity, current_price, 
                               data.index[index], 'SimpleMomentum')
        
        # Signal de vente : momentum baissier
        elif current_price < past_price * 0.98 and current_position > 0:  # -2%
            engine.execute_trade(symbol, 'sell', current_position, current_price, 
                               data.index[index], 'SimpleMomentum')
    
    # Configuration
    symbol = 'BTC/USDT'
    days = 20
    capital = 1000
    
    try:
        # Récupérer les données
        print(f"📊 Récupération des données {symbol} sur {days} jours...")
        data = data_provider.get_historical_data(symbol, '1h', days)
        
        # Configurer le moteur
        backtest_engine.initial_capital = capital
        
        # Exécuter le backtesting avec la stratégie personnalisée
        print("🔄 Test de la stratégie momentum personnalisée...")
        result = backtest_engine.run_strategy(simple_momentum_strategy, data, 
                                            symbol, 'Simple Momentum')
        
        # Afficher les résultats
        performance = result['performance']
        print(f"\n📈 Résultats de la stratégie personnalisée:")
        print(f"   Rendement total: {performance['total_return_percent']:.2f}%")
        print(f"   Ratio de Sharpe: {performance['sharpe_ratio']:.2f}")
        print(f"   Nombre de trades: {result['trading_metrics']['total_trades']}")
        
        return result
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return None

def example_market_analysis():
    """Exemple d'analyse de marché"""
    print("\n🚀 EXEMPLE 4: Analyse de marché")
    print("="*60)
    
    symbols = ['BTC/USDT', 'ETH/USDT', 'BNB/USDT']
    
    for symbol in symbols:
        try:
            print(f"\n📊 Analyse de {symbol}:")
            summary = data_provider.get_market_data_summary(symbol, 30)
            
            if summary:
                print(f"   Prix début: {summary['price_start']:.2f}")
                print(f"   Prix fin: {summary['price_end']:.2f}")
                print(f"   Rendement: {summary['total_return']:.2f}%")
                print(f"   Volatilité: {summary['volatility_avg']:.2f}%")
                print(f"   RSI moyen: {summary['rsi_avg']:.1f}")
                print(f"   Qualité données: {summary['data_quality']}")
            
        except Exception as e:
            print(f"❌ Erreur pour {symbol}: {e}")

def main():
    """Fonction principale des exemples"""
    setup_logging()
    
    print("🎯 EXEMPLES D'UTILISATION DU SYSTÈME DE BACKTESTING")
    print("="*80)
    
    try:
        # Exemple 1: Stratégie unique
        result1 = example_single_strategy()
        
        # Exemple 2: Comparaison de stratégies
        result2 = example_strategy_comparison()
        
        # Exemple 3: Stratégie personnalisée
        result3 = example_custom_strategy()
        
        # Exemple 4: Analyse de marché
        example_market_analysis()
        
        print("\n✅ Tous les exemples ont été exécutés avec succès!")
        print("📄 Consultez les rapports HTML générés dans assets/results/backtests/")
        
    except KeyboardInterrupt:
        print("\n🛑 Exemples interrompus par l'utilisateur")
    except Exception as e:
        print(f"\n❌ Erreur inattendue: {e}")

if __name__ == "__main__":
    main()
