"""
⚡ Exécuteur de copy trading
Réplique les trades des wallets suivis avec gestion des risques et position sizing
"""

import asyncio
import time
import uuid
from decimal import Decimal, ROUND_DOWN
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from datetime import datetime, timed<PERSON>ta
from enum import Enum
import logging
from pathlib import Path
import sys

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent.parent))

from bots.copy_trading.wallet_analyzer import WalletAnalyzer, WalletPerformance
from bots.copy_trading.signal_detector import SignalDetector, WalletSignal
from utils.error_handler import error_handler, ErrorCategory, ErrorSeverity
from logging_system.central_logger import central_logger, LogCategory
from risk_management.portfolio_manager import portfolio_manager

class CopyTradeStatus(Enum):
    """Statuts d'un copy trade"""
    PENDING = "PENDING"
    EXECUTING = "EXECUTING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    CANCELLED = "CANCELLED"
    PARTIAL = "PARTIAL"

@dataclass
class CopyTradeConfig:
    """Configuration du copy trading"""
    # Position sizing
    default_copy_percentage: float = 2.0  # 2% du portefeuille par défaut
    max_copy_percentage: float = 5.0      # 5% maximum
    min_trade_amount: float = 50.0        # $50 minimum
    max_trade_amount: float = 10000.0     # $10k maximum
    
    # Gestion des risques
    max_concurrent_copies: int = 10
    max_copies_per_wallet: int = 3
    max_daily_copy_amount: float = 50000.0
    
    # Filtres d'exécution
    min_signal_confidence: float = 0.7
    required_urgency_levels: List[str] = None  # None = tous, ou ['high', 'medium']
    
    # Timing
    execution_timeout_seconds: int = 300
    max_slippage_percentage: float = 2.0
    
    # Position management
    enable_stop_loss: bool = True
    default_stop_loss_percentage: float = 10.0
    enable_take_profit: bool = True
    default_take_profit_percentage: float = 20.0
    
    # Wallet weighting
    performance_weight_factor: float = 0.5  # Influence de la performance sur le sizing

@dataclass
class CopyTrade:
    """Représente un copy trade"""
    id: str
    signal: WalletSignal
    status: CopyTradeStatus
    created_at: datetime
    
    # Paramètres d'exécution
    target_amount_usd: float
    actual_amount_usd: Optional[float]
    copy_percentage: float
    
    # Résultats d'exécution
    entry_price: Optional[float]
    entry_tx_hash: Optional[str]
    entry_timestamp: Optional[datetime]
    
    # Gestion de position
    stop_loss_price: Optional[float]
    take_profit_price: Optional[float]
    current_price: Optional[float]
    unrealized_pnl: Optional[float]
    
    # Clôture
    exit_price: Optional[float]
    exit_tx_hash: Optional[str]
    exit_timestamp: Optional[datetime]
    exit_reason: Optional[str]  # 'stop_loss', 'take_profit', 'manual', 'timeout'
    realized_pnl: Optional[float]
    
    # Métadonnées
    slippage_percentage: Optional[float]
    gas_fees_usd: Optional[float]
    execution_time_seconds: Optional[float]

class CopyExecutor:
    """Exécuteur de copy trading"""
    
    def __init__(self, signal_detector: SignalDetector, config: CopyTradeConfig = None):
        self.logger = logging.getLogger(__name__)
        self.signal_detector = signal_detector
        self.config = config or CopyTradeConfig()
        
        # Initialiser les filtres par défaut
        if self.config.required_urgency_levels is None:
            self.config.required_urgency_levels = ['high', 'medium']
        
        # Copy trades actifs et historique
        self.active_copy_trades: Dict[str, CopyTrade] = {}
        self.copy_trade_history: List[CopyTrade] = []
        
        # Limitations et compteurs
        self.daily_copy_amount = 0.0
        self.last_reset_date = datetime.now().date()
        self.wallet_copy_counts: Dict[str, int] = {}
        
        # Statistiques
        self.total_copies_executed = 0
        self.successful_copies = 0
        self.total_copy_volume = 0.0
        self.total_realized_pnl = 0.0
        
        central_logger.log(
            level="INFO",
            message="Exécuteur de copy trading initialisé",
            category=LogCategory.STRATEGY,
            max_concurrent=self.config.max_concurrent_copies,
            min_confidence=self.config.min_signal_confidence
        )
    
    async def start_monitoring(self):
        """Démarre le monitoring des signaux"""
        while True:
            try:
                # Réinitialiser les compteurs quotidiens
                self._reset_daily_counters()
                
                # Récupérer les nouveaux signaux
                await self._process_new_signals()
                
                # Gérer les positions existantes
                await self._manage_existing_positions()
                
                # Nettoyer les trades terminés
                self._cleanup_completed_trades()
                
                # Attendre avant le prochain cycle
                await asyncio.sleep(5)
                
            except Exception as e:
                error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.MEDIUM, {
                    'function': 'start_monitoring'
                })
                await asyncio.sleep(30)
    
    def _reset_daily_counters(self):
        """Réinitialise les compteurs quotidiens"""
        today = datetime.now().date()
        if today != self.last_reset_date:
            self.daily_copy_amount = 0.0
            self.wallet_copy_counts.clear()
            self.last_reset_date = today
    
    async def _process_new_signals(self):
        """Traite les nouveaux signaux"""
        try:
            # Récupérer les signaux actifs
            signals = self.signal_detector.get_active_signals(
                min_confidence=self.config.min_signal_confidence
            )
            
            for signal in signals:
                # Vérifier si on peut exécuter ce signal
                if await self._can_execute_signal(signal):
                    copy_trade = await self._execute_copy_trade(signal)
                    if copy_trade:
                        self.active_copy_trades[copy_trade.id] = copy_trade
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.MEDIUM, {
                'function': '_process_new_signals'
            })
    
    async def _can_execute_signal(self, signal: WalletSignal) -> bool:
        """Vérifie si un signal peut être exécuté"""
        try:
            # Vérifier l'urgence
            if signal.urgency not in self.config.required_urgency_levels:
                return False
            
            # Vérifier les limites de concurrent
            if len(self.active_copy_trades) >= self.config.max_concurrent_copies:
                return False
            
            # Vérifier les limites par wallet
            wallet_copies = self.wallet_copy_counts.get(signal.wallet_address, 0)
            if wallet_copies >= self.config.max_copies_per_wallet:
                return False
            
            # Vérifier les limites quotidiennes
            estimated_amount = self._calculate_copy_amount(signal)
            if self.daily_copy_amount + estimated_amount > self.config.max_daily_copy_amount:
                return False
            
            # Vérifier qu'on n'a pas déjà copié ce signal
            signal_key = f"{signal.wallet_address}_{signal.token_symbol}_{int(signal.timestamp.timestamp())}"
            existing_copy = any(
                ct.signal.wallet_address == signal.wallet_address and
                ct.signal.token_symbol == signal.token_symbol and
                abs((ct.signal.timestamp - signal.timestamp).total_seconds()) < 300
                for ct in self.active_copy_trades.values()
            )
            
            if existing_copy:
                return False
            
            return True
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.LOW, {
                'function': '_can_execute_signal',
                'signal_wallet': signal.wallet_address
            })
            return False
    
    def _calculate_copy_amount(self, signal: WalletSignal) -> float:
        """Calcule le montant à copier pour un signal"""
        try:
            # Montant de base selon la configuration
            portfolio_value = portfolio_manager.get_portfolio_value()
            base_amount = portfolio_value * (self.config.default_copy_percentage / 100)
            
            # Ajustement selon la performance du wallet
            performance = signal.wallet_performance
            performance_multiplier = 1.0
            
            if performance:
                # Facteur basé sur le win rate
                win_rate_factor = performance.win_rate / 100
                
                # Facteur basé sur la consistance
                consistency_factor = performance.consistency_score
                
                # Facteur basé sur le risque (inverse)
                risk_factor = 1 - performance.risk_score
                
                # Multiplier combiné
                performance_multiplier = (
                    win_rate_factor * 0.4 +
                    consistency_factor * 0.3 +
                    risk_factor * 0.3
                )
                
                # Appliquer le facteur de pondération
                performance_multiplier = 1 + (performance_multiplier - 1) * self.config.performance_weight_factor
            
            # Ajustement selon la confiance du signal
            confidence_multiplier = signal.confidence_score
            
            # Ajustement selon l'urgence
            urgency_multipliers = {'high': 1.2, 'medium': 1.0, 'low': 0.8}
            urgency_multiplier = urgency_multipliers.get(signal.urgency, 1.0)
            
            # Calcul final
            final_amount = (base_amount * 
                          performance_multiplier * 
                          confidence_multiplier * 
                          urgency_multiplier)
            
            # Appliquer les limites
            final_amount = max(self.config.min_trade_amount, final_amount)
            final_amount = min(self.config.max_trade_amount, final_amount)
            
            # Vérifier le pourcentage maximum
            max_amount_by_percentage = portfolio_value * (self.config.max_copy_percentage / 100)
            final_amount = min(final_amount, max_amount_by_percentage)
            
            return final_amount
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.LOW, {
                'function': '_calculate_copy_amount'
            })
            return self.config.min_trade_amount
    
    async def _execute_copy_trade(self, signal: WalletSignal) -> Optional[CopyTrade]:
        """Exécute un copy trade"""
        try:
            # Calculer le montant à copier
            copy_amount = self._calculate_copy_amount(signal)
            copy_percentage = (copy_amount / portfolio_manager.get_portfolio_value()) * 100
            
            # Créer le copy trade
            copy_trade = CopyTrade(
                id=str(uuid.uuid4()),
                signal=signal,
                status=CopyTradeStatus.PENDING,
                created_at=datetime.now(),
                target_amount_usd=copy_amount,
                actual_amount_usd=None,
                copy_percentage=copy_percentage,
                entry_price=None,
                entry_tx_hash=None,
                entry_timestamp=None,
                stop_loss_price=None,
                take_profit_price=None,
                current_price=None,
                unrealized_pnl=None,
                exit_price=None,
                exit_tx_hash=None,
                exit_timestamp=None,
                exit_reason=None,
                realized_pnl=None,
                slippage_percentage=None,
                gas_fees_usd=None,
                execution_time_seconds=None
            )
            
            central_logger.log(
                level="INFO",
                message=f"Démarrage copy trade: {signal.action} {signal.token_symbol}",
                category=LogCategory.TRADING,
                copy_trade_id=copy_trade.id,
                wallet_address=signal.wallet_address,
                amount_usd=copy_amount,
                confidence=signal.confidence_score
            )
            
            # Exécuter le trade
            success = await self._execute_trade(copy_trade)
            
            if success:
                # Mettre à jour les compteurs
                self.daily_copy_amount += copy_amount
                self.wallet_copy_counts[signal.wallet_address] = (
                    self.wallet_copy_counts.get(signal.wallet_address, 0) + 1
                )
                self.total_copies_executed += 1
                self.total_copy_volume += copy_amount
                
                central_logger.trade_executed(
                    message=f"Copy trade exécuté: {signal.action} {signal.token_symbol}",
                    symbol=signal.token_symbol,
                    side=signal.action,
                    quantity=copy_trade.actual_amount_usd or copy_amount,
                    price=copy_trade.entry_price or 0,
                    trade_id=copy_trade.entry_tx_hash or copy_trade.id
                )
                
                return copy_trade
            else:
                copy_trade.status = CopyTradeStatus.FAILED
                return copy_trade
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.HIGH, {
                'function': '_execute_copy_trade',
                'signal_wallet': signal.wallet_address
            })
            return None
    
    async def _execute_trade(self, copy_trade: CopyTrade) -> bool:
        """Exécute le trade réel (simulation)"""
        try:
            copy_trade.status = CopyTradeStatus.EXECUTING
            start_time = time.time()
            
            signal = copy_trade.signal
            
            # Simuler l'exécution du trade
            await asyncio.sleep(2)  # Simuler le temps d'exécution
            
            # Simuler un prix d'entrée avec slippage
            import numpy as np
            base_price = 100.0  # Prix simulé
            slippage = np.random.uniform(0.1, self.config.max_slippage_percentage)
            
            if signal.action == 'buy':
                entry_price = base_price * (1 + slippage / 100)
            else:
                entry_price = base_price * (1 - slippage / 100)
            
            # Calculer le montant réel
            actual_amount = copy_trade.target_amount_usd
            
            # Simuler les frais de gas
            gas_fees = np.random.uniform(10, 50)  # $10-50
            
            # Simuler un hash de transaction
            import hashlib
            tx_data = f"{copy_trade.id}_{time.time()}"
            tx_hash = f"0x{hashlib.sha256(tx_data.encode()).hexdigest()[:64]}"
            
            # Mettre à jour le copy trade
            copy_trade.entry_price = entry_price
            copy_trade.actual_amount_usd = actual_amount
            copy_trade.entry_tx_hash = tx_hash
            copy_trade.entry_timestamp = datetime.now()
            copy_trade.slippage_percentage = slippage
            copy_trade.gas_fees_usd = gas_fees
            copy_trade.execution_time_seconds = time.time() - start_time
            copy_trade.current_price = entry_price
            copy_trade.unrealized_pnl = 0.0
            
            # Calculer les niveaux de stop loss et take profit
            if self.config.enable_stop_loss:
                if signal.action == 'buy':
                    copy_trade.stop_loss_price = entry_price * (1 - self.config.default_stop_loss_percentage / 100)
                else:
                    copy_trade.stop_loss_price = entry_price * (1 + self.config.default_stop_loss_percentage / 100)
            
            if self.config.enable_take_profit:
                if signal.action == 'buy':
                    copy_trade.take_profit_price = entry_price * (1 + self.config.default_take_profit_percentage / 100)
                else:
                    copy_trade.take_profit_price = entry_price * (1 - self.config.default_take_profit_percentage / 100)
            
            copy_trade.status = CopyTradeStatus.COMPLETED
            
            return True
            
        except Exception as e:
            copy_trade.status = CopyTradeStatus.FAILED
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.HIGH, {
                'function': '_execute_trade',
                'copy_trade_id': copy_trade.id
            })
            return False
    
    async def _manage_existing_positions(self):
        """Gère les positions existantes"""
        try:
            for copy_trade in list(self.active_copy_trades.values()):
                if copy_trade.status == CopyTradeStatus.COMPLETED:
                    await self._update_position_pnl(copy_trade)
                    await self._check_exit_conditions(copy_trade)
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.MEDIUM, {
                'function': '_manage_existing_positions'
            })
    
    async def _update_position_pnl(self, copy_trade: CopyTrade):
        """Met à jour le P&L d'une position"""
        try:
            # Simuler un nouveau prix
            if copy_trade.current_price:
                # Mouvement aléatoire du prix
                price_change = np.random.uniform(-0.05, 0.05)  # ±5%
                new_price = copy_trade.current_price * (1 + price_change)
                copy_trade.current_price = new_price
                
                # Calculer le P&L non réalisé
                if copy_trade.entry_price and copy_trade.actual_amount_usd:
                    if copy_trade.signal.action == 'buy':
                        pnl_percentage = (new_price - copy_trade.entry_price) / copy_trade.entry_price
                    else:
                        pnl_percentage = (copy_trade.entry_price - new_price) / copy_trade.entry_price
                    
                    copy_trade.unrealized_pnl = copy_trade.actual_amount_usd * pnl_percentage
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.LOW, {
                'function': '_update_position_pnl',
                'copy_trade_id': copy_trade.id
            })
    
    async def _check_exit_conditions(self, copy_trade: CopyTrade):
        """Vérifie les conditions de sortie"""
        try:
            if not copy_trade.current_price:
                return
            
            exit_reason = None
            
            # Vérifier le stop loss
            if (copy_trade.stop_loss_price and 
                ((copy_trade.signal.action == 'buy' and copy_trade.current_price <= copy_trade.stop_loss_price) or
                 (copy_trade.signal.action == 'sell' and copy_trade.current_price >= copy_trade.stop_loss_price))):
                exit_reason = 'stop_loss'
            
            # Vérifier le take profit
            elif (copy_trade.take_profit_price and 
                  ((copy_trade.signal.action == 'buy' and copy_trade.current_price >= copy_trade.take_profit_price) or
                   (copy_trade.signal.action == 'sell' and copy_trade.current_price <= copy_trade.take_profit_price))):
                exit_reason = 'take_profit'
            
            # Vérifier le timeout
            elif copy_trade.entry_timestamp:
                time_elapsed = (datetime.now() - copy_trade.entry_timestamp).total_seconds()
                if time_elapsed > self.config.execution_timeout_seconds:
                    exit_reason = 'timeout'
            
            # Exécuter la sortie si nécessaire
            if exit_reason:
                await self._exit_position(copy_trade, exit_reason)
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.MEDIUM, {
                'function': '_check_exit_conditions',
                'copy_trade_id': copy_trade.id
            })
    
    async def _exit_position(self, copy_trade: CopyTrade, reason: str):
        """Ferme une position"""
        try:
            # Simuler la fermeture
            await asyncio.sleep(1)
            
            # Simuler un hash de transaction de sortie
            import hashlib
            tx_data = f"exit_{copy_trade.id}_{time.time()}"
            exit_tx_hash = f"0x{hashlib.sha256(tx_data.encode()).hexdigest()[:64]}"
            
            # Mettre à jour le copy trade
            copy_trade.exit_price = copy_trade.current_price
            copy_trade.exit_tx_hash = exit_tx_hash
            copy_trade.exit_timestamp = datetime.now()
            copy_trade.exit_reason = reason
            copy_trade.realized_pnl = copy_trade.unrealized_pnl
            
            # Mettre à jour les statistiques
            if copy_trade.realized_pnl and copy_trade.realized_pnl > 0:
                self.successful_copies += 1
            
            if copy_trade.realized_pnl:
                self.total_realized_pnl += copy_trade.realized_pnl
            
            central_logger.position_closed(
                message=f"Copy trade fermé: {reason}",
                symbol=copy_trade.signal.token_symbol,
                quantity=copy_trade.actual_amount_usd or 0,
                exit_price=copy_trade.exit_price or 0,
                pnl=copy_trade.realized_pnl or 0,
                reason=reason
            )
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.HIGH, {
                'function': '_exit_position',
                'copy_trade_id': copy_trade.id
            })
    
    def _cleanup_completed_trades(self):
        """Nettoie les trades terminés"""
        try:
            completed_trades = [
                trade_id for trade_id, trade in self.active_copy_trades.items()
                if trade.exit_timestamp is not None
            ]
            
            for trade_id in completed_trades:
                trade = self.active_copy_trades.pop(trade_id)
                self.copy_trade_history.append(trade)
            
            # Limiter l'historique
            if len(self.copy_trade_history) > 1000:
                self.copy_trade_history = self.copy_trade_history[-500:]
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.LOW, {
                'function': '_cleanup_completed_trades'
            })
    
    def get_active_copy_trades(self) -> List[CopyTrade]:
        """Retourne les copy trades actifs"""
        return list(self.active_copy_trades.values())
    
    def get_copy_trade_by_id(self, trade_id: str) -> Optional[CopyTrade]:
        """Récupère un copy trade par son ID"""
        return self.active_copy_trades.get(trade_id) or next(
            (trade for trade in self.copy_trade_history if trade.id == trade_id), None
        )
    
    def get_performance_statistics(self) -> Dict[str, Any]:
        """Retourne les statistiques de performance"""
        success_rate = (self.successful_copies / max(1, self.total_copies_executed)) * 100
        
        avg_pnl = 0.0
        if self.copy_trade_history:
            completed_with_pnl = [t for t in self.copy_trade_history if t.realized_pnl is not None]
            if completed_with_pnl:
                avg_pnl = sum(t.realized_pnl for t in completed_with_pnl) / len(completed_with_pnl)
        
        return {
            'total_copies_executed': self.total_copies_executed,
            'successful_copies': self.successful_copies,
            'success_rate': success_rate,
            'total_copy_volume': self.total_copy_volume,
            'total_realized_pnl': self.total_realized_pnl,
            'average_pnl_per_trade': avg_pnl,
            'active_positions': len(self.active_copy_trades),
            'daily_copy_amount': self.daily_copy_amount,
            'daily_limit_remaining': self.config.max_daily_copy_amount - self.daily_copy_amount
        }
