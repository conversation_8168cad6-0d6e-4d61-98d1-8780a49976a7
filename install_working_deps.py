#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 Install Working Dependencies
Installe seulement les dépendances qui fonctionnent
"""

import subprocess
import sys

def run_pip_install(packages, description):
    """Installe des packages avec pip"""
    print(f"📦 {description}...")
    
    for package in packages:
        try:
            print(f"  ⏳ Installation de {package}...")
            result = subprocess.run([sys.executable, '-m', 'pip', 'install', package], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"  ✅ {package} installé")
            else:
                print(f"  ❌ Erreur {package}: {result.stderr.strip()}")
        except Exception as e:
            print(f"  ❌ Exception {package}: {e}")

def main():
    """Installation des dépendances essentielles"""
    print("🔧 INSTALLATION DÉPENDANCES FONCTIONNELLES")
    print("=" * 60)
    
    # Dépendances de base (déjà installées et fonctionnelles)
    basic_deps = [
        "requests>=2.28.0",
        "python-dotenv>=0.19.0", 
        "ccxt>=4.0.0"
    ]
    
    # Dépendances scientifiques (versions compatibles)
    scientific_deps = [
        "numpy>=1.26.0,<2.0.0",
        "pandas>=2.0.0",
        "matplotlib>=3.5.0"
    ]
    
    # Dépendances optionnelles (si nécessaire)
    optional_deps = [
        "seaborn>=0.11.0",
        "psutil>=5.9.0"
    ]
    
    print("🎯 Installation par groupes pour éviter les conflits...")
    
    # 1. Dépendances de base
    run_pip_install(basic_deps, "Installation dépendances de base")
    
    # 2. Dépendances scientifiques
    run_pip_install(scientific_deps, "Installation dépendances scientifiques")
    
    # 3. Dépendances optionnelles
    run_pip_install(optional_deps, "Installation dépendances optionnelles")
    
    print("\n✅ INSTALLATION TERMINÉE")
    print("\n🧪 TEST DE FONCTIONNEMENT:")
    
    # Test d'import
    test_imports = [
        ("requests", "requests"),
        ("dotenv", "python-dotenv"),
        ("ccxt", "ccxt"),
        ("numpy", "numpy"),
        ("pandas", "pandas"),
        ("matplotlib", "matplotlib")
    ]
    
    print("🔍 Vérification des imports...")
    all_good = True
    
    for module, package in test_imports:
        try:
            __import__(module)
            print(f"  ✅ {package}")
        except ImportError as e:
            print(f"  ❌ {package}: {e}")
            all_good = False
    
    if all_good:
        print("\n🎉 TOUTES LES DÉPENDANCES ESSENTIELLES FONCTIONNENT")
        print("\n🚀 COMMANDES POUR TESTER:")
        print("python bots/safe_bot.py --mode testnet")
        print("python launcher.py")
    else:
        print("\n⚠️ CERTAINES DÉPENDANCES MANQUENT")
        print("💡 Utilisez requirements_minimal.txt pour une installation basique")
    
    print("\n📋 DÉPENDANCES AVANCÉES (optionnelles):")
    print("- FastAPI: pip install fastapi uvicorn")
    print("- Analyse: pip install scipy scikit-learn")
    print("- Base de données: pip install sqlalchemy")
    print("- Tests: pip install pytest")

if __name__ == "__main__":
    main()
