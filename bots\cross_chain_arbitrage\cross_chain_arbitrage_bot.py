"""
🌉 Bot d'arbitrage cross-chain principal
Bot automatisé pour l'arbitrage de stablecoins entre différentes blockchains
"""

import asyncio
import json
import time
from decimal import Decimal
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import logging
from pathlib import Path
import sys

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent.parent))

from bots.cross_chain_arbitrage.chain_connector import ChainConnector
from bots.cross_chain_arbitrage.arbitrage_detector import ArbitrageDetector, ArbitrageConfig
from bots.cross_chain_arbitrage.arbitrage_executor import ArbitrageExecutor, ExecutionConfig
from utils.error_handler import error_handler, ErrorCategory, ErrorSeverity
from logging_system.central_logger import central_logger, LogCategory
from risk_management.portfolio_manager import portfolio_manager
from utils.notifications import notifier

@dataclass
class BotConfig:
    """Configuration complète du bot d'arbitrage"""
    # Paramètres de détection
    min_profit_usd: float = 20.0
    min_roi_percentage: float = 1.0
    
    # Paramètres d'exécution
    max_concurrent_trades: int = 2
    max_capital_per_trade: float = 5000.0
    
    # Chaînes et tokens à surveiller
    monitored_chains: List[str] = None
    monitored_tokens: List[str] = None
    
    # Timing
    scan_interval_seconds: int = 30
    opportunity_timeout_seconds: int = 300
    
    # Notifications
    enable_notifications: bool = True
    notification_min_profit: float = 50.0
    
    # Sécurité
    emergency_stop_loss_percentage: float = 5.0
    max_daily_loss: float = 1000.0

class CrossChainArbitrageBot:
    """Bot d'arbitrage cross-chain principal"""
    
    def __init__(self, config: BotConfig = None):
        self.logger = logging.getLogger(__name__)
        self.config = config or BotConfig()
        
        # Initialiser les valeurs par défaut
        if self.config.monitored_chains is None:
            self.config.monitored_chains = ['ethereum', 'bsc', 'polygon', 'arbitrum']
        if self.config.monitored_tokens is None:
            self.config.monitored_tokens = ['USDT', 'USDC', 'DAI']
        
        # État du bot
        self.is_running = False
        self.start_time = None
        self.last_scan_time = 0
        
        # Composants principaux
        self.connector = ChainConnector()
        
        # Configuration des sous-composants
        detector_config = ArbitrageConfig(
            min_profit_usd=self.config.min_profit_usd,
            min_roi_percentage=self.config.min_roi_percentage,
            monitored_tokens=self.config.monitored_tokens,
            monitored_chains=self.config.monitored_chains
        )
        
        executor_config = ExecutionConfig(
            max_concurrent_executions=self.config.max_concurrent_trades,
            max_capital_per_trade=self.config.max_capital_per_trade
        )
        
        self.detector = ArbitrageDetector(self.connector, detector_config)
        self.executor = ArbitrageExecutor(self.connector, self.detector, executor_config)
        
        # Métriques de performance
        self.daily_profit = 0.0
        self.daily_trades = 0
        self.last_reset_date = datetime.now().date()
        
        # Gestion des erreurs
        self.consecutive_errors = 0
        self.max_consecutive_errors = 5
        
        central_logger.log(
            level="INFO",
            message="Bot d'arbitrage cross-chain initialisé",
            category=LogCategory.STRATEGY,
            monitored_chains=len(self.config.monitored_chains),
            monitored_tokens=len(self.config.monitored_tokens)
        )
    
    async def start(self):
        """Démarre le bot d'arbitrage"""
        if self.is_running:
            self.logger.warning("Le bot est déjà en cours d'exécution")
            return
        
        try:
            self.is_running = True
            self.start_time = datetime.now()
            
            central_logger.strategy_action(
                message="Démarrage du bot d'arbitrage cross-chain",
                strategy_name="CrossChain_Arbitrage",
                action="start"
            )
            
            # Notification de démarrage
            if self.config.enable_notifications:
                await notifier.send_telegram(
                    f"🌉 <b>Bot Arbitrage Cross-Chain démarré</b>\n"
                    f"🔗 Chaînes: {', '.join(self.config.monitored_chains)}\n"
                    f"💰 Tokens: {', '.join(self.config.monitored_tokens)}\n"
                    f"📊 Profit min: ${self.config.min_profit_usd}"
                )
            
            # Vérifier la santé des chaînes
            await self._check_chains_health()
            
            # Boucle principale
            await self._main_loop()
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.CRITICAL, {
                'function': 'start'
            })
            await self.stop()
    
    async def stop(self):
        """Arrête le bot d'arbitrage"""
        if not self.is_running:
            return
        
        self.is_running = False
        
        try:
            # Annuler les exécutions en cours
            active_executions = self.executor.get_active_executions()
            for execution in active_executions:
                await self.executor.cancel_execution(execution.id)
            
            # Générer le rapport final
            final_report = self._generate_performance_report()
            
            central_logger.strategy_action(
                message="Arrêt du bot d'arbitrage cross-chain",
                strategy_name="CrossChain_Arbitrage",
                action="stop",
                **final_report
            )
            
            # Notification d'arrêt
            if self.config.enable_notifications:
                await notifier.send_telegram(
                    f"🛑 <b>Bot Arbitrage Cross-Chain arrêté</b>\n"
                    f"📊 Trades: {final_report.get('total_trades', 0)}\n"
                    f"💰 Profit: ${final_report.get('total_profit', 0):.2f}\n"
                    f"📈 Taux de réussite: {final_report.get('success_rate', 0):.1f}%"
                )
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.MEDIUM, {
                'function': 'stop'
            })
    
    async def _main_loop(self):
        """Boucle principale du bot"""
        while self.is_running:
            try:
                # Réinitialiser les compteurs quotidiens si nécessaire
                self._reset_daily_counters()
                
                # Vérifier les limites de sécurité
                if not self._check_safety_limits():
                    await self._emergency_stop("Limites de sécurité dépassées")
                    break
                
                # Scanner les opportunités
                current_time = time.time()
                if current_time - self.last_scan_time >= self.config.scan_interval_seconds:
                    await self._scan_and_execute_opportunities()
                    self.last_scan_time = current_time
                
                # Surveiller les exécutions en cours
                await self._monitor_active_executions()
                
                # Attendre avant le prochain cycle
                await asyncio.sleep(5)
                
            except Exception as e:
                self.consecutive_errors += 1
                error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.HIGH, {
                    'function': '_main_loop',
                    'consecutive_errors': self.consecutive_errors
                })
                
                # Arrêt d'urgence si trop d'erreurs consécutives
                if self.consecutive_errors >= self.max_consecutive_errors:
                    await self._emergency_stop("Trop d'erreurs consécutives")
                    break
                
                await asyncio.sleep(10)  # Pause avant de réessayer
            else:
                self.consecutive_errors = 0  # Reset si pas d'erreur
    
    async def _check_chains_health(self):
        """Vérifie la santé de toutes les chaînes"""
        for chain in self.config.monitored_chains:
            health = await self.connector.check_chain_health(chain)
            
            if not health.get('healthy', False):
                central_logger.log(
                    level="WARNING",
                    message=f"Chaîne {chain} non disponible",
                    category=LogCategory.NETWORK,
                    chain=chain,
                    error=health.get('error', 'Unknown')
                )
    
    async def _scan_and_execute_opportunities(self):
        """Scanne et exécute les opportunités"""
        try:
            # Scanner les opportunités
            opportunities = await self.detector.scan_opportunities()
            
            if not opportunities:
                return
            
            central_logger.log(
                level="INFO",
                message=f"{len(opportunities)} opportunités détectées",
                category=LogCategory.STRATEGY,
                opportunities_count=len(opportunities)
            )
            
            # Exécuter les meilleures opportunités
            for opportunity in opportunities[:self.config.max_concurrent_trades]:
                # Vérifier si on peut encore exécuter
                active_count = len(self.executor.get_active_executions())
                if active_count >= self.config.max_concurrent_trades:
                    break
                
                # Vérifier la rentabilité
                if opportunity.net_profit >= self.config.min_profit_usd:
                    # Exécuter l'opportunité
                    execution = await self.executor.execute_opportunity(opportunity)
                    
                    if execution:
                        self.daily_trades += 1
                        
                        # Notification pour les gros profits
                        if (self.config.enable_notifications and 
                            opportunity.net_profit >= self.config.notification_min_profit):
                            
                            await notifier.send_telegram(
                                f"💰 <b>Arbitrage en cours</b>\n"
                                f"🪙 Token: {opportunity.token_symbol}\n"
                                f"🔗 Route: {opportunity.buy_chain} → {opportunity.sell_chain}\n"
                                f"💵 Montant: ${opportunity.optimal_amount:.0f}\n"
                                f"📈 Profit estimé: ${opportunity.net_profit:.2f}\n"
                                f"⚡ ROI: {opportunity.roi_percentage:.2f}%"
                            )
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.MEDIUM, {
                'function': '_scan_and_execute_opportunities'
            })
    
    async def _monitor_active_executions(self):
        """Surveille les exécutions actives"""
        try:
            active_executions = self.executor.get_active_executions()
            
            for execution in active_executions:
                # Vérifier le timeout
                execution_time = (datetime.now() - execution.start_time).total_seconds()
                if execution_time > self.config.opportunity_timeout_seconds:
                    await self.executor.cancel_execution(execution.id)
                    
                    central_logger.log(
                        level="WARNING",
                        message="Exécution annulée pour timeout",
                        category=LogCategory.TRADING,
                        execution_id=execution.id,
                        execution_time=execution_time
                    )
        
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.LOW, {
                'function': '_monitor_active_executions'
            })
    
    def _reset_daily_counters(self):
        """Réinitialise les compteurs quotidiens"""
        today = datetime.now().date()
        if today != self.last_reset_date:
            self.daily_profit = 0.0
            self.daily_trades = 0
            self.last_reset_date = today
            
            central_logger.log(
                level="INFO",
                message="Compteurs quotidiens réinitialisés",
                category=LogCategory.SYSTEM,
                date=today.isoformat()
            )
    
    def _check_safety_limits(self) -> bool:
        """Vérifie les limites de sécurité"""
        # Vérifier les pertes quotidiennes
        if abs(self.daily_profit) > self.config.max_daily_loss and self.daily_profit < 0:
            central_logger.log(
                level="WARNING",
                message="Limite quotidienne de perte atteinte",
                category=LogCategory.RISK,
                daily_profit=self.daily_profit,
                limit=self.config.max_daily_loss
            )
            return False
        
        # Vérifier le drawdown du portefeuille
        portfolio_value = portfolio_manager.get_portfolio_value()
        if portfolio_value > 0:
            # Calculer le drawdown depuis le démarrage
            # (Simplification - en production, utiliser l'historique complet)
            pass
        
        return True
    
    async def _emergency_stop(self, reason: str):
        """Arrêt d'urgence du bot"""
        central_logger.emergency_stop(
            message=f"Arrêt d'urgence du bot d'arbitrage: {reason}",
            reason=reason,
            daily_trades=self.daily_trades,
            daily_profit=self.daily_profit
        )
        
        if self.config.enable_notifications:
            await notifier.send_telegram(
                f"🚨 <b>ARRÊT D'URGENCE</b>\n"
                f"🤖 Bot: Arbitrage Cross-Chain\n"
                f"⚠️ Raison: {reason}\n"
                f"📊 Trades: {self.daily_trades}\n"
                f"💰 Profit: ${self.daily_profit:.2f}"
            )
        
        await self.stop()
    
    def _generate_performance_report(self) -> Dict[str, Any]:
        """Génère un rapport de performance"""
        try:
            # Statistiques du détecteur
            detector_stats = self.detector.get_statistics()
            
            # Statistiques de l'exécuteur
            executor_stats = self.executor.get_performance_stats()
            
            # Calcul de l'uptime
            uptime_hours = 0
            if self.start_time:
                uptime = datetime.now() - self.start_time
                uptime_hours = uptime.total_seconds() / 3600
            
            return {
                'uptime_hours': uptime_hours,
                'total_opportunities_found': detector_stats.get('total_opportunities_found', 0),
                'total_trades': executor_stats.get('total_executions', 0),
                'successful_trades': executor_stats.get('successful_executions', 0),
                'success_rate': executor_stats.get('success_rate', 0),
                'total_profit': executor_stats.get('net_profit', 0),
                'daily_trades': self.daily_trades,
                'daily_profit': self.daily_profit,
                'average_profit_per_trade': executor_stats.get('average_profit_per_trade', 0),
                'average_roi': executor_stats.get('average_roi_percentage', 0),
                'active_executions': len(self.executor.get_active_executions()),
                'consecutive_errors': self.consecutive_errors,
                'is_running': self.is_running
            }
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.LOW, {
                'function': '_generate_performance_report'
            })
            return {}
    
    def get_status(self) -> Dict[str, Any]:
        """Retourne le statut actuel du bot"""
        return {
            'is_running': self.is_running,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'monitored_chains': self.config.monitored_chains,
            'monitored_tokens': self.config.monitored_tokens,
            'daily_trades': self.daily_trades,
            'daily_profit': self.daily_profit,
            'consecutive_errors': self.consecutive_errors,
            'active_executions': len(self.executor.get_active_executions()) if hasattr(self, 'executor') else 0,
            'last_scan_time': self.last_scan_time,
            'scan_interval': self.config.scan_interval_seconds
        }
    
    async def get_current_opportunities(self) -> List[Dict[str, Any]]:
        """Retourne les opportunités actuelles"""
        try:
            opportunities = await self.detector.scan_opportunities()
            
            return [
                {
                    'id': opp.id,
                    'token': opp.token_symbol,
                    'buy_chain': opp.buy_chain,
                    'sell_chain': opp.sell_chain,
                    'buy_price': opp.buy_price,
                    'sell_price': opp.sell_price,
                    'price_difference_pct': opp.price_difference_pct,
                    'optimal_amount': opp.optimal_amount,
                    'net_profit': opp.net_profit,
                    'roi_percentage': opp.roi_percentage,
                    'confidence_score': opp.confidence_score,
                    'urgency_score': opp.urgency_score,
                    'execution_time_minutes': opp.execution_time_minutes,
                    'risk_level': opp.risk_level
                }
                for opp in opportunities
            ]
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.LOW, {
                'function': 'get_current_opportunities'
            })
            return []

# Fonction utilitaire pour créer et lancer le bot
async def create_and_run_bot(config_dict: Dict[str, Any] = None):
    """Crée et lance le bot avec la configuration donnée"""
    try:
        # Créer la configuration
        config = BotConfig(**config_dict) if config_dict else BotConfig()
        
        # Créer le bot
        bot = CrossChainArbitrageBot(config)
        
        # Démarrer
        await bot.start()
        
    except KeyboardInterrupt:
        central_logger.log(
            level="INFO",
            message="Arrêt du bot demandé par l'utilisateur",
            category=LogCategory.SYSTEM
        )
        if 'bot' in locals():
            await bot.stop()
    except Exception as e:
        error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.CRITICAL, {
            'function': 'create_and_run_bot'
        })
        if 'bot' in locals():
            await bot.stop()

if __name__ == "__main__":
    # Configuration d'exemple
    config = {
        'min_profit_usd': 25.0,
        'min_roi_percentage': 1.5,
        'max_concurrent_trades': 2,
        'max_capital_per_trade': 5000.0,
        'scan_interval_seconds': 30,
        'enable_notifications': True
    }
    
    asyncio.run(create_and_run_bot(config))
