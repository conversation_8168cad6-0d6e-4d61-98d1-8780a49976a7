#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 Fix Encoding Issues
Corrige les problèmes d'encodage Unicode sur Windows
"""

import os
import re
import sys
from pathlib import Path

def fix_logging_encoding():
    """Corrige l'encodage des logs dans tous les fichiers Python"""
    
    print("🔧 Correction des problèmes d'encodage...")
    
    # Patterns à rechercher et remplacer
    patterns = [
        # Configuration de logging basique
        (
            r'logging\.basicConfig\([^)]*\)',
            '''logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(filename, encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(filename, encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)'''
        ),
        
        # Configuration de logging avec filename
        (
            r'logging\.basicConfig\(\s*filename\s*=\s*([^,]+),([^)]*)\)',
            r'''logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(filename, encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(\1, encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)'''
        )
    ]
    
    # Fichiers à corriger
    python_files = []
    for root, dirs, files in os.walk('.'):
        # Ignorer certains dossiers
        dirs[:] = [d for d in dirs if d not in ['venv', '__pycache__', '.git', 'node_modules']]
        
        for file in files:
            if file.endswith('.py'):
                python_files.append(os.path.join(root, file))
    
    fixed_files = []
    
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # Appliquer les corrections
            for pattern, replacement in patterns:
                content = re.sub(pattern, replacement, content, flags=re.MULTILINE | re.DOTALL)
            
            # Si le fichier a été modifié
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                fixed_files.append(file_path)
                print(f"✅ Corrigé: {file_path}")
        
        except Exception as e:
            print(f"⚠️ Erreur avec {file_path}: {e}")
    
    return fixed_files

def create_unicode_safe_logger():
    """Crée un logger sécurisé pour Unicode"""
    
    logger_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 Unicode Safe Logger
Logger sécurisé pour les caractères Unicode sur Windows
"""

import logging
import sys
import os
from pathlib import Path

class UnicodeSafeLogger:
    """Logger qui gère correctement l'Unicode sur Windows"""
    
    def __init__(self, name, log_file=None):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.INFO)
        
        # Éviter les doublons de handlers
        if self.logger.handlers:
            self.logger.handlers.clear()
        
        # Formatter sans emojis pour les logs
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # Handler pour fichier avec encodage UTF-8
        if log_file:
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)
        
        # Handler pour console avec gestion d'erreur
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
    
    def safe_log(self, level, message):
        """Log un message en gérant les erreurs d'encodage"""
        try:
            # Remplacer les emojis par du texte
            safe_message = self.make_unicode_safe(message)
            getattr(self.logger, level)(safe_message)
        except UnicodeEncodeError:
            # Fallback: message sans caractères spéciaux
            ascii_message = message.encode('ascii', 'ignore').decode('ascii')
            getattr(self.logger, level)(f"[UNICODE_ERROR] {ascii_message}")
    
    def make_unicode_safe(self, text):
        """Remplace les emojis par du texte lisible"""
        emoji_replacements = {
            '🚀': '[START]',
            '✅': '[OK]',
            '❌': '[ERROR]',
            '⚠️': '[WARNING]',
            '📊': '[STATS]',
            '💰': '[MONEY]',
            '🎯': '[TARGET]',
            '📈': '[UP]',
            '📉': '[DOWN]',
            '🔧': '[CONFIG]',
            '⏸️': '[PAUSE]',
            '🛑': '[STOP]',
            '💡': '[INFO]',
            '🔍': '[SEARCH]',
            '📝': '[LOG]',
            '🎉': '[SUCCESS]',
            '⏱️': '[TIME]',
            '🧪': '[TEST]',
            '🔐': '[SECURE]',
            '📦': '[PACKAGE]',
            '🌐': '[NETWORK]',
            '💾': '[SAVE]',
            '🏦': '[BANK]',
            '🎮': '[GAME]',
            '📁': '[FOLDER]',
            '🔄': '[REFRESH]',
            '⚡': '[FAST]',
            '🎲': '[RANDOM]',
            '🔥': '[HOT]',
            '❄️': '[COLD]',
            '🌟': '[STAR]',
            '💎': '[DIAMOND]',
            '🚨': '[ALERT]',
            '🎪': '[CIRCUS]',
            '🎭': '[MASK]',
            '🎨': '[ART]',
            '🎵': '[MUSIC]',
            '🎬': '[MOVIE]',
            '📱': '[PHONE]',
            '💻': '[COMPUTER]',
            '⌚': '[WATCH]',
            '📷': '[CAMERA]',
            '🔊': '[SOUND]',
            '🔇': '[MUTE]',
            '🔋': '[BATTERY]',
            '🔌': '[PLUG]',
            '💡': '[BULB]',
            '🔦': '[FLASHLIGHT]',
            '🕯️': '[CANDLE]',
            '🧯': '[EXTINGUISHER]',
            '⛽': '[FUEL]',
            '🚗': '[CAR]',
            '🚕': '[TAXI]',
            '🚙': '[SUV]',
            '🚌': '[BUS]',
            '🚎': '[TROLLEY]',
            '🏎️': '[RACE_CAR]',
            '🚓': '[POLICE]',
            '🚑': '[AMBULANCE]',
            '🚒': '[FIRE_TRUCK]',
            '🚐': '[VAN]',
            '🛻': '[TRUCK]',
            '🚚': '[DELIVERY]',
            '🚛': '[SEMI]',
            '🚜': '[TRACTOR]'
        }
        
        safe_text = str(text)
        for emoji, replacement in emoji_replacements.items():
            safe_text = safe_text.replace(emoji, replacement)
        
        return safe_text
    
    def info(self, message):
        self.safe_log('info', message)
    
    def warning(self, message):
        self.safe_log('warning', message)
    
    def error(self, message):
        self.safe_log('error', message)
    
    def debug(self, message):
        self.safe_log('debug', message)
    
    def critical(self, message):
        self.safe_log('critical', message)

def get_unicode_safe_logger(name, log_file=None):
    """Fonction helper pour créer un logger sécurisé"""
    return UnicodeSafeLogger(name, log_file)

# Configuration globale pour éviter les erreurs d'encodage
if sys.platform.startswith('win'):
    # Sur Windows, forcer l'encodage UTF-8 pour stdout
    if hasattr(sys.stdout, 'reconfigure'):
        try:
            sys.stdout.reconfigure(encoding='utf-8')
        except:
            pass
'''
    
    # Sauvegarder le logger
    with open('utils/unicode_logger.py', 'w', encoding='utf-8') as f:
        f.write(logger_code)
    
    print("✅ Logger Unicode sécurisé créé: utils/unicode_logger.py")

def fix_environment_variables():
    """Configure les variables d'environnement pour l'Unicode"""
    
    env_fixes = '''
# Ajoutez ces lignes au début de vos scripts Python pour Windows
import os
import sys

# Configuration Unicode pour Windows
if sys.platform.startswith('win'):
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    if hasattr(sys.stdout, 'reconfigure'):
        try:
            sys.stdout.reconfigure(encoding='utf-8')
            sys.stderr.reconfigure(encoding='utf-8')
        except:
            pass
'''
    
    print("💡 Configuration d'environnement recommandée:")
    print(env_fixes)

def main():
    """Fonction principale"""
    print("🔧 CORRECTION DES PROBLÈMES D'ENCODAGE UNICODE")
    print("=" * 60)
    
    # 1. Corriger les configurations de logging
    fixed_files = fix_logging_encoding()
    
    # 2. Créer un logger sécurisé
    create_unicode_safe_logger()
    
    # 3. Afficher les recommandations
    fix_environment_variables()
    
    print("\n" + "=" * 60)
    print("✅ CORRECTIONS APPLIQUÉES")
    print("=" * 60)
    
    if fixed_files:
        print(f"📝 {len(fixed_files)} fichiers corrigés:")
        for file in fixed_files:
            print(f"   • {file}")
    else:
        print("📝 Aucun fichier de logging à corriger trouvé")
    
    print("\n💡 SOLUTIONS SUPPLÉMENTAIRES:")
    print("1. 🔧 Utilisez le nouveau logger: from utils.unicode_logger import get_unicode_safe_logger")
    print("2. 🌐 Définissez PYTHONIOENCODING=utf-8 dans vos variables d'environnement")
    print("3. 📝 Remplacez les emojis par du texte dans les logs critiques")
    
    print("\n🚀 COMMANDE POUR TESTER:")
    print("set PYTHONIOENCODING=utf-8 && python quick_test.py --test-1h")

if __name__ == "__main__":
    main()
