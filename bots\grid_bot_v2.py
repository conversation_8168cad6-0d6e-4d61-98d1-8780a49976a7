#!/usr/bin/env python3
"""
📈 Grid Trading Bot v2.0 - Version Sécurisée
Bot de grid trading avec gestion avancée des risques
"""

import os
import sys
import time
import hmac
import hashlib
import logging
from pathlib import Path
from typing import Dict, List, Optional

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

import requests
from config.settings import config
from utils.notifications import notifier
from monitoring.metrics import get_metrics_collector

class GridBotV2:
    """Bot de grid trading sécurisé"""
    
    def __init__(self, environment: str = "test"):
        """
        Initialise le grid bot
        
        Args:
            environment: 'test' ou 'prod'
        """
        self.environment = environment
        self.logger = self._setup_logging()
        self.config = config.get_trading_config(environment)
        
        # État du bot
        self.open_orders = {}
        self.is_running = False
        self.start_time = time.time()
        self.last_hourly_report = time.time()
        
        # Statistiques
        self.stats = {
            "orders_placed": 0,
            "orders_filled": 0,
            "total_profit": 0.0,
            "max_drawdown": 0.0,
            "initial_balance": 0.0
        }
        
        # Gestion du temps serveur
        self.server_time_offset = 0

        # Monitoring
        self.metrics = get_metrics_collector(f"GridBot", environment, "grid")

        self._validate_config()
        self._init_server_time()
    
    def _setup_logging(self):
        """Configure le logging"""
        log_file = f"grid_bot_{self.environment}.log"
        logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(filename, encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        return logging.getLogger(f"GridBot_{self.environment}")
    
    def _validate_config(self):
        """Valide la configuration"""
        required_fields = ["api_key", "api_secret", "base_url", "pair"]
        missing_fields = []
        
        for field in required_fields:
            if not self.config.get(field):
                missing_fields.append(field)
        
        if missing_fields:
            error_msg = f"Configuration incomplète: {missing_fields}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)
        
        self.logger.info(f"✅ Configuration {self.environment} validée")
    
    def _init_server_time(self):
        """Initialise le décalage horaire avec le serveur"""
        try:
            url = f"{self.config['base_url']}/api/v3/time"
            response = requests.get(url, timeout=10)
            server_time = response.json()['serverTime']
            self.server_time_offset = server_time - int(time.time() * 1000)
            self.logger.info("✅ Synchronisation horaire établie")
        except Exception as e:
            self.logger.error(f"❌ Erreur synchronisation horaire: {e}")
            raise
    
    def get_timestamp(self) -> int:
        """Retourne le timestamp avec correction serveur"""
        return int(time.time() * 1000) + self.server_time_offset
    
    def sign_request(self, query_string: str) -> str:
        """Signe une requête avec la clé secrète"""
        return hmac.new(
            self.config['api_secret'].encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    def send_signed_request(self, method: str, endpoint: str, params: Dict = None) -> Dict:
        """Envoie une requête signée à l'API Binance"""
        if params is None:
            params = {}
        
        params['timestamp'] = self.get_timestamp()
        query_string = '&'.join([f"{k}={v}" for k, v in params.items()])
        signature = self.sign_request(query_string)
        query_string += f"&signature={signature}"
        
        headers = {'X-MBX-APIKEY': self.config['api_key']}
        url = f"{self.config['base_url']}{endpoint}?{query_string}"
        
        try:
            response = requests.request(method, url, headers=headers, timeout=10)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            self.logger.error(f"❌ Erreur API: {e}")
            raise
    
    def get_balance(self) -> float:
        """Récupère le solde USDT"""
        try:
            result = self.send_signed_request("GET", "/api/v3/account")
            if 'balances' not in result:
                raise Exception(f"Réponse API invalide: {result}")
            
            usdt_balance = next(
                (float(asset['free']) for asset in result['balances'] if asset['asset'] == 'USDT'),
                0.0
            )
            return usdt_balance
        except Exception as e:
            self.logger.error(f"❌ Erreur récupération balance: {e}")
            raise
    
    def get_price(self, symbol: str = None) -> float:
        """Récupère le prix actuel d'un symbole"""
        symbol = symbol or self.config['pair']
        try:
            url = f"{self.config['base_url']}/api/v3/ticker/price"
            params = {'symbol': symbol}
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            return float(response.json()['price'])
        except Exception as e:
            self.logger.error(f"❌ Erreur récupération prix: {e}")
            raise
    
    def place_order(self, side: str, quantity: float, price: float) -> Dict:
        """Place un ordre limit"""
        try:
            params = {
                'symbol': self.config['pair'],
                'side': side,
                'type': 'LIMIT',
                'timeInForce': 'GTC',
                'quantity': f"{quantity:.6f}",
                'price': f"{price:.2f}"
            }
            
            result = self.send_signed_request("POST", "/api/v3/order", params)
            self.stats["orders_placed"] += 1
            
            self.logger.info(f"📝 Ordre placé: {side} {quantity:.6f} à {price:.2f}")
            return result
            
        except Exception as e:
            self.logger.error(f"❌ Erreur placement ordre: {e}")
            raise
    
    def get_open_orders(self) -> List[Dict]:
        """Récupère les ordres ouverts"""
        try:
            params = {'symbol': self.config['pair']}
            return self.send_signed_request("GET", "/api/v3/openOrders", params)
        except Exception as e:
            self.logger.error(f"❌ Erreur récupération ordres: {e}")
            return []
    
    def cancel_all_orders(self):
        """Annule tous les ordres ouverts"""
        try:
            params = {'symbol': self.config['pair']}
            result = self.send_signed_request("DELETE", "/api/v3/openOrders", params)
            self.logger.info(f"🗑️ {len(result)} ordres annulés")
            return result
        except Exception as e:
            self.logger.error(f"❌ Erreur annulation ordres: {e}")
            return []
    
    def calculate_grid_levels(self, current_price: float) -> tuple:
        """Calcule les niveaux de grille"""
        grid_spacing = self.config['grid_spacing']
        grid_size = self.config['grid_size']
        
        buy_levels = [current_price - (i * grid_spacing) for i in range(1, grid_size + 1)]
        sell_levels = [current_price + (i * grid_spacing) for i in range(1, grid_size + 1)]
        
        return buy_levels, sell_levels
    
    def setup_initial_grid(self, current_price: float):
        """Met en place la grille initiale"""
        self.logger.info(f"🏗️ Configuration de la grille initiale à {current_price:.2f}")
        
        buy_levels, sell_levels = self.calculate_grid_levels(current_price)
        order_size = self.config['order_size']
        
        # Placer les ordres d'achat
        for level in buy_levels:
            try:
                order = self.place_order("BUY", order_size, level)
                self.open_orders[order['orderId']] = {
                    'type': 'buy',
                    'price': level,
                    'quantity': order_size
                }
            except Exception as e:
                self.logger.error(f"❌ Erreur ordre BUY à {level}: {e}")
        
        # Placer les ordres de vente
        for level in sell_levels:
            try:
                order = self.place_order("SELL", order_size, level)
                self.open_orders[order['orderId']] = {
                    'type': 'sell',
                    'price': level,
                    'quantity': order_size
                }
            except Exception as e:
                self.logger.error(f"❌ Erreur ordre SELL à {level}: {e}")
        
        self.logger.info(f"✅ Grille configurée: {len(self.open_orders)} ordres")

        # Métriques de configuration de grille
        self.metrics.log_grid_setup(
            len(buy_levels) + len(sell_levels),
            self.config['grid_spacing'],
            current_price
        )
    
    def check_and_replace_orders(self, current_price: float):
        """Vérifie et remplace les ordres exécutés"""
        try:
            active_orders = self.get_open_orders()
            active_order_ids = {order['orderId'] for order in active_orders}
            
            # Identifier les ordres exécutés
            executed_orders = [
                order_id for order_id in self.open_orders 
                if order_id not in active_order_ids
            ]
            
            # Remplacer les ordres exécutés
            for order_id in executed_orders:
                order_info = self.open_orders.pop(order_id)
                self.stats["orders_filled"] += 1
                
                # Calculer le nouveau prix
                grid_spacing = self.config['grid_spacing']
                if order_info['type'] == 'buy':
                    # Ordre d'achat exécuté -> placer ordre de vente
                    new_price = order_info['price'] + grid_spacing
                    new_side = "SELL"
                    new_type = 'sell'
                else:
                    # Ordre de vente exécuté -> placer ordre d'achat
                    new_price = order_info['price'] - grid_spacing
                    new_side = "BUY"
                    new_type = 'buy'
                
                try:
                    new_order = self.place_order(new_side, order_info['quantity'], new_price)
                    self.open_orders[new_order['orderId']] = {
                        'type': new_type,
                        'price': new_price,
                        'quantity': order_info['quantity']
                    }
                    
                    # Calculer le profit (approximatif)
                    if order_info['type'] == 'buy':
                        profit = grid_spacing * order_info['quantity']
                        self.stats["total_profit"] += profit
                    
                    self.logger.info(f"🔄 Ordre remplacé: {new_side} à {new_price:.2f}")
                    notifier.send_trade_executed(new_side, order_info['quantity'], new_price)

                    # Métriques de trading
                    self.metrics.log_trade(
                        self.config['pair'], new_side, order_info['quantity'],
                        new_price, "executed", profit_loss=profit if order_info['type'] == 'buy' else 0
                    )
                    self.metrics.log_order_filled(new_side, new_price, profit if order_info['type'] == 'buy' else 0)
                    
                except Exception as e:
                    self.logger.error(f"❌ Erreur remplacement ordre: {e}")
        
        except Exception as e:
            self.logger.error(f"❌ Erreur vérification ordres: {e}")
    
    def check_stop_conditions(self, current_price: float) -> bool:
        """Vérifie les conditions d'arrêt"""
        stop_loss = current_price * (1 - self.config['stop_loss_percent'] / 100)
        take_profit = current_price * (1 + self.config['take_profit_percent'] / 100)
        
        if current_price <= stop_loss:
            self.logger.warning(f"❌ Stop-Loss atteint: {current_price:.2f} <= {stop_loss:.2f}")
            notifier.send_stop_loss(current_price, f"GridBot_{self.environment}")
            return True
        
        if current_price >= take_profit:
            self.logger.info(f"🎉 Take-Profit atteint: {current_price:.2f} >= {take_profit:.2f}")
            notifier.send_take_profit(current_price, f"GridBot_{self.environment}")
            return True
        
        return False
    
    def send_hourly_report(self):
        """Envoie un rapport horaire"""
        try:
            current_balance = self.get_balance()
            pnl = current_balance - self.stats["initial_balance"]
            pnl_percent = (pnl / self.stats["initial_balance"]) * 100 if self.stats["initial_balance"] > 0 else 0
            
            notifier.send_hourly_summary(
                current_balance, 
                pnl, 
                pnl_percent, 
                len(self.open_orders)
            )
            
            self.last_hourly_report = time.time()
            
        except Exception as e:
            self.logger.error(f"❌ Erreur rapport horaire: {e}")
    
    def run(self):
        """Lance le bot en mode continu"""
        try:
            # Vérifications initiales
            initial_balance = self.get_balance()
            self.stats["initial_balance"] = initial_balance
            
            if initial_balance < self.config['capital_max']:
                error_msg = f"Capital insuffisant: {initial_balance} < {self.config['capital_max']}"
                self.logger.error(error_msg)
                notifier.send_error(error_msg, f"GridBot_{self.environment}")
                return
            
            # Configuration initiale
            current_price = self.get_price()
            self.setup_initial_grid(current_price)
            
            # Notification de démarrage
            notifier.send_bot_start(
                f"GridBot_{self.environment}", 
                initial_balance, 
                current_price, 
                self.environment
            )
            
            self.is_running = True
            self.logger.info(f"🚀 GridBot {self.environment} démarré")

            # Démarrer le monitoring
            self.metrics.start_monitoring()
            
            # Boucle principale
            while self.is_running:
                try:
                    current_price = self.get_price()
                    current_time = time.strftime('%Y-%m-%d %H:%M:%S')
                    
                    self.logger.info(f"{current_time} - 📊 Prix: {current_price:.2f} USDT")
                    
                    # Vérifier les conditions d'arrêt
                    if self.check_stop_conditions(current_price):
                        break
                    
                    # Vérifier et remplacer les ordres
                    self.check_and_replace_orders(current_price)
                    
                    # Rapport horaire
                    if time.time() - self.last_hourly_report >= 3600:
                        self.send_hourly_report()
                    
                    # Pause
                    time.sleep(30)
                    
                except KeyboardInterrupt:
                    self.logger.info("🛑 Arrêt demandé par l'utilisateur")
                    break
                except Exception as e:
                    self.logger.error(f"❌ Erreur dans la boucle: {e}")
                    notifier.send_error(f"Erreur GridBot: {str(e)}", f"GridBot_{self.environment}")
                    time.sleep(60)  # Pause plus longue en cas d'erreur
        
        except Exception as e:
            self.logger.error(f"❌ Erreur fatale: {e}")
            notifier.send_error(f"Erreur fatale GridBot: {str(e)}", f"GridBot_{self.environment}")
        
        finally:
            self.is_running = False

            # Arrêter le monitoring
            self.metrics.stop_monitoring()

            # Annuler tous les ordres ouverts
            try:
                self.cancel_all_orders()
            except:
                pass
            
            # Rapport final
            runtime = time.time() - self.start_time
            final_msg = (
                f"🏁 GridBot {self.environment} arrêté\n"
                f"⏱️ Durée: {runtime/3600:.1f}h\n"
                f"📝 Ordres placés: {self.stats['orders_placed']}\n"
                f"✅ Ordres exécutés: {self.stats['orders_filled']}\n"
                f"💰 Profit estimé: {self.stats['total_profit']:.4f} USDT"
            )
            
            self.logger.info(final_msg)
            notifier.send_telegram(final_msg)

def main():
    """Point d'entrée principal"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Grid Trading Bot v2.0")
    parser.add_argument(
        "--env", 
        choices=["test", "prod"], 
        default="test",
        help="Environnement d'exécution (test ou prod)"
    )
    
    args = parser.parse_args()
    
    try:
        bot = GridBotV2(args.env)
        bot.run()
    except Exception as e:
        logging.error(f"❌ Erreur fatale: {e}")
        notifier.send_error(f"Erreur fatale GridBot: {str(e)}", f"GridBot_{args.env}")
        sys.exit(1)

if __name__ == "__main__":
    main()
