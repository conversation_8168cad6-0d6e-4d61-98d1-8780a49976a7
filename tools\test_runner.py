#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 Test Runner - Utilitaire de test consolidé
Tests de connexion et validation pour BotCrypto
"""

import os
import time
import hmac
import hashlib
import requests
from pathlib import Path
from dotenv import load_dotenv

class TestRunner:
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.load_environment()
        
    def load_environment(self):
        """Charge les variables d'environnement"""
        env_file = self.project_root / ".env.local"
        if env_file.exists():
            load_dotenv(env_file)
            print("✅ Configuration chargée")
        else:
            print("❌ Fichier .env.local manquant")
            return False
        return True
    
    def test_api_connection(self, api_key, api_secret, base_url, env_name):
        """Teste une connexion API"""
        print(f"\n🔐 Test {env_name}...")
        
        if not api_key or not api_secret:
            print(f"❌ Clés {env_name} manquantes")
            return False
        
        try:
            # Test server time
            time_response = requests.get(f'{base_url}/api/v3/time')
            if time_response.status_code != 200:
                print(f"❌ Erreur server time: {time_response.status_code}")
                return False
            
            server_time = time_response.json()['serverTime']
            
            # Test account info
            query_string = f'timestamp={server_time}'
            signature = hmac.new(
                api_secret.encode('utf-8'),
                query_string.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            
            url = f'{base_url}/api/v3/account?{query_string}&signature={signature}'
            headers = {'X-MBX-APIKEY': api_key}
            
            account_response = requests.get(url, headers=headers)
            
            if account_response.status_code == 200:
                account_data = account_response.json()
                print(f"✅ {env_name} - Connexion réussie")
                print(f"   Type: {account_data.get('accountType', 'N/A')}")
                
                # Balances significatives
                balances = account_data.get('balances', [])
                non_zero = [b for b in balances if float(b['free']) > 0]
                print(f"   Balances: {len(non_zero)} actives")
                
                return True
            else:
                print(f"❌ {env_name} - Erreur: {account_response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ {env_name} - Exception: {e}")
            return False
    
    def test_all_connections(self):
        """Teste toutes les connexions configurées"""
        print("🧪 TEST DE TOUTES LES CONNEXIONS")
        print("=" * 50)
        
        results = {}
        
        # Test Production
        prod_key = os.getenv("safe_bot_PROD_API_KEY")
        prod_secret = os.getenv("safe_bot_PROD_API_SECRET")
        prod_url = os.getenv("BASE_URL", "https://api.binance.com")
        results["Production"] = self.test_api_connection(prod_key, prod_secret, prod_url, "Production")
        
        # Test Testnet
        test_key = os.getenv("safe_bot_TEST_API_KEY")
        test_secret = os.getenv("safe_bot_TEST_API_SECRET")
        test_url = os.getenv("TEST_BASE_URL", "https://testnet.binance.vision")
        results["Testnet"] = self.test_api_connection(test_key, test_secret, test_url, "Testnet")
        
        return results
    
    def validate_security(self):
        """Valide la sécurité du projet"""
        print("\n🔒 VALIDATION SÉCURITÉ")
        print("=" * 30)
        
        issues = []
        
        # Vérifier qu'aucun .env n'est suivi par Git
        try:
            import subprocess
            result = subprocess.run(['git', 'ls-files'], capture_output=True, text=True, cwd=self.project_root)
            tracked_files = result.stdout.strip().split('\n')
            
            env_tracked = [f for f in tracked_files if '.env' in f and not f.endswith('.example')]
            if env_tracked:
                issues.append(f"Fichiers .env suivis par Git: {env_tracked}")
            else:
                print("✅ Aucun fichier .env suivi par Git")
        except:
            print("⚠️ Impossible de vérifier Git")
        
        # Vérifier .gitignore
        gitignore_path = self.project_root / ".gitignore"
        if gitignore_path.exists():
            with open(gitignore_path, 'r') as f:
                gitignore_content = f.read()
            
            required_patterns = ['.env', '*.env', 'private-docs/']
            missing = [p for p in required_patterns if p not in gitignore_content]
            
            if missing:
                issues.append(f"Patterns manquants dans .gitignore: {missing}")
            else:
                print("✅ .gitignore correctement configuré")
        
        return len(issues) == 0, issues
    
    def run_full_test(self):
        """Lance tous les tests"""
        print("🚀 LANCEMENT DES TESTS COMPLETS")
        print("=" * 60)
        
        if not self.load_environment():
            return
        
        # Tests de connexion
        connection_results = self.test_all_connections()
        
        # Tests de sécurité
        security_ok, security_issues = self.validate_security()
        
        # Rapport final
        print("\n" + "=" * 60)
        print("📊 RAPPORT FINAL")
        print("=" * 60)
        
        print("🔗 Connexions:")
        for env, status in connection_results.items():
            icon = "✅" if status else "❌"
            print(f"   {icon} {env}")
        
        print(f"\n🔒 Sécurité: {'✅ OK' if security_ok else '❌ Problèmes'}")
        if security_issues:
            for issue in security_issues:
                print(f"   ⚠️ {issue}")
        
        all_connections_ok = all(connection_results.values())
        
        if all_connections_ok and security_ok:
            print("\n🎉 TOUS LES TESTS PASSENT !")
            print("✅ Votre projet est prêt à être utilisé")
        else:
            print("\n⚠️ CERTAINS TESTS ÉCHOUENT")
            print("🔧 Vérifiez votre configuration")

def main():
    """Point d'entrée principal"""
    runner = TestRunner()
    runner.run_full_test()

if __name__ == "__main__":
    main()
