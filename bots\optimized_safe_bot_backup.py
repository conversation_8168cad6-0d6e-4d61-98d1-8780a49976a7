#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 Optimized Safe Bot - Bot de Trading Optimisé pour Rentabilité Maximale
Version avancée avec optimisation automatique et réplication test->prod
"""

import os
import sys
import time
import hmac
import hashlib
import requests
import logging
import argparse
import json
import threading
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
from dotenv import load_dotenv
from logging.handlers import RotatingFileHandler
from typing import Dict, List, Optional, Tuple
import statistics
import numpy as np

class OptimizedSafeBot:
    """Bot de trading optimisé avec intelligence artificielle"""
    
    def __init__(self, mode="testnet"):
        self.mode = mode
        self.project_root = Path(__file__).parent.parent
        self.load_environment()
        self.setup_configuration()
        self.setup_advanced_logging()
        self.setup_performance_tracking()
        self.setup_optimization_engine()
        
    def load_environment(self):
        """Charge les variables d'environnement"""
        env_file = self.project_root / ".env.local"
        if env_file.exists():
            load_dotenv(env_file)
            print("✅ Configuration avancée chargée")
        else:
            print("❌ Fichier .env.local manquant")
            sys.exit(1)
    
    def setup_configuration(self):
        """Configure les paramètres optimisés selon le mode"""
        if self.mode == "testnet":
            self.api_key = os.getenv("safe_bot_TEST_API_KEY")
            self.api_secret = os.getenv("safe_bot_TEST_API_SECRET")
            self.base_url = os.getenv("TEST_BASE_URL", "https://testnet.binance.vision")
            self.capital_max = float(os.getenv("CAPITAL_MAX", 1000))
            self.log_prefix = "optimized_safe_bot_testnet"
        else:  # production
            self.api_key = os.getenv("safe_bot_PROD_API_KEY")
            self.api_secret = os.getenv("safe_bot_PROD_API_SECRET")
            self.base_url = os.getenv("BASE_URL", "https://api.binance.com")
            self.capital_max = float(os.getenv("CAPITAL_MAX", 1000)) * 0.1  # 10% en prod pour sécurité
            self.log_prefix = "optimized_safe_bot_production"
        
        if not self.api_key or not self.api_secret:
            print(f"❌ Clés API {self.mode} manquantes")
            sys.exit(1)
        
        # Configuration de trading optimisée
        self.pair = "BTCUSDT"
        self.grid_size = int(os.getenv("GRID_SIZE", 15))
        self.grid_spacing = float(os.getenv("GRID_SPACING", 50))
        self.order_size = float(os.getenv("ORDER_SIZE", 0.002))
        self.stop_loss_percent = float(os.getenv("STOP_LOSS_PERCENT", 2))
        self.take_profit_percent = float(os.getenv("TAKE_PROFIT_PERCENT", 3))
        
        # Nouvelles métriques avancées
        self.min_volume_24h = float(os.getenv("MIN_VOLUME_24H", 1000000))
        self.min_price_change = float(os.getenv("MIN_PRICE_CHANGE", 0.5))
        self.max_spread = float(os.getenv("MAX_SPREAD", 0.1))
        self.min_profit_threshold = float(os.getenv("MIN_PROFIT_THRESHOLD", 0.2))
        
        # Configuration d'optimisation
        self.auto_optimization = os.getenv("AUTO_OPTIMIZATION", "true").lower() == "true"
        self.optimization_interval = int(os.getenv("OPTIMIZATION_INTERVAL", 3600))
        self.performance_threshold = float(os.getenv("PERFORMANCE_THRESHOLD", 0.1))
        
        # Variables de trading
        self.open_orders = {}
        self.price_history = []
        self.trade_history = []
        self.last_optimization = time.time()
        
        # Statistiques avancées
        self.stats = {
            "trades_executed": 0,
            "profitable_trades": 0,
            "total_profit": 0.0,
            "total_loss": 0.0,
            "profit_factor": 0.0,
            "win_rate": 0.0,
            "avg_profit_per_trade": 0.0,
            "max_drawdown": 0.0,
            "sharpe_ratio": 0.0,
            "start_time": datetime.now(),
            "start_balance": 0.0,
            "current_balance": 0.0,
            "best_config": None,
            "optimization_count": 0
        }
    
        def setup_advanced_logging(self):
        """Configure le logging rotatif avancé avec support Unicode"""
        log_size_mb = int(os.getenv("LOG_ROTATION_SIZE", 10))
        retention_days = int(os.getenv("LOG_RETENTION_DAYS", 7))
        detailed_logging = os.getenv("DETAILED_LOGGING", "true").lower() == "true"
        
        # Configuration Unicode pour Windows
        if sys.platform.startswith('win'):
            os.environ['PYTHONIOENCODING'] = 'utf-8'
            if hasattr(sys.stdout, 'reconfigure'):
                try:
                    sys.stdout.reconfigure(encoding='utf-8')
                    sys.stderr.reconfigure(encoding='utf-8')
                except:
                    pass
        
        # Logger principal avec rotation et encodage UTF-8
        log_file = f"{self.log_prefix}.log"
        handler = RotatingFileHandler(
            log_file, 
            maxBytes=log_size_mb * 1024 * 1024,  # MB to bytes
            backupCount=retention_days,
            encoding='utf-8'  # Forcer l'encodage UTF-8
        )
        
        # Format détaillé
        if detailed_logging:
            formatter = logging.Formatter(
                '%(asctime)s - %(levelname)s - [%(funcName)s:%(lineno)d] - %(message)s'
            )
        else:
            formatter = logging.Formatter(
                '%(asctime)s - %(levelname)s - %(message)s'
            )
        
        handler.setFormatter(formatter)
        
        # Configuration du logger
        self.logger = logging.getLogger(f"OptimizedSafeBot_{self.mode}")
        self.logger.setLevel(logging.INFO)
        self.logger.addHandler(handler)
        
        # Logger de performance séparé avec encodage UTF-8
        perf_handler = RotatingFileHandler(
            f"{self.log_prefix}_performance.log",
            maxBytes=5 * 1024 * 1024,  # 5MB
            backupCount=3,
            encoding='utf-8'  # Forcer l'encodage UTF-8
        )
        perf_formatter = logging.Formatter(
            '%(asctime)s - PERF - %(message)s'
        )
        perf_handler.setFormatter(perf_formatter)
        
        self.perf_logger = logging.getLogger(f"Performance_{self.mode}")
        self.perf_logger.setLevel(logging.INFO)
        self.perf_logger.addHandler(perf_handler)
        
        print(f"[LOG] Logging avancé configuré: {log_file}")
    def safe_log(self, level, message):
        """Log un message en gérant les erreurs d'encodage"""
        try:
            # Remplacer les emojis par du texte pour éviter les erreurs
            safe_message = self.make_unicode_safe(str(message))
            getattr(self.logger, level)(safe_message)
        except UnicodeEncodeError:
            # Fallback: message sans caractères spéciaux
            ascii_message = str(message).encode('ascii', 'ignore').decode('ascii')
            getattr(self.logger, level)(f"[UNICODE_ERROR] {ascii_message}")
        except Exception as e:
            # Dernier recours
            print(f"[LOG_ERROR] {message}")
    
    def make_unicode_safe(self, text):
        """Remplace les emojis par du texte lisible"""
        emoji_replacements = {
            '🚀': '[START]', '✅': '[OK]', '❌': '[ERROR]', '⚠️': '[WARNING]',
            '📊': '[STATS]', '💰': '[MONEY]', '🎯': '[TARGET]', '📈': '[UP]',
            '📉': '[DOWN]', '🔧': '[CONFIG]', '⏸️': '[PAUSE]', '🛑': '[STOP]',
            '💡': '[INFO]', '🔍': '[SEARCH]', '📝': '[LOG]', '🎉': '[SUCCESS]',
            '⏱️': '[TIME]', '🧪': '[TEST]', '🔐': '[SECURE]', '📦': '[PACKAGE]',
            '🌐': '[NETWORK]', '💾': '[SAVE]', '🏦': '[BANK]', '🎮': '[GAME]',
            '📁': '[FOLDER]', '🔄': '[REFRESH]', '⚡': '[FAST]', '🎲': '[RANDOM]'
        }
        
        safe_text = str(text)
        for emoji, replacement in emoji_replacements.items():
            safe_text = safe_text.replace(emoji, replacement)
        
        return safe_text

    
    def setup_performance_tracking(self):
        """Configure le suivi de performance en temps réel"""
        self.performance_data = {
            "hourly_profits": [],
            "trade_execution_times": [],
            "price_predictions": [],
            "market_conditions": [],
            "optimization_results": []
        }
        
        # Thread pour le monitoring continu
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(target=self._continuous_monitoring)
        self.monitor_thread.daemon = True
        
    def setup_optimization_engine(self):
        """Configure le moteur d'optimisation automatique"""
        self.optimization_engine = {
            "parameter_ranges": {
                "grid_spacing": (20, 200, 10),  # (min, max, step)
                "order_size": (0.001, 0.01, 0.001),
                "stop_loss_percent": (1, 5, 0.5),
                "take_profit_percent": (1, 10, 0.5)
            },
            "tested_configs": [],
            "best_performance": -float('inf'),
            "current_test_start": None,
            "test_duration": 3600  # 1 heure par test
        }
    
    def sign_request(self, query_string: str) -> str:
        """Signe une requête API"""
        return hmac.new(
            self.api_secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    def get_server_time(self) -> int:
        """Récupère le temps du serveur"""
        try:
            response = requests.get(f"{self.base_url}/api/v3/time")
            if response.status_code == 200:
                return response.json()['serverTime']
        except Exception as e:
            self.safe_log("error", f"❌ Erreur temps serveur: {e}"))
        return int(time.time() * 1000)
    
    def get_current_price(self) -> Optional[float]:
        """Récupère le prix actuel avec validation"""
        try:
            response = requests.get(f"{self.base_url}/api/v3/ticker/price?symbol={self.pair}")
            if response.status_code == 200:
                price = float(response.json()['price'])
                
                # Validation du prix
                if self.price_history:
                    last_price = self.price_history[-1]['price']
                    price_change = abs(price - last_price) / last_price * 100
                    
                    if price_change > 10:  # Changement > 10% suspect
                        self.safe_log("warning", f"⚠️ Changement de prix suspect: {price_change:.2f}%"))
                        return None
                
                # Enregistrer dans l'historique
                self.price_history.append({
                    'timestamp': time.time(),
                    'price': price
                })
                
                # Garder seulement les 1000 derniers prix
                if len(self.price_history) > 1000:
                    self.price_history = self.price_history[-1000:]
                
                return price
                
        except Exception as e:
            self.safe_log("error", f"❌ Erreur prix: {e}"))
        return None
    
    def get_market_metrics(self) -> Dict:
        """Récupère les métriques de marché avancées"""
        try:
            # Volume 24h
            ticker_response = requests.get(f"{self.base_url}/api/v3/ticker/24hr?symbol={self.pair}")
            if ticker_response.status_code != 200:
                return {}
            
            ticker_data = ticker_response.json()
            
            # Spread (bid-ask)
            depth_response = requests.get(f"{self.base_url}/api/v3/depth?symbol={self.pair}&limit=5")
            spread = 0
            if depth_response.status_code == 200:
                depth_data = depth_response.json()
                if depth_data['bids'] and depth_data['asks']:
                    best_bid = float(depth_data['bids'][0][0])
                    best_ask = float(depth_data['asks'][0][0])
                    spread = (best_ask - best_bid) / best_bid * 100
            
            metrics = {
                'volume_24h': float(ticker_data['volume']),
                'price_change_24h': float(ticker_data['priceChangePercent']),
                'spread_percent': spread,
                'high_24h': float(ticker_data['highPrice']),
                'low_24h': float(ticker_data['lowPrice']),
                'trades_count_24h': int(ticker_data['count'])
            }
            
            return metrics
            
        except Exception as e:
            self.safe_log("error", f"❌ Erreur métriques marché: {e}"))
            return {}
    
    def validate_market_conditions(self) -> bool:
        """Valide les conditions de marché pour le trading"""
        metrics = self.get_market_metrics()
        if not metrics:
            return False
        
        # Vérifications
        conditions = {
            'volume_ok': metrics.get('volume_24h', 0) >= self.min_volume_24h,
            'volatility_ok': abs(metrics.get('price_change_24h', 0)) >= self.min_price_change,
            'spread_ok': metrics.get('spread_percent', 100) <= self.max_spread
        }
        
        all_good = all(conditions.values())
        
        if not all_good:
            self.safe_log("info", f"⚠️ Conditions de marché non optimales: {conditions}"))
        
        return all_good

    def get_balances(self) -> Tuple[Optional[float], Optional[float]]:
        """Récupère les balances BTC et USDT"""
        try:
            timestamp = self.get_server_time()
            params = {'timestamp': timestamp}

            query_string = '&'.join([f"{k}={v}" for k, v in params.items()])
            signature = self.sign_request(query_string)
            query_string += f"&signature={signature}"

            url = f"{self.base_url}/api/v3/account"
            headers = {'X-MBX-APIKEY': self.api_key}

            response = requests.get(url, headers=headers, params=query_string)

            if response.status_code == 200:
                account_data = response.json()
                btc_balance = 0.0
                usdt_balance = 0.0

                for balance in account_data['balances']:
                    if balance['asset'] == 'BTC':
                        btc_balance = float(balance['free'])
                    elif balance['asset'] == 'USDT':
                        usdt_balance = float(balance['free'])

                return btc_balance, usdt_balance
            else:
                self.safe_log("error", f"❌ Erreur balances: {response.status_code}"))
                return None, None

        except Exception as e:
            self.safe_log("error", f"❌ Erreur récupération balances: {e}"))
            return None, None

    def place_optimized_order(self, side: str, quantity: float, price: float) -> Optional[Dict]:
        """Place un ordre optimisé avec validation avancée"""
        try:
            # Validation des paramètres
            if quantity <= 0 or price <= 0:
                self.safe_log("error", f"❌ Paramètres invalides: quantity={quantity}, price={price}"))
                return None

            # Vérification du capital disponible
            btc_balance, usdt_balance = self.get_balances()
            if btc_balance is None or usdt_balance is None:
                return None

            required_capital = quantity * price
            if side == "BUY" and usdt_balance < required_capital * 1.01:  # +1% marge
                self.safe_log("warning", f"⚠️ Capital insuffisant pour BUY: {usdt_balance} < {required_capital}"))
                return None
            elif side == "SELL" and btc_balance < quantity:
                self.safe_log("warning", f"⚠️ BTC insuffisant pour SELL: {btc_balance} < {quantity}"))
                return None

            # Calcul du profit potentiel
            current_price = self.get_current_price()
            if current_price:
                if side == "BUY":
                    potential_profit = (current_price - price) / price * 100
                else:
                    potential_profit = (price - current_price) / current_price * 100

                if potential_profit < self.min_profit_threshold:
                    self.safe_log("info", f"📊 Profit potentiel trop faible: {potential_profit:.2f}%"))
                    return None

            # Placement de l'ordre
            timestamp = self.get_server_time()
            params = {
                'symbol': self.pair,
                'side': side,
                'type': 'LIMIT',
                'timeInForce': 'GTC',
                'quantity': f"{quantity:.8f}",
                'price': f"{price:.2f}",
                'timestamp': timestamp
            }

            query_string = '&'.join([f"{k}={v}" for k, v in params.items()])
            signature = self.sign_request(query_string)
            query_string += f"&signature={signature}"

            url = f"{self.base_url}/api/v3/order"
            headers = {'X-MBX-APIKEY': self.api_key}

            start_time = time.time()
            response = requests.post(url, headers=headers, data=query_string)
            execution_time = time.time() - start_time

            if response.status_code == 200:
                order_data = response.json()

                # Enregistrement détaillé
                trade_info = {
                    'timestamp': time.time(),
                    'side': side,
                    'quantity': quantity,
                    'price': price,
                    'order_id': order_data['orderId'],
                    'execution_time': execution_time,
                    'potential_profit': potential_profit if 'potential_profit' in locals() else 0,
                    'market_conditions': self.get_market_metrics()
                }

                self.trade_history.append(trade_info)
                self.performance_data['trade_execution_times'].append(execution_time)

                message = f"✅ Ordre optimisé {side}: {quantity:.8f} {self.pair} à {price:.2f} USDT (Profit potentiel: {potential_profit:.2f}%)"
                print(message)
                self.logger.info(message)
                self.perf_logger.info(f"TRADE_PLACED,{side},{quantity},{price},{potential_profit:.2f},{execution_time:.3f}")

                self.stats["trades_executed"] += 1
                return order_data
            else:
                error_msg = f"❌ Erreur ordre {side}: {response.status_code} - {response.text}"
                print(error_msg)
                self.logger.error(error_msg)
                return None

        except Exception as e:
            self.safe_log("error", f"❌ Erreur placement ordre: {e}"))
            return None

    def calculate_optimal_grid_levels(self, current_price: float) -> Tuple[List[float], List[float]]:
        """Calcule les niveaux de grille optimaux"""
        buy_levels = []
        sell_levels = []

        # Analyse de volatilité pour ajuster l'espacement
        if len(self.price_history) >= 20:
            recent_prices = [p['price'] for p in self.price_history[-20:]]
            volatility = np.std(recent_prices) / np.mean(recent_prices) * 100

            # Ajustement dynamique de l'espacement
            if volatility > 2:  # Haute volatilité
                adjusted_spacing = self.grid_spacing * 1.5
            elif volatility < 0.5:  # Faible volatilité
                adjusted_spacing = self.grid_spacing * 0.7
            else:
                adjusted_spacing = self.grid_spacing
        else:
            adjusted_spacing = self.grid_spacing

        # Calcul des niveaux
        for i in range(1, self.grid_size // 2 + 1):
            buy_price = current_price - (adjusted_spacing * i)
            sell_price = current_price + (adjusted_spacing * i)

            if buy_price > 0:
                buy_levels.append(buy_price)
            sell_levels.append(sell_price)

        self.safe_log("info", f"📊 Grille calculée: {len(buy_levels)} achats, {len(sell_levels)} ventes (espacement: {adjusted_spacing:.2f})"))
        return buy_levels, sell_levels

    def intelligent_trading_strategy(self):
        """Stratégie de trading intelligente avec optimisation"""
        # Validation des conditions de marché
        if not self.validate_market_conditions():
            self.safe_log("info", f"⏸️ Conditions de marché non favorables, pause trading"))
            return

        current_price = self.get_current_price()
        if not current_price:
            return

        btc_balance, usdt_balance = self.get_balances()
        if btc_balance is None or usdt_balance is None:
            return

        # Calcul de la valeur totale
        btc_value = btc_balance * current_price
        total_value = btc_value + usdt_balance

        # Mise à jour des statistiques
        if self.stats["start_balance"] == 0:
            self.stats["start_balance"] = total_value
        self.stats["current_balance"] = total_value

        # Calcul du profit/perte actuel
        current_pnl = total_value - self.stats["start_balance"]
        self.stats["total_profit"] = max(0, current_pnl)
        self.stats["total_loss"] = max(0, -current_pnl)

        # Stratégie de rééquilibrage intelligent
        target_btc_ratio = 0.5  # 50% BTC, 50% USDT
        current_btc_ratio = btc_value / total_value if total_value > 0 else 0

        imbalance = abs(current_btc_ratio - target_btc_ratio)

        # Seuil d'action basé sur la volatilité
        action_threshold = 0.1  # 10% de déséquilibre minimum

        if imbalance > action_threshold:
            if current_btc_ratio < target_btc_ratio:
                # Acheter du BTC
                buy_amount = (target_btc_ratio * total_value - btc_value) / current_price
                buy_amount = min(buy_amount, self.order_size)

                if buy_amount > 0.001 and usdt_balance >= buy_amount * current_price * 1.01:
                    buy_price = current_price - self.grid_spacing
                    self.place_optimized_order("BUY", buy_amount, buy_price)

            else:
                # Vendre du BTC
                sell_amount = min((btc_value - target_btc_ratio * total_value) / current_price, self.order_size)

                if sell_amount > 0.001 and btc_balance >= sell_amount:
                    sell_price = current_price + self.grid_spacing
                    self.place_optimized_order("SELL", sell_amount, sell_price)

        # Enregistrement des métriques de performance
        self.perf_logger.info(f"STRATEGY_EXEC,{current_price},{btc_balance},{usdt_balance},{current_pnl:.2f},{imbalance:.3f}")

    def calculate_performance_metrics(self) -> Dict:
        """Calcule les métriques de performance avancées"""
        if not self.trade_history:
            return {}

        # Calculs de base
        profitable_trades = [t for t in self.trade_history if t.get('potential_profit', 0) > 0]
        total_trades = len(self.trade_history)

        win_rate = len(profitable_trades) / total_trades * 100 if total_trades > 0 else 0

        # Profit factor
        total_profit = sum(t.get('potential_profit', 0) for t in profitable_trades)
        total_loss = sum(abs(t.get('potential_profit', 0)) for t in self.trade_history if t.get('potential_profit', 0) < 0)
        profit_factor = total_profit / total_loss if total_loss > 0 else float('inf')

        # Temps d'exécution moyen
        avg_execution_time = statistics.mean(self.performance_data['trade_execution_times']) if self.performance_data['trade_execution_times'] else 0

        # Sharpe ratio simplifié
        if len(self.price_history) >= 10:
            returns = []
            for i in range(1, len(self.price_history)):
                ret = (self.price_history[i]['price'] - self.price_history[i-1]['price']) / self.price_history[i-1]['price']
                returns.append(ret)

            if returns:
                avg_return = statistics.mean(returns)
                std_return = statistics.stdev(returns) if len(returns) > 1 else 0
                sharpe_ratio = avg_return / std_return if std_return > 0 else 0
            else:
                sharpe_ratio = 0
        else:
            sharpe_ratio = 0

        metrics = {
            'total_trades': total_trades,
            'profitable_trades': len(profitable_trades),
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'avg_execution_time': avg_execution_time,
            'sharpe_ratio': sharpe_ratio,
            'current_balance': self.stats["current_balance"],
            'total_pnl': self.stats["current_balance"] - self.stats["start_balance"],
            'pnl_percentage': ((self.stats["current_balance"] - self.stats["start_balance"]) / self.stats["start_balance"] * 100) if self.stats["start_balance"] > 0 else 0
        }

        return metrics

    def optimize_parameters(self):
        """Optimise automatiquement les paramètres de trading"""
        if not self.auto_optimization:
            return

        current_time = time.time()
        if current_time - self.last_optimization < self.optimization_interval:
            return

        self.safe_log("info", f"🔧 Démarrage de l'optimisation automatique des paramètres"))

        # Évaluation de la performance actuelle
        current_metrics = self.calculate_performance_metrics()
        current_performance = current_metrics.get('pnl_percentage', 0)

        # Si performance insuffisante, tester de nouveaux paramètres
        if current_performance < self.performance_threshold:
            self.safe_log("info", f"📉 Performance actuelle: {current_performance:.2f}% - Optimisation nécessaire"))

            # Générer une nouvelle configuration
            new_config = self._generate_optimized_config()

            # Appliquer la nouvelle configuration
            if new_config:
                self._apply_config(new_config)
                self.safe_log("info", f"✅ Nouvelle configuration appliquée: {new_config}"))
                self.stats["optimization_count"] += 1

        else:
            self.safe_log("info", f"✅ Performance satisfaisante: {current_performance:.2f}%"))

        self.last_optimization = current_time

    def _generate_optimized_config(self) -> Optional[Dict]:
        """Génère une configuration optimisée basée sur l'historique"""
        ranges = self.optimization_engine["parameter_ranges"]

        # Analyse des meilleures configurations passées
        if self.optimization_engine["tested_configs"]:
            best_configs = sorted(
                self.optimization_engine["tested_configs"],
                key=lambda x: x['performance'],
                reverse=True
            )[:3]  # Top 3

            # Moyenne pondérée des meilleurs paramètres
            if best_configs:
                new_config = {}
                for param in ranges.keys():
                    values = [config['params'][param] for config in best_configs if param in config['params']]
                    if values:
                        new_config[param] = statistics.mean(values)
                    else:
                        # Valeur aléatoire dans la plage
                        min_val, max_val, step = ranges[param]
                        new_config[param] = min_val + (max_val - min_val) * np.random.random()

                return new_config

        # Configuration aléatoire si pas d'historique
        new_config = {}
        for param, (min_val, max_val, step) in ranges.items():
            new_config[param] = min_val + (max_val - min_val) * np.random.random()

        return new_config

    def _apply_config(self, config: Dict):
        """Applique une nouvelle configuration"""
        if 'grid_spacing' in config:
            self.grid_spacing = config['grid_spacing']
        if 'order_size' in config:
            self.order_size = config['order_size']
        if 'stop_loss_percent' in config:
            self.stop_loss_percent = config['stop_loss_percent']
        if 'take_profit_percent' in config:
            self.take_profit_percent = config['take_profit_percent']

        # Enregistrer la configuration testée
        self.optimization_engine["tested_configs"].append({
            'timestamp': time.time(),
            'params': config.copy(),
            'performance': 0  # Sera mis à jour plus tard
        })

    def _continuous_monitoring(self):
        """Monitoring continu en arrière-plan"""
        while self.monitoring_active:
            try:
                # Calcul des métriques toutes les 5 minutes
                metrics = self.calculate_performance_metrics()

                if metrics:
                    # Enregistrement des métriques
                    self.perf_logger.info(f"METRICS,{json.dumps(metrics)}")

                    # Mise à jour de la performance de la configuration actuelle
                    if self.optimization_engine["tested_configs"]:
                        self.optimization_engine["tested_configs"][-1]['performance'] = metrics.get('pnl_percentage', 0)

                    # Alerte si performance dégradée
                    if metrics.get('pnl_percentage', 0) < -5:  # -5% de perte
                        self.safe_log("warning", f"⚠️ ALERTE: Perte importante détectée: {metrics['pnl_percentage']:.2f}%"))

                time.sleep(300)  # 5 minutes

            except Exception as e:
                self.safe_log("error", f"❌ Erreur monitoring: {e}"))
                time.sleep(60)

    def print_advanced_status(self):
        """Affiche un statut avancé avec métriques détaillées"""
        current_price = self.get_current_price()
        btc_balance, usdt_balance = self.get_balances()
        metrics = self.calculate_performance_metrics()
        market_metrics = self.get_market_metrics()

        print("\n" + "="*80)
        print(f"🤖 OPTIMIZED SAFE BOT - {self.mode.upper()} - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*80)

        # Prix et balances
        if current_price and btc_balance is not None and usdt_balance is not None:
            btc_value = btc_balance * current_price
            total_value = btc_value + usdt_balance

            print(f"💰 Prix BTC: {current_price:,.2f} USDT")
            print(f"🏦 Balances: {btc_balance:.8f} BTC ({btc_value:.2f} USDT) + {usdt_balance:.2f} USDT")
            print(f"💎 Valeur totale: {total_value:.2f} USDT")

            if self.stats["start_balance"] > 0:
                pnl = total_value - self.stats["start_balance"]
                pnl_percent = pnl / self.stats["start_balance"] * 100
                print(f"📊 P&L: {pnl:+.2f} USDT ({pnl_percent:+.2f}%)")

        # Métriques de trading
        if metrics:
            print(f"\n📈 MÉTRIQUES DE PERFORMANCE:")
            print(f"   • Trades totaux: {metrics['total_trades']}")
            print(f"   • Trades rentables: {metrics['profitable_trades']}")
            print(f"   • Taux de réussite: {metrics['win_rate']:.1f}%")
            print(f"   • Facteur de profit: {metrics['profit_factor']:.2f}")
            print(f"   • Temps d'exécution moyen: {metrics['avg_execution_time']:.3f}s")
            print(f"   • Ratio de Sharpe: {metrics['sharpe_ratio']:.3f}")

        # Métriques de marché
        if market_metrics:
            print(f"\n🌍 CONDITIONS DE MARCHÉ:")
            print(f"   • Volume 24h: {market_metrics.get('volume_24h', 0):,.0f}")
            print(f"   • Variation 24h: {market_metrics.get('price_change_24h', 0):+.2f}%")
            print(f"   • Spread: {market_metrics.get('spread_percent', 0):.3f}%")
            print(f"   • Trades 24h: {market_metrics.get('trades_count_24h', 0):,}")

        # Configuration actuelle
        print(f"\n⚙️ CONFIGURATION ACTUELLE:")
        print(f"   • Taille de grille: {self.grid_size}")
        print(f"   • Espacement: {self.grid_spacing:.2f} USDT")
        print(f"   • Taille d'ordre: {self.order_size:.6f} BTC")
        print(f"   • Stop-loss: {self.stop_loss_percent}%")
        print(f"   • Take-profit: {self.take_profit_percent}%")
        print(f"   • Optimisations: {self.stats['optimization_count']}")

        # Ordres ouverts
        print(f"\n📋 Ordres ouverts: {len(self.open_orders)}")

        # Temps de fonctionnement
        uptime = datetime.now() - self.stats["start_time"]
        print(f"⏱️ Temps de fonctionnement: {uptime}")

        print("="*80)

    def save_strategy_config(self, filename: str = None):
        """Sauvegarde la configuration optimale pour réplication"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"optimal_config_{self.mode}_{timestamp}.json"

        config_data = {
            'mode': self.mode,
            'timestamp': datetime.now().isoformat(),
            'performance_metrics': self.calculate_performance_metrics(),
            'optimal_parameters': {
                'grid_size': self.grid_size,
                'grid_spacing': self.grid_spacing,
                'order_size': self.order_size,
                'stop_loss_percent': self.stop_loss_percent,
                'take_profit_percent': self.take_profit_percent,
                'min_volume_24h': self.min_volume_24h,
                'min_price_change': self.min_price_change,
                'max_spread': self.max_spread,
                'min_profit_threshold': self.min_profit_threshold
            },
            'market_conditions': self.get_market_metrics(),
            'optimization_history': self.optimization_engine["tested_configs"][-10:],  # 10 dernières
            'stats': self.stats
        }

        try:
            with open(filename, 'w') as f:
                json.dump(config_data, f, indent=2, default=str)

            self.safe_log("info", f"✅ Configuration sauvegardée: {filename}"))
            print(f"💾 Configuration optimale sauvegardée: {filename}")

            return filename

        except Exception as e:
            self.safe_log("error", f"❌ Erreur sauvegarde config: {e}"))
            return None

    def load_strategy_config(self, filename: str):
        """Charge une configuration optimale depuis un fichier"""
        try:
            with open(filename, 'r') as f:
                config_data = json.load(f)

            # Appliquer les paramètres optimaux
            params = config_data['optimal_parameters']
            self._apply_config(params)

            self.safe_log("info", f"✅ Configuration chargée: {filename}"))
            print(f"📂 Configuration optimale chargée: {filename}")

            return config_data

        except Exception as e:
            self.safe_log("error", f"❌ Erreur chargement config: {e}"))
            return None

    def run_optimization_test(self, duration_hours: int = None):
        """Lance un test d'optimisation sur une durée définie"""
        if duration_hours is None:
            duration_hours = int(os.getenv("TEST_DURATION_HOURS", 168))  # 1 semaine par défaut

        print(f"\n🧪 DÉMARRAGE DU TEST D'OPTIMISATION - {duration_hours}h")
        print(f"Mode: {self.mode.upper()}")
        print(f"Durée: {duration_hours} heures ({duration_hours/24:.1f} jours)")

        if self.mode == "production":
            print("\n⚠️ ATTENTION: TEST EN MODE PRODUCTION AVEC ARGENT RÉEL !")
            confirm = input("Confirmer le démarrage du test ? (oui/non): ").lower()
            if confirm not in ['oui', 'o', 'yes', 'y']:
                print("❌ Test annulé")
                return

        # Démarrage du monitoring
        self.monitor_thread.start()

        # Configuration initiale
        self.stats["start_time"] = datetime.now()
        end_time = self.stats["start_time"] + timedelta(hours=duration_hours)

        print(f"\n⏳ Test démarrera dans 3 secondes...")
        print(f"📅 Fin prévue: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        time.sleep(3)

        try:
            iteration = 0
            last_status_time = time.time()
            last_optimization_time = time.time()

            while datetime.now() < end_time:
                iteration += 1
                current_time = time.time()

                # Affichage du statut toutes les 10 minutes
                if current_time - last_status_time >= 600:  # 10 minutes
                    self.print_advanced_status()
                    last_status_time = current_time

                # Optimisation automatique toutes les heures
                if current_time - last_optimization_time >= 3600:  # 1 heure
                    self.optimize_parameters()
                    last_optimization_time = current_time

                # Exécution de la stratégie
                self.intelligent_trading_strategy()

                # Pause de 30 secondes
                time.sleep(30)

                # Vérification d'arrêt d'urgence
                if iteration % 120 == 0:  # Toutes les heures (120 * 30s)
                    metrics = self.calculate_performance_metrics()
                    if metrics.get('pnl_percentage', 0) < -10:  # Perte > 10%
                        print("\n🚨 ARRÊT D'URGENCE: Perte excessive détectée!")
                        break

        except KeyboardInterrupt:
            print("\n\n🛑 Arrêt demandé par l'utilisateur")

        finally:
            # Arrêt du monitoring
            self.monitoring_active = False

            # Rapport final
            self._generate_final_report(duration_hours)

    def _generate_final_report(self, planned_duration: int):
        """Génère un rapport final détaillé"""
        print("\n" + "="*100)
        print("📊 RAPPORT FINAL DU TEST D'OPTIMISATION")
        print("="*100)

        # Durée réelle
        actual_duration = datetime.now() - self.stats["start_time"]
        print(f"⏱️ Durée planifiée: {planned_duration}h")
        print(f"⏱️ Durée réelle: {actual_duration}")

        # Métriques finales
        final_metrics = self.calculate_performance_metrics()
        if final_metrics:
            print(f"\n📈 PERFORMANCE FINALE:")
            for key, value in final_metrics.items():
                if isinstance(value, float):
                    print(f"   • {key}: {value:.4f}")
                else:
                    print(f"   • {key}: {value}")

        # Sauvegarde de la configuration optimale
        config_file = self.save_strategy_config()

        # Recommandations
        print(f"\n💡 RECOMMANDATIONS:")
        if final_metrics.get('pnl_percentage', 0) > 0:
            print("   ✅ Stratégie rentable - Recommandée pour la production")
            print(f"   📁 Configuration sauvegardée: {config_file}")

            if self.mode == "testnet":
                print("   🚀 Prêt pour déploiement en production avec cette configuration")
        else:
            print("   ⚠️ Stratégie non rentable - Optimisation supplémentaire nécessaire")
            print("   🔧 Essayer avec des paramètres différents")

        # Logs
        print(f"\n📝 LOGS DÉTAILLÉS:")
        print(f"   • Trading: {self.log_prefix}.log")
        print(f"   • Performance: {self.log_prefix}_performance.log")

        print("="*100)

    def replicate_to_production(self, config_file: str):
        """Réplique une configuration testnet vers la production"""
        if self.mode != "production":
            print("❌ Cette fonction n'est disponible qu'en mode production")
            return

        print(f"\n🔄 RÉPLICATION TESTNET → PRODUCTION")
        print(f"📁 Fichier de configuration: {config_file}")

        # Chargement de la configuration
        config_data = self.load_strategy_config(config_file)
        if not config_data:
            print("❌ Impossible de charger la configuration")
            return

        # Vérification des performances
        test_performance = config_data.get('performance_metrics', {}).get('pnl_percentage', 0)
        if test_performance <= 0:
            print(f"⚠️ Configuration non rentable en test: {test_performance:.2f}%")
            confirm = input("Continuer quand même ? (oui/non): ").lower()
            if confirm not in ['oui', 'o', 'yes', 'y']:
                print("❌ Réplication annulée")
                return

        # Ajustements pour la production
        print(f"\n⚙️ Ajustements pour la production:")
        print(f"   • Capital réduit à 10% du testnet pour sécurité")
        print(f"   • Surveillance renforcée")
        print(f"   • Arrêts d'urgence plus stricts")

        # Confirmation finale
        print(f"\n⚠️ ATTENTION: TRADING AVEC ARGENT RÉEL!")
        print(f"💰 Capital maximum: {self.capital_max:.2f} USDT")
        confirm = input("Confirmer la réplication en production ? (oui/non): ").lower()

        if confirm in ['oui', 'o', 'yes', 'y']:
            print("✅ Configuration répliquée - Démarrage du trading en production")
            self.run_optimization_test(24)  # Test de 24h en production
        else:
            print("❌ Réplication annulée")

def main():
    """Fonction principale avec options avancées"""
    parser = argparse.ArgumentParser(description="Optimized Safe Bot - Trading optimisé avec IA")
    parser.add_argument("--mode", choices=["testnet", "production"], default="testnet",
                       help="Mode de trading (testnet ou production)")
    parser.add_argument("--duration", type=int, default=None,
                       help="Durée du test en heures (défaut: 168h = 1 semaine)")
    parser.add_argument("--config", type=str, default=None,
                       help="Fichier de configuration à charger")
    parser.add_argument("--replicate", type=str, default=None,
                       help="Répliquer une config testnet vers production")

    args = parser.parse_args()

    print("🤖 OPTIMIZED SAFE BOT - Version Avancée")
    print("="*50)

    # Initialisation du bot
    bot = OptimizedSafeBot(mode=args.mode)

    # Chargement d'une configuration si spécifiée
    if args.config:
        bot.load_strategy_config(args.config)

    # Réplication vers production
    if args.replicate and args.mode == "production":
        bot.replicate_to_production(args.replicate)
        return

    # Lancement du test d'optimisation
    bot.run_optimization_test(args.duration)

if __name__ == "__main__":
    main()
