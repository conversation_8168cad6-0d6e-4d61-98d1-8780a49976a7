"""
📊 Monitoring des erreurs en temps réel
Dashboard et alertes pour le suivi des erreurs système
"""

import time
import threading
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from collections import defaultdict, deque
from pathlib import Path
import sys
import json

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from utils.error_handler import error_handler, ErrorCategory, ErrorSeverity
from utils.network_handler import network_handler
from utils.trading_handler import trading_handler
from utils.notifications import notifier

class ErrorMonitor:
    """Moniteur d'erreurs en temps réel"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.is_monitoring = False
        self.monitor_thread = None
        
        # Historique des erreurs (dernières 24h)
        self.error_history = deque(maxlen=1000)
        
        # Statistiques par période
        self.hourly_stats = defaultdict(lambda: defaultdict(int))
        self.daily_stats = defaultdict(lambda: defaultdict(int))
        
        # Seuils d'alerte
        self.alert_thresholds = {
            ErrorCategory.NETWORK: {'hourly': 20, 'critical_count': 5},
            ErrorCategory.API: {'hourly': 15, 'critical_count': 3},
            ErrorCategory.TRADING: {'hourly': 10, 'critical_count': 2},
            ErrorCategory.DATA: {'hourly': 25, 'critical_count': 5},
            ErrorCategory.SYSTEM: {'hourly': 5, 'critical_count': 1}
        }
        
        # État des alertes (pour éviter le spam)
        self.alert_cooldowns = {}
        self.alert_cooldown_duration = timedelta(minutes=15)
        
        # Métriques de santé système
        self.health_metrics = {
            'uptime_start': datetime.now(),
            'total_errors': 0,
            'critical_errors': 0,
            'last_error': None,
            'error_rate_per_hour': 0,
            'system_health_score': 100
        }
        
        self.logger.info("📊 Moniteur d'erreurs initialisé")
    
    def start_monitoring(self, interval: int = 30):
        """Démarre le monitoring en arrière-plan"""
        if self.is_monitoring:
            self.logger.warning("⚠️ Monitoring déjà en cours")
            return
        
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitoring_loop,
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
        
        self.logger.info(f"🚀 Monitoring démarré (intervalle: {interval}s)")
    
    def stop_monitoring(self):
        """Arrête le monitoring"""
        if not self.is_monitoring:
            self.logger.warning("⚠️ Aucun monitoring en cours")
            return
        
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        
        self.logger.info("🛑 Monitoring arrêté")
    
    def _monitoring_loop(self, interval: int):
        """Boucle principale de monitoring"""
        while self.is_monitoring:
            try:
                # Collecter les statistiques
                self._collect_error_stats()
                
                # Vérifier les seuils d'alerte
                self._check_alert_thresholds()
                
                # Mettre à jour les métriques de santé
                self._update_health_metrics()
                
                # Nettoyer les anciennes données
                self._cleanup_old_data()
                
                time.sleep(interval)
                
            except Exception as e:
                self.logger.error(f"❌ Erreur dans la boucle de monitoring: {e}")
                time.sleep(interval)
    
    def _collect_error_stats(self):
        """Collecte les statistiques d'erreurs"""
        # Récupérer les stats du gestionnaire d'erreurs
        error_stats = error_handler.get_error_stats()
        
        # Récupérer les stats réseau
        network_status = network_handler.get_network_status()
        
        # Récupérer les stats de trading
        trading_stats = trading_handler.get_trading_stats()
        
        # Mettre à jour l'historique
        current_time = datetime.now()
        hour_key = current_time.strftime('%Y-%m-%d %H')
        day_key = current_time.strftime('%Y-%m-%d')
        
        # Compter les erreurs par catégorie
        for error_key, count in error_stats['error_counts'].items():
            category = error_key.split('_')[0]
            self.hourly_stats[hour_key][category] += count
            self.daily_stats[day_key][category] += count
        
        # Ajouter les métriques spéciales
        if trading_stats['emergency_stop']:
            self.hourly_stats[hour_key]['EMERGENCY_STOP'] = 1
        
        if not network_status['connectivity']:
            self.hourly_stats[hour_key]['NETWORK_DOWN'] = 1
    
    def _check_alert_thresholds(self):
        """Vérifie les seuils d'alerte"""
        current_hour = datetime.now().strftime('%Y-%m-%d %H')
        
        for category, thresholds in self.alert_thresholds.items():
            category_key = category.value
            hourly_count = self.hourly_stats[current_hour].get(category_key, 0)
            
            # Vérifier le seuil horaire
            if hourly_count >= thresholds['hourly']:
                self._send_threshold_alert(category, 'hourly', hourly_count, thresholds['hourly'])
            
            # Vérifier les erreurs critiques
            critical_count = self._count_recent_critical_errors(category)
            if critical_count >= thresholds['critical_count']:
                self._send_threshold_alert(category, 'critical', critical_count, thresholds['critical_count'])
    
    def _count_recent_critical_errors(self, category: ErrorCategory) -> int:
        """Compte les erreurs critiques récentes pour une catégorie"""
        recent_time = datetime.now() - timedelta(minutes=30)
        
        count = 0
        for error_info in reversed(self.error_history):
            if error_info['timestamp'] < recent_time:
                break
            if (error_info['category'] == category.value and 
                error_info['severity'] == ErrorSeverity.CRITICAL.value):
                count += 1
        
        return count
    
    def _send_threshold_alert(self, category: ErrorCategory, alert_type: str, 
                            current_count: int, threshold: int):
        """Envoie une alerte de seuil dépassé"""
        alert_key = f"{category.value}_{alert_type}"
        
        # Vérifier le cooldown
        if alert_key in self.alert_cooldowns:
            if datetime.now() - self.alert_cooldowns[alert_key] < self.alert_cooldown_duration:
                return  # Encore en cooldown
        
        # Enregistrer l'alerte
        self.alert_cooldowns[alert_key] = datetime.now()
        
        # Préparer le message
        emoji_map = {
            'hourly': '⏰',
            'critical': '🚨'
        }
        
        emoji = emoji_map.get(alert_type, '⚠️')
        
        message = (
            f"{emoji} <b>SEUIL D'ALERTE DÉPASSÉ</b>\n"
            f"📂 Catégorie: {category.value}\n"
            f"📊 Type: {alert_type}\n"
            f"🔢 Erreurs: {current_count}/{threshold}\n"
            f"🕒 Heure: {datetime.now().strftime('%H:%M:%S')}"
        )
        
        # Ajouter des recommandations
        if category == ErrorCategory.NETWORK:
            message += "\n💡 Vérifiez la connectivité réseau"
        elif category == ErrorCategory.TRADING:
            message += "\n💡 Vérifiez les paramètres de trading"
        elif category == ErrorCategory.API:
            message += "\n💡 Vérifiez les clés API et rate limits"
        
        notifier.send_telegram(message)
        self.logger.warning(f"🚨 Alerte envoyée: {alert_key}")
    
    def _update_health_metrics(self):
        """Met à jour les métriques de santé système"""
        current_time = datetime.now()
        
        # Calculer l'uptime
        uptime = current_time - self.health_metrics['uptime_start']
        
        # Compter les erreurs récentes (dernière heure)
        recent_errors = self._count_recent_errors(timedelta(hours=1))
        self.health_metrics['error_rate_per_hour'] = recent_errors
        
        # Calculer le score de santé (0-100)
        health_score = 100
        
        # Pénalités pour les erreurs
        if recent_errors > 0:
            health_score -= min(recent_errors * 2, 30)  # Max -30 points
        
        # Pénalités pour les circuit breakers actifs
        active_breakers = len(error_handler.circuit_breakers)
        health_score -= active_breakers * 20  # -20 points par breaker
        
        # Pénalités pour l'arrêt d'urgence
        if trading_handler.emergency_stop:
            health_score -= 50
        
        # Pénalités pour les problèmes réseau
        network_status = network_handler.get_network_status()
        connectivity_pct = network_status.get('connectivity_percentage', 100)
        if connectivity_pct < 100:
            health_score -= (100 - connectivity_pct) / 2
        
        self.health_metrics['system_health_score'] = max(0, health_score)
        self.health_metrics['total_errors'] = sum(error_handler.error_counts.values())
        
        # Mettre à jour la dernière erreur
        if error_handler.last_errors:
            latest_error = max(
                error_handler.last_errors.values(),
                key=lambda x: x['timestamp']
            )
            self.health_metrics['last_error'] = latest_error
    
    def _count_recent_errors(self, time_window: timedelta) -> int:
        """Compte les erreurs dans une fenêtre de temps"""
        cutoff_time = datetime.now() - time_window
        
        count = 0
        for error_info in reversed(self.error_history):
            if error_info['timestamp'] < cutoff_time:
                break
            count += 1
        
        return count
    
    def _cleanup_old_data(self):
        """Nettoie les anciennes données"""
        # Nettoyer les stats horaires (garder 48h)
        cutoff_time = datetime.now() - timedelta(hours=48)
        cutoff_hour = cutoff_time.strftime('%Y-%m-%d %H')
        
        old_hours = [h for h in self.hourly_stats.keys() if h < cutoff_hour]
        for hour in old_hours:
            del self.hourly_stats[hour]
        
        # Nettoyer les stats quotidiennes (garder 30 jours)
        cutoff_date = datetime.now() - timedelta(days=30)
        cutoff_day = cutoff_date.strftime('%Y-%m-%d')
        
        old_days = [d for d in self.daily_stats.keys() if d < cutoff_day]
        for day in old_days:
            del self.daily_stats[day]
        
        # Nettoyer les cooldowns expirés
        expired_cooldowns = [
            key for key, timestamp in self.alert_cooldowns.items()
            if datetime.now() - timestamp > self.alert_cooldown_duration
        ]
        for key in expired_cooldowns:
            del self.alert_cooldowns[key]
    
    def add_error_to_history(self, error_info: Dict):
        """Ajoute une erreur à l'historique"""
        self.error_history.append(error_info)
        
        # Mettre à jour les compteurs
        if error_info['severity'] == ErrorSeverity.CRITICAL.value:
            self.health_metrics['critical_errors'] += 1
    
    def get_monitoring_dashboard(self) -> Dict[str, Any]:
        """Récupère les données pour le dashboard de monitoring"""
        current_time = datetime.now()
        
        # Stats des dernières 24h
        last_24h_errors = self._get_errors_in_timeframe(timedelta(hours=24))
        
        # Stats par catégorie
        category_stats = defaultdict(int)
        severity_stats = defaultdict(int)
        
        for error in last_24h_errors:
            category_stats[error['category']] += 1
            severity_stats[error['severity']] += 1
        
        # Tendances horaires (dernières 12h)
        hourly_trends = {}
        for i in range(12):
            hour_time = current_time - timedelta(hours=i)
            hour_key = hour_time.strftime('%Y-%m-%d %H')
            hourly_trends[hour_key] = dict(self.hourly_stats.get(hour_key, {}))
        
        return {
            'timestamp': current_time.isoformat(),
            'health_metrics': self.health_metrics.copy(),
            'error_stats': {
                'last_24h_total': len(last_24h_errors),
                'by_category': dict(category_stats),
                'by_severity': dict(severity_stats)
            },
            'hourly_trends': hourly_trends,
            'active_alerts': len(self.alert_cooldowns),
            'circuit_breakers': {k.value: v for k, v in error_handler.circuit_breakers.items()},
            'system_status': {
                'emergency_stop': trading_handler.emergency_stop,
                'monitoring_active': self.is_monitoring,
                'network_connectivity': network_handler.get_network_status()['connectivity_percentage']
            },
            'recent_errors': [
                {
                    'timestamp': error['timestamp'].isoformat(),
                    'category': error['category'],
                    'severity': error['severity'],
                    'error_type': error['error_type'],
                    'message': error['error_message'][:100]
                }
                for error in list(self.error_history)[-10:]  # 10 dernières erreurs
            ]
        }
    
    def _get_errors_in_timeframe(self, timeframe: timedelta) -> List[Dict]:
        """Récupère les erreurs dans une fenêtre de temps"""
        cutoff_time = datetime.now() - timeframe
        
        return [
            error for error in self.error_history
            if error['timestamp'] >= cutoff_time
        ]
    
    def generate_error_report(self, timeframe_hours: int = 24) -> str:
        """Génère un rapport d'erreurs"""
        timeframe = timedelta(hours=timeframe_hours)
        errors = self._get_errors_in_timeframe(timeframe)
        
        if not errors:
            return f"✅ Aucune erreur dans les dernières {timeframe_hours}h"
        
        # Analyser les erreurs
        category_counts = defaultdict(int)
        severity_counts = defaultdict(int)
        error_types = defaultdict(int)
        
        for error in errors:
            category_counts[error['category']] += 1
            severity_counts[error['severity']] += 1
            error_types[error['error_type']] += 1
        
        # Générer le rapport
        report = f"📊 **RAPPORT D'ERREURS - {timeframe_hours}h**\n\n"
        report += f"🔢 Total: {len(errors)} erreurs\n\n"
        
        report += "📂 **Par catégorie:**\n"
        for category, count in sorted(category_counts.items()):
            report += f"  • {category}: {count}\n"
        
        report += "\n⚠️ **Par sévérité:**\n"
        for severity, count in sorted(severity_counts.items()):
            report += f"  • {severity}: {count}\n"
        
        report += "\n🔧 **Types les plus fréquents:**\n"
        top_types = sorted(error_types.items(), key=lambda x: x[1], reverse=True)[:5]
        for error_type, count in top_types:
            report += f"  • {error_type}: {count}\n"
        
        # Score de santé
        health_score = self.health_metrics['system_health_score']
        if health_score >= 90:
            health_emoji = "🟢"
        elif health_score >= 70:
            health_emoji = "🟡"
        else:
            health_emoji = "🔴"
        
        report += f"\n{health_emoji} **Score de santé:** {health_score:.1f}/100"
        
        return report

# Instance globale
error_monitor = ErrorMonitor()
