"""
⚡ Moteur de scalping DEX haute fréquence
Stratégie de scalping optimisée pour Uniswap v3 avec gestion des frais
"""

import asyncio
import time
import numpy as np
from decimal import Decimal, ROUND_DOWN
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import deque
import logging
from pathlib import Path
import sys

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent.parent))

from bots.dex_scalping.uniswap_v3_connector import UniswapV3Connector, PoolInfo, QuoteResult
from utils.error_handler import error_handler, ErrorCategory, ErrorSeverity
from logging_system.central_logger import central_logger, LogCategory
from risk_management.portfolio_manager import portfolio_manager, PositionType

@dataclass
class ScalpingConfig:
    """Configuration du bot de scalping"""
    # Paire de trading
    token_in: str
    token_out: str
    pool_fee: int = 3000  # 0.3%
    
    # Paramètres de scalping
    min_profit_threshold: float = 0.001  # 0.1% minimum
    max_position_size: float = 0.1  # 10% du capital max
    max_slippage: float = 0.005  # 0.5% slippage max
    
    # Gestion des frais
    max_gas_price_gwei: int = 100
    min_profit_after_fees: float = 0.0005  # 0.05% après frais
    
    # Timing
    check_interval: float = 0.5  # 500ms entre les vérifications
    max_hold_time: int = 300  # 5 minutes max par position
    
    # Seuils de volatilité
    min_volatility: float = 0.002  # 0.2% volatilité minimum
    max_volatility: float = 0.05   # 5% volatilité maximum
    
    # Liquidité
    min_liquidity: int = 1000000  # $1M minimum de liquidité

@dataclass
class MarketData:
    """Données de marché en temps réel"""
    timestamp: datetime
    price: float
    liquidity: int
    volume_24h: float
    volatility: float
    bid_ask_spread: float
    gas_price: int

@dataclass
class ScalpingOpportunity:
    """Opportunité de scalping détectée"""
    timestamp: datetime
    direction: str  # 'buy' ou 'sell'
    entry_price: float
    target_price: float
    stop_loss: float
    expected_profit: float
    confidence: float
    amount_in: int
    amount_out_min: int
    gas_estimate: int
    profit_after_fees: float

class ScalpingEngine:
    """Moteur de scalping DEX haute fréquence"""
    
    def __init__(self, connector: UniswapV3Connector, config: ScalpingConfig):
        self.logger = logging.getLogger(__name__)
        self.connector = connector
        self.config = config
        
        # État du moteur
        self.is_running = False
        self.current_position = None
        self.position_start_time = None
        
        # Données de marché
        self.price_history: deque = deque(maxlen=1000)
        self.volume_history: deque = deque(maxlen=100)
        self.gas_price_history: deque = deque(maxlen=50)
        
        # Métriques de performance
        self.total_trades = 0
        self.successful_trades = 0
        self.total_profit = 0.0
        self.total_fees_paid = 0.0
        
        # Cache des pools
        self.pool_info: Optional[PoolInfo] = None
        self.last_pool_update = 0
        
        # Détection d'opportunités
        self.opportunity_detector = OpportunityDetector(config)
        
        central_logger.log(
            level="INFO",
            message="Moteur de scalping DEX initialisé",
            category=LogCategory.STRATEGY,
            token_pair=f"{config.token_in}/{config.token_out}",
            pool_fee=config.pool_fee
        )
    
    async def start(self):
        """Démarre le moteur de scalping"""
        if self.is_running:
            self.logger.warning("Le moteur est déjà en cours d'exécution")
            return
        
        self.is_running = True
        
        central_logger.strategy_action(
            message="Démarrage du bot de scalping DEX",
            strategy_name="DEX_Scalping",
            action="start",
            symbol=f"{self.config.token_in}/{self.config.token_out}"
        )
        
        try:
            # Initialiser les données de marché
            await self._initialize_market_data()
            
            # Boucle principale de scalping
            while self.is_running:
                await self._scalping_cycle()
                await asyncio.sleep(self.config.check_interval)
                
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.HIGH, {
                'function': 'start',
                'strategy': 'dex_scalping'
            })
        finally:
            await self.stop()
    
    async def stop(self):
        """Arrête le moteur de scalping"""
        self.is_running = False
        
        # Fermer les positions ouvertes
        if self.current_position:
            await self._close_position("strategy_stop")
        
        central_logger.strategy_action(
            message="Arrêt du bot de scalping DEX",
            strategy_name="DEX_Scalping",
            action="stop",
            total_trades=self.total_trades,
            success_rate=self.successful_trades / self.total_trades * 100 if self.total_trades > 0 else 0,
            total_profit=self.total_profit
        )
    
    async def _initialize_market_data(self):
        """Initialise les données de marché"""
        try:
            # Récupérer les informations de la pool
            self.pool_info = await self.connector.get_pool_info(
                self.config.token_in,
                self.config.token_out,
                self.config.pool_fee
            )
            
            if not self.pool_info:
                raise Exception("Impossible de récupérer les informations de la pool")
            
            # Vérifier la liquidité minimum
            if self.pool_info.liquidity < self.config.min_liquidity:
                raise Exception(f"Liquidité insuffisante: {self.pool_info.liquidity}")
            
            # Initialiser l'historique des prix
            current_price = self._sqrt_price_to_price(self.pool_info.sqrt_price_x96)
            for _ in range(10):
                self.price_history.append({
                    'timestamp': datetime.now(),
                    'price': current_price,
                    'liquidity': self.pool_info.liquidity
                })
            
            self.logger.info(f"Données de marché initialisées - Prix: {current_price:.6f}")
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.DATA, ErrorSeverity.HIGH, {
                'function': '_initialize_market_data'
            })
            raise
    
    async def _scalping_cycle(self):
        """Cycle principal de scalping"""
        try:
            # Mettre à jour les données de marché
            market_data = await self._update_market_data()
            
            if not market_data:
                return
            
            # Vérifier les conditions de marché
            if not self._check_market_conditions(market_data):
                return
            
            # Gérer les positions existantes
            if self.current_position:
                await self._manage_existing_position(market_data)
            else:
                # Chercher de nouvelles opportunités
                opportunity = await self._find_scalping_opportunity(market_data)
                if opportunity:
                    await self._execute_opportunity(opportunity)
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.MEDIUM, {
                'function': '_scalping_cycle'
            })
    
    async def _update_market_data(self) -> Optional[MarketData]:
        """Met à jour les données de marché"""
        try:
            # Mettre à jour les informations de la pool si nécessaire
            current_time = time.time()
            if current_time - self.last_pool_update > 10:  # Toutes les 10 secondes
                self.pool_info = await self.connector.get_pool_info(
                    self.config.token_in,
                    self.config.token_out,
                    self.config.pool_fee
                )
                self.last_pool_update = current_time
            
            if not self.pool_info:
                return None
            
            # Calculer le prix actuel
            current_price = self._sqrt_price_to_price(self.pool_info.sqrt_price_x96)
            
            # Ajouter à l'historique
            price_data = {
                'timestamp': datetime.now(),
                'price': current_price,
                'liquidity': self.pool_info.liquidity
            }
            self.price_history.append(price_data)
            
            # Calculer la volatilité
            volatility = self._calculate_volatility()
            
            # Calculer le spread bid-ask (estimation)
            bid_ask_spread = self._estimate_bid_ask_spread()
            
            # Prix du gas
            gas_price = self.connector.calculate_optimal_gas_price()
            self.gas_price_history.append(gas_price)
            
            return MarketData(
                timestamp=datetime.now(),
                price=current_price,
                liquidity=self.pool_info.liquidity,
                volume_24h=0,  # Simplification
                volatility=volatility,
                bid_ask_spread=bid_ask_spread,
                gas_price=gas_price
            )
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.DATA, ErrorSeverity.MEDIUM, {
                'function': '_update_market_data'
            })
            return None
    
    def _sqrt_price_to_price(self, sqrt_price_x96: int) -> float:
        """Convertit sqrtPriceX96 en prix décimal"""
        # sqrtPriceX96 = sqrt(price) * 2^96
        sqrt_price = sqrt_price_x96 / (2 ** 96)
        price = sqrt_price ** 2
        
        # Ajuster selon les décimales des tokens (WETH=18, WBTC=8)
        # Simplification: on assume que c'est déjà ajusté
        return price
    
    def _calculate_volatility(self, window: int = 20) -> float:
        """Calcule la volatilité sur une fenêtre donnée"""
        if len(self.price_history) < window:
            return 0.0
        
        prices = [p['price'] for p in list(self.price_history)[-window:]]
        returns = []
        
        for i in range(1, len(prices)):
            ret = (prices[i] - prices[i-1]) / prices[i-1]
            returns.append(ret)
        
        if not returns:
            return 0.0
        
        return np.std(returns)
    
    def _estimate_bid_ask_spread(self) -> float:
        """Estime le spread bid-ask"""
        # Simplification: basé sur les frais de la pool et la liquidité
        base_spread = self.config.pool_fee / 1000000  # Convertir en décimal
        
        # Ajuster selon la liquidité
        if self.pool_info and self.pool_info.liquidity > 0:
            liquidity_factor = max(0.5, min(2.0, 10000000 / self.pool_info.liquidity))
            return base_spread * liquidity_factor
        
        return base_spread
    
    def _check_market_conditions(self, market_data: MarketData) -> bool:
        """Vérifie si les conditions de marché sont favorables"""
        # Vérifier la volatilité
        if market_data.volatility < self.config.min_volatility:
            return False
        
        if market_data.volatility > self.config.max_volatility:
            return False
        
        # Vérifier le prix du gas
        gas_price_gwei = market_data.gas_price / 10**9
        if gas_price_gwei > self.config.max_gas_price_gwei:
            return False
        
        # Vérifier la liquidité
        if market_data.liquidity < self.config.min_liquidity:
            return False
        
        return True
    
    async def _find_scalping_opportunity(self, market_data: MarketData) -> Optional[ScalpingOpportunity]:
        """Cherche des opportunités de scalping"""
        try:
            # Utiliser le détecteur d'opportunités
            opportunity = await self.opportunity_detector.detect_opportunity(
                market_data, self.price_history, self.connector
            )
            
            if opportunity:
                # Vérifier la rentabilité après frais
                if opportunity.profit_after_fees >= self.config.min_profit_after_fees:
                    central_logger.log(
                        level="INFO",
                        message=f"Opportunité détectée: {opportunity.direction}",
                        category=LogCategory.STRATEGY,
                        direction=opportunity.direction,
                        expected_profit=opportunity.expected_profit,
                        confidence=opportunity.confidence
                    )
                    return opportunity
            
            return None
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.MEDIUM, {
                'function': '_find_scalping_opportunity'
            })
            return None
    
    async def _execute_opportunity(self, opportunity: ScalpingOpportunity):
        """Exécute une opportunité de scalping"""
        try:
            # Vérifier les limites de risque
            if not self._check_risk_limits(opportunity):
                return
            
            # Exécuter le swap
            tx_hash = await self.connector.execute_swap(
                token_in=self.config.token_in if opportunity.direction == 'buy' else self.config.token_out,
                token_out=self.config.token_out if opportunity.direction == 'buy' else self.config.token_in,
                amount_in=opportunity.amount_in,
                amount_out_minimum=opportunity.amount_out_min,
                fee=self.config.pool_fee,
                slippage_tolerance=self.config.max_slippage
            )
            
            if tx_hash:
                # Enregistrer la position
                self.current_position = {
                    'opportunity': opportunity,
                    'tx_hash': tx_hash,
                    'entry_time': datetime.now(),
                    'status': 'pending'
                }
                self.position_start_time = time.time()
                
                # Attendre la confirmation
                receipt = await self.connector.wait_for_transaction(tx_hash, timeout=60)
                
                if receipt and receipt['success']:
                    self.current_position['status'] = 'open'
                    self.total_trades += 1
                    
                    central_logger.position_opened(
                        message=f"Position de scalping ouverte: {opportunity.direction}",
                        symbol=f"{self.config.token_in}/{self.config.token_out}",
                        position_type=opportunity.direction.upper(),
                        quantity=opportunity.amount_in,
                        entry_price=opportunity.entry_price,
                        tx_hash=tx_hash
                    )
                else:
                    # Transaction échouée
                    self.current_position = None
                    central_logger.log(
                        level="ERROR",
                        message="Échec de l'exécution de l'opportunité",
                        category=LogCategory.TRADING,
                        tx_hash=tx_hash
                    )
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.HIGH, {
                'function': '_execute_opportunity',
                'direction': opportunity.direction
            })
    
    def _check_risk_limits(self, opportunity: ScalpingOpportunity) -> bool:
        """Vérifie les limites de risque"""
        # Vérifier la taille de position
        position_value = opportunity.amount_in * opportunity.entry_price
        max_position_value = portfolio_manager.get_portfolio_value() * self.config.max_position_size
        
        if position_value > max_position_value:
            return False
        
        # Vérifier le profit minimum
        if opportunity.profit_after_fees < self.config.min_profit_after_fees:
            return False
        
        return True
    
    async def _manage_existing_position(self, market_data: MarketData):
        """Gère les positions existantes"""
        if not self.current_position:
            return
        
        opportunity = self.current_position['opportunity']
        current_time = time.time()
        
        # Vérifier le timeout
        if current_time - self.position_start_time > self.config.max_hold_time:
            await self._close_position("timeout")
            return
        
        # Vérifier les conditions de sortie
        current_price = market_data.price
        
        # Take profit
        if opportunity.direction == 'buy' and current_price >= opportunity.target_price:
            await self._close_position("take_profit")
        elif opportunity.direction == 'sell' and current_price <= opportunity.target_price:
            await self._close_position("take_profit")
        
        # Stop loss
        elif opportunity.direction == 'buy' and current_price <= opportunity.stop_loss:
            await self._close_position("stop_loss")
        elif opportunity.direction == 'sell' and current_price >= opportunity.stop_loss:
            await self._close_position("stop_loss")
    
    async def _close_position(self, reason: str):
        """Ferme la position actuelle"""
        if not self.current_position:
            return
        
        try:
            opportunity = self.current_position['opportunity']
            
            # Exécuter le swap de fermeture (direction opposée)
            close_direction = 'sell' if opportunity.direction == 'buy' else 'buy'
            
            # Calculer les montants pour la fermeture
            # Simplification: on utilise les montants de l'opportunité initiale
            amount_in = opportunity.amount_out_min
            
            # Obtenir une cotation pour la fermeture
            quote = await self.connector.get_quote(
                token_in=self.config.token_out if close_direction == 'sell' else self.config.token_in,
                token_out=self.config.token_in if close_direction == 'sell' else self.config.token_out,
                amount_in=amount_in,
                fee=self.config.pool_fee
            )
            
            if quote:
                tx_hash = await self.connector.execute_swap(
                    token_in=self.config.token_out if close_direction == 'sell' else self.config.token_in,
                    token_out=self.config.token_in if close_direction == 'sell' else self.config.token_out,
                    amount_in=amount_in,
                    amount_out_minimum=int(quote.amount_out * 0.995),  # 0.5% slippage
                    fee=self.config.pool_fee
                )
                
                if tx_hash:
                    # Calculer le P&L
                    pnl = self._calculate_pnl(opportunity, quote.amount_out)
                    
                    if pnl > 0:
                        self.successful_trades += 1
                    
                    self.total_profit += pnl
                    
                    central_logger.position_closed(
                        message=f"Position de scalping fermée: {reason}",
                        symbol=f"{self.config.token_in}/{self.config.token_out}",
                        quantity=amount_in,
                        exit_price=quote.amount_out / amount_in if amount_in > 0 else 0,
                        pnl=pnl,
                        reason=reason,
                        tx_hash=tx_hash
                    )
            
            # Nettoyer la position
            self.current_position = None
            self.position_start_time = None
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.HIGH, {
                'function': '_close_position',
                'reason': reason
            })
    
    def _calculate_pnl(self, opportunity: ScalpingOpportunity, amount_out: int) -> float:
        """Calcule le P&L d'une position"""
        # Simplification du calcul P&L
        initial_value = opportunity.amount_in * opportunity.entry_price
        final_value = amount_out * opportunity.entry_price  # Approximation
        
        return final_value - initial_value
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Retourne les statistiques de performance"""
        win_rate = self.successful_trades / self.total_trades * 100 if self.total_trades > 0 else 0
        
        return {
            'total_trades': self.total_trades,
            'successful_trades': self.successful_trades,
            'win_rate': win_rate,
            'total_profit': self.total_profit,
            'total_fees_paid': self.total_fees_paid,
            'average_profit_per_trade': self.total_profit / self.total_trades if self.total_trades > 0 else 0,
            'current_position': self.current_position is not None,
            'is_running': self.is_running
        }

class OpportunityDetector:
    """Détecteur d'opportunités de scalping"""
    
    def __init__(self, config: ScalpingConfig):
        self.config = config
    
    async def detect_opportunity(self, market_data: MarketData, 
                               price_history: deque,
                               connector: UniswapV3Connector) -> Optional[ScalpingOpportunity]:
        """Détecte les opportunités de scalping"""
        try:
            # Analyser la tendance des prix
            trend = self._analyze_price_trend(price_history)
            
            # Détecter les signaux de retournement
            reversal_signal = self._detect_reversal_signals(price_history, market_data)
            
            if reversal_signal:
                direction = reversal_signal['direction']
                confidence = reversal_signal['confidence']
                
                # Calculer les prix cibles
                entry_price = market_data.price
                target_price, stop_loss = self._calculate_targets(entry_price, direction, market_data)
                
                # Calculer les montants
                amount_in = self._calculate_position_size(market_data)
                
                # Obtenir une cotation
                quote = await connector.get_quote(
                    token_in=self.config.token_in if direction == 'buy' else self.config.token_out,
                    token_out=self.config.token_out if direction == 'buy' else self.config.token_in,
                    amount_in=amount_in,
                    fee=self.config.pool_fee
                )
                
                if quote:
                    # Calculer le profit attendu
                    expected_profit = self._calculate_expected_profit(
                        entry_price, target_price, amount_in, quote.gas_estimate
                    )
                    
                    # Calculer le profit après frais
                    profit_after_fees = expected_profit - self._estimate_total_fees(
                        amount_in, quote.gas_estimate, market_data.gas_price
                    )
                    
                    return ScalpingOpportunity(
                        timestamp=datetime.now(),
                        direction=direction,
                        entry_price=entry_price,
                        target_price=target_price,
                        stop_loss=stop_loss,
                        expected_profit=expected_profit,
                        confidence=confidence,
                        amount_in=amount_in,
                        amount_out_min=int(quote.amount_out * 0.995),
                        gas_estimate=quote.gas_estimate,
                        profit_after_fees=profit_after_fees
                    )
            
            return None
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.MEDIUM, {
                'function': 'detect_opportunity'
            })
            return None
    
    def _analyze_price_trend(self, price_history: deque) -> str:
        """Analyse la tendance des prix"""
        if len(price_history) < 10:
            return 'neutral'
        
        recent_prices = [p['price'] for p in list(price_history)[-10:]]
        
        # Calculer la pente de la tendance
        x = np.arange(len(recent_prices))
        slope = np.polyfit(x, recent_prices, 1)[0]
        
        if slope > 0.001:
            return 'uptrend'
        elif slope < -0.001:
            return 'downtrend'
        else:
            return 'neutral'
    
    def _detect_reversal_signals(self, price_history: deque, 
                               market_data: MarketData) -> Optional[Dict]:
        """Détecte les signaux de retournement"""
        if len(price_history) < 20:
            return None
        
        prices = [p['price'] for p in list(price_history)[-20:]]
        current_price = market_data.price
        
        # Calculer des moyennes mobiles simples
        sma_5 = np.mean(prices[-5:])
        sma_10 = np.mean(prices[-10:])
        sma_20 = np.mean(prices[-20:])
        
        # Signal de retournement haussier
        if (current_price > sma_5 > sma_10 and 
            current_price < prices[-2] and  # Prix en baisse récente
            market_data.volatility > self.config.min_volatility):
            
            return {
                'direction': 'buy',
                'confidence': min(0.9, market_data.volatility * 10)
            }
        
        # Signal de retournement baissier
        elif (current_price < sma_5 < sma_10 and 
              current_price > prices[-2] and  # Prix en hausse récente
              market_data.volatility > self.config.min_volatility):
            
            return {
                'direction': 'sell',
                'confidence': min(0.9, market_data.volatility * 10)
            }
        
        return None
    
    def _calculate_targets(self, entry_price: float, direction: str, 
                         market_data: MarketData) -> Tuple[float, float]:
        """Calcule les prix cibles et stop-loss"""
        volatility_factor = max(0.001, market_data.volatility)
        
        if direction == 'buy':
            target_price = entry_price * (1 + volatility_factor * 2)
            stop_loss = entry_price * (1 - volatility_factor)
        else:
            target_price = entry_price * (1 - volatility_factor * 2)
            stop_loss = entry_price * (1 + volatility_factor)
        
        return target_price, stop_loss
    
    def _calculate_position_size(self, market_data: MarketData) -> int:
        """Calcule la taille de position optimale"""
        # Simplification: taille fixe basée sur la liquidité
        base_amount = min(1000000, market_data.liquidity // 1000)  # 0.1% de la liquidité
        
        # Ajuster selon la volatilité
        volatility_factor = max(0.5, min(2.0, 1 / market_data.volatility))
        
        return int(base_amount * volatility_factor)
    
    def _calculate_expected_profit(self, entry_price: float, target_price: float,
                                 amount_in: int, gas_estimate: int) -> float:
        """Calcule le profit attendu"""
        price_diff = abs(target_price - entry_price)
        return (price_diff / entry_price) * amount_in
    
    def _estimate_total_fees(self, amount_in: int, gas_estimate: int, 
                           gas_price: int) -> float:
        """Estime les frais totaux"""
        # Frais de gas
        gas_fee = gas_estimate * gas_price / 10**18  # Convertir en ETH
        
        # Frais de la pool (0.3% par défaut)
        pool_fee = amount_in * (self.config.pool_fee / 1000000)
        
        return gas_fee + pool_fee
