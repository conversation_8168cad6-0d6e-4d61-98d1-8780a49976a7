"""
🔧 Configuration centralisée pour botCrypto
Gestion sécurisée des variables d'environnement et paramètres
"""

import os
from pathlib import Path
from dotenv import load_dotenv
from typing import Optional, Dict, Any

class BotConfig:
    """Configuration centralisée pour tous les bots"""
    
    def __init__(self, env_file: str = ".env.local"):
        """
        Initialise la configuration
        
        Args:
            env_file: Nom du fichier d'environnement à charger
        """
        self.env_file = env_file
        self._load_environment()
        self._validate_required_vars()
    
    def _load_environment(self):
        """Charge les variables d'environnement"""
        # Chercher le fichier .env.local dans le répertoire racine du projet
        project_root = Path(__file__).parent.parent
        env_path = project_root / self.env_file
        
        if env_path.exists():
            load_dotenv(env_path)
            print(f"✅ Configuration chargée depuis {env_path}")
        else:
            print(f"⚠️  Fichier {env_path} non trouvé")
    
    def _validate_required_vars(self):
        """Valide que les variables critiques sont présentes"""
        required_vars = [
            "PRIVATE_KEY", "WALLET_ADDRESS", "BSC_SCAN_API_KEY",
            "TELEGRAM_BOT_TOKEN", "TELEGRAM_CHAT_ID"
        ]
        
        missing_vars = []
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            print(f"❌ Variables manquantes: {missing_vars}")
            print("📖 Consultez GUIDE_RECONFIGURATION.md")
    
    # 🔐 Credentials Crypto
    @property
    def private_key(self) -> Optional[str]:
        return os.getenv("PRIVATE_KEY")
    
    @property
    def wallet_address(self) -> Optional[str]:
        return os.getenv("WALLET_ADDRESS")
    
    @property
    def bsc_scan_api_key(self) -> Optional[str]:
        return os.getenv("BSC_SCAN_API_KEY")
    
    # 🏦 Binance API Keys
    @property
    def binance_api_key(self) -> Optional[str]:
        return os.getenv("safe_bot_API_KEY")
    
    @property
    def binance_api_secret(self) -> Optional[str]:
        return os.getenv("safe_bot_API_SECRET")
    
    @property
    def binance_test_api_key(self) -> Optional[str]:
        return os.getenv("safe_bot_TEST_API_KEY")
    
    @property
    def binance_test_api_secret(self) -> Optional[str]:
        return os.getenv("safe_bot_TEST_API_SECRET")
    
    @property
    def binance_prod_api_key(self) -> Optional[str]:
        return os.getenv("safe_bot_PROD_API_KEY")
    
    @property
    def binance_prod_api_secret(self) -> Optional[str]:
        return os.getenv("safe_bot_PROD_API_SECRET")
    
    # 📱 Telegram
    @property
    def telegram_token(self) -> Optional[str]:
        return os.getenv("TELEGRAM_BOT_TOKEN")
    
    @property
    def telegram_chat_id(self) -> Optional[str]:
        return os.getenv("TELEGRAM_CHAT_ID")
    
    # 🌐 URLs et Adresses
    @property
    def base_url(self) -> str:
        return os.getenv("BASE_URL", "https://api.binance.com")
    
    @property
    def bsc_rpc_url(self) -> str:
        return "https://bsc-dataseed.binance.org/"
    
    @property
    def pancakeswap_router(self) -> Optional[str]:
        return os.getenv("PANCAKESWAP_ROUTER")
    
    @property
    def wrapped_bnb(self) -> Optional[str]:
        return os.getenv("WRAPPED_BNB")
    
    def get_trading_config(self, environment: str = "test") -> Dict[str, Any]:
        """
        Retourne la configuration de trading pour un environnement donné
        
        Args:
            environment: 'test' ou 'prod'
        """
        base_config = {
            "pair": "BTCUSDT",
            "grid_size": 10,
            "grid_spacing": 100,
            "stop_loss_percent": 5,
            "take_profit_percent": 10,
        }
        
        if environment == "test":
            base_config.update({
                "order_size": 0.001,
                "capital_max": 1000,
                "base_url": "https://testnet.binance.vision",
                "api_key": self.binance_test_api_key,
                "api_secret": self.binance_test_api_secret,
            })
        elif environment == "prod":
            base_config.update({
                "order_size": 0.00011,
                "capital_max": 10,
                "base_url": self.base_url,
                "api_key": self.binance_prod_api_key,
                "api_secret": self.binance_prod_api_secret,
            })
        
        return base_config
    
    def get_sniper_config(self) -> Dict[str, Any]:
        """Configuration pour le sniper bot"""
        return {
            "invest_amount": 0.1,  # En BNB
            "slippage": 5,  # %
            "dex_screener_api": "https://api.dexscreener.com//token-profiles/latest/v1",
            "private_key": self.private_key,
            "wallet_address": self.wallet_address,
            "bsc_scan_api_key": self.bsc_scan_api_key,
            "pancakeswap_router": self.pancakeswap_router,
            "wrapped_bnb": self.wrapped_bnb,
        }
    
    def is_valid(self) -> bool:
        """Vérifie si la configuration est valide"""
        return all([
            self.private_key,
            self.wallet_address,
            self.telegram_token,
            self.telegram_chat_id
        ])

# Instance globale de configuration
config = BotConfig()
