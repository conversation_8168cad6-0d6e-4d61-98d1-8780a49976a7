"""
🧪 Tests d'intégration pour les bots de trading
Tests de l'intégration entre les différents composants
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta
import sys
from pathlib import Path

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent.parent))

from bots.dex_scalping.dex_scalping_bot import DexScalpingBot, ScalpingConfig
from bots.copy_trading.copy_trading_bot import CopyTradingBot, CopyTradingBotConfig
from bots.cross_chain_arbitrage.cross_chain_arbitrage_bot import CrossChainArbitrageBot, BotConfig
from dashboard.dashboard_manager import DashboardManager
from risk_management.portfolio_manager import portfolio_manager

@pytest.mark.integration
class TestBotIntegration:
    """Tests d'intégration pour les bots"""
    
    def setup_method(self):
        """Configuration avant chaque test"""
        # Réinitialiser le portfolio manager
        portfolio_manager.total_balance = 10000.0
        portfolio_manager.available_balance = 8000.0
        portfolio_manager.positions.clear()
        portfolio_manager.daily_trades.clear()
    
    @pytest.mark.asyncio
    async def test_dex_scalping_integration(self):
        """Test d'intégration du bot de scalping DEX"""
        # Configuration du bot
        config = ScalpingConfig(
            target_pairs=['ETH/USDC'],
            min_profit_usd=10.0,
            max_position_size_usd=1000.0,
            scan_interval_seconds=1  # Rapide pour les tests
        )
        
        bot = DexScalpingBot(config)
        
        # Mock des connecteurs DEX
        with patch('bots.dex_scalping.dex_connector.DexConnector') as mock_connector:
            mock_instance = AsyncMock()
            mock_connector.return_value = mock_instance
            
            # Simuler des prix différents pour créer une opportunité
            mock_instance.get_price.side_effect = [2000.0, 2020.0]  # Écart de 20$
            mock_instance.get_liquidity.return_value = 100000.0
            mock_instance.estimate_gas.return_value = 150000
            mock_instance.execute_swap.return_value = {
                'tx_hash': '0x123',
                'amount_out': 1.0,
                'gas_used': 145000,
                'effective_price': 2010.0
            }
            
            # Démarrer le bot
            bot_task = asyncio.create_task(bot.start())
            
            # Laisser le bot tourner un peu
            await asyncio.sleep(2)
            
            # Arrêter le bot
            await bot.stop()
            bot_task.cancel()
            
            try:
                await bot_task
            except asyncio.CancelledError:
                pass
            
            # Vérifier que le bot a détecté des opportunités
            assert bot.opportunities_detected > 0
    
    @pytest.mark.asyncio
    async def test_copy_trading_integration(self):
        """Test d'intégration du bot de copy trading"""
        # Configuration du bot
        config = CopyTradingBotConfig(
            tracked_wallets=[
                {
                    'address': '******************************************',
                    'label': 'Test Wallet',
                    'min_trade_size': 100.0
                }
            ],
            default_copy_percentage=1.0,
            max_concurrent_copies=2
        )
        
        bot = CopyTradingBot(config)
        
        # Mock de l'analyseur de wallets
        with patch.object(bot.wallet_analyzer, 'analyze_wallet_transactions') as mock_analyze:
            # Simuler une analyse réussie
            mock_analyze.return_value = Mock(
                total_trades=50,
                win_rate=75.0,
                total_pnl=2500.0,
                consistency_score=0.8,
                risk_score=0.3
            )
            
            # Mock du détecteur de signaux
            with patch.object(bot.signal_detector, 'monitor_wallets') as mock_monitor:
                mock_monitor.return_value = None
                
                # Démarrer le bot
                bot_task = asyncio.create_task(bot.start())
                
                # Laisser le bot s'initialiser
                await asyncio.sleep(1)
                
                # Arrêter le bot
                await bot.stop()
                bot_task.cancel()
                
                try:
                    await bot_task
                except asyncio.CancelledError:
                    pass
                
                # Vérifier que l'analyse a été appelée
                mock_analyze.assert_called()
    
    @pytest.mark.asyncio
    async def test_cross_chain_arbitrage_integration(self):
        """Test d'intégration du bot d'arbitrage cross-chain"""
        # Configuration du bot
        config = BotConfig(
            min_profit_usd=20.0,
            max_concurrent_trades=2,
            monitored_chains=['ethereum', 'bsc'],
            monitored_tokens=['USDT', 'USDC']
        )
        
        bot = CrossChainArbitrageBot(config)
        
        # Mock du connecteur de chaînes
        with patch.object(bot.chain_connector, 'get_all_stablecoin_prices') as mock_prices:
            # Simuler des prix avec écart pour arbitrage
            mock_prices.return_value = {
                'ethereum': {
                    'USDT': Mock(price_usd=1.0001, liquidity_usd=10000000),
                    'USDC': Mock(price_usd=0.9999, liquidity_usd=15000000)
                },
                'bsc': {
                    'USDT': Mock(price_usd=1.0025, liquidity_usd=8000000),  # Écart profitable
                    'USDC': Mock(price_usd=1.0002, liquidity_usd=12000000)
                }
            }
            
            # Mock de la santé des chaînes
            with patch.object(bot.chain_connector, 'check_chain_health') as mock_health:
                mock_health.return_value = {'healthy': True, 'chain_name': 'Test'}
                
                # Démarrer le bot
                bot_task = asyncio.create_task(bot.start())
                
                # Laisser le bot détecter des opportunités
                await asyncio.sleep(2)
                
                # Arrêter le bot
                await bot.stop()
                bot_task.cancel()
                
                try:
                    await bot_task
                except asyncio.CancelledError:
                    pass
                
                # Vérifier que des opportunités ont été détectées
                assert bot.opportunities_detected > 0
    
    @pytest.mark.asyncio
    async def test_dashboard_integration(self):
        """Test d'intégration du dashboard avec les bots"""
        # Créer le dashboard
        dashboard = DashboardManager(host="127.0.0.1", port=8081)
        
        # Créer des bots de test
        scalping_config = ScalpingConfig(target_pairs=['ETH/USDC'])
        scalping_bot = DexScalpingBot(scalping_config)
        
        copy_config = CopyTradingBotConfig(tracked_wallets=[])
        copy_bot = CopyTradingBot(copy_config)
        
        # Enregistrer les bots dans le dashboard
        dashboard.register_bot("scalping_bot", "dex_scalping", scalping_bot)
        dashboard.register_bot("copy_bot", "copy_trading", copy_bot)
        
        # Simuler des métriques
        scalping_bot.trades_executed = 10
        scalping_bot.total_profit = 150.0
        copy_bot.daily_copies = 5
        copy_bot.daily_profit = 75.0
        
        # Démarrer le dashboard
        dashboard_task = asyncio.create_task(dashboard.start())
        
        # Laisser le dashboard collecter des métriques
        await asyncio.sleep(2)
        
        # Vérifier les métriques collectées
        summary = dashboard.get_performance_summary()
        
        assert summary['trading']['total_bots'] == 2
        assert 'scalping_bot' in summary['dashboard']  # Vérifier que les bots sont trackés
        
        # Arrêter le dashboard
        await dashboard.stop()
        dashboard_task.cancel()
        
        try:
            await dashboard_task
        except asyncio.CancelledError:
            pass
    
    @pytest.mark.asyncio
    async def test_portfolio_manager_integration(self):
        """Test d'intégration avec le gestionnaire de portefeuille"""
        # Créer un bot de scalping
        config = ScalpingConfig(
            target_pairs=['ETH/USDC'],
            max_position_size_usd=1000.0
        )
        bot = DexScalpingBot(config)
        
        # Vérifier l'état initial du portefeuille
        initial_balance = portfolio_manager.get_available_balance()
        assert initial_balance == 8000.0
        
        # Simuler l'ouverture d'une position
        position_id = portfolio_manager.open_position(
            symbol="ETH/USDC",
            position_type="LONG",
            size=0.5,
            entry_price=2000.0
        )
        
        assert position_id is not None
        
        # Vérifier que la balance a été mise à jour
        new_balance = portfolio_manager.get_available_balance()
        assert new_balance == initial_balance - 1000.0  # 0.5 ETH * 2000 USD
        
        # Simuler la fermeture avec profit
        pnl = portfolio_manager.close_position(position_id, exit_price=2100.0)
        assert pnl == 50.0  # (2100 - 2000) * 0.5
        
        # Vérifier que le profit a été ajouté
        final_balance = portfolio_manager.get_available_balance()
        assert final_balance == initial_balance + 50.0
    
    @pytest.mark.asyncio
    async def test_error_propagation(self):
        """Test de la propagation d'erreurs entre composants"""
        # Créer un bot avec une configuration qui va générer des erreurs
        config = ScalpingConfig(
            target_pairs=['INVALID/PAIR'],  # Paire invalide
            min_profit_usd=10.0
        )
        bot = DexScalpingBot(config)
        
        # Mock qui lève des exceptions
        with patch('bots.dex_scalping.dex_connector.DexConnector') as mock_connector:
            mock_instance = AsyncMock()
            mock_connector.return_value = mock_instance
            mock_instance.get_price.side_effect = Exception("Erreur de connexion")
            
            # Démarrer le bot
            bot_task = asyncio.create_task(bot.start())
            
            # Laisser le bot essayer de fonctionner
            await asyncio.sleep(1)
            
            # Le bot doit continuer à fonctionner malgré les erreurs
            assert bot.is_running == True
            
            # Arrêter le bot
            await bot.stop()
            bot_task.cancel()
            
            try:
                await bot_task
            except asyncio.CancelledError:
                pass
    
    @pytest.mark.asyncio
    async def test_concurrent_bots(self):
        """Test de l'exécution concurrente de plusieurs bots"""
        # Créer plusieurs bots
        scalping_config = ScalpingConfig(
            target_pairs=['ETH/USDC'],
            scan_interval_seconds=1
        )
        scalping_bot = DexScalpingBot(scalping_config)
        
        copy_config = CopyTradingBotConfig(
            tracked_wallets=[],
            analysis_interval_minutes=1
        )
        copy_bot = CopyTradingBot(copy_config)
        
        arbitrage_config = BotConfig(
            monitored_chains=['ethereum', 'bsc'],
            scan_interval_seconds=1
        )
        arbitrage_bot = CrossChainArbitrageBot(arbitrage_config)
        
        # Mock des composants pour éviter les erreurs réseau
        with patch('bots.dex_scalping.dex_connector.DexConnector'), \
             patch.object(copy_bot.wallet_analyzer, 'analyze_wallet_transactions'), \
             patch.object(arbitrage_bot.chain_connector, 'get_all_stablecoin_prices'):
            
            # Démarrer tous les bots en parallèle
            tasks = [
                asyncio.create_task(scalping_bot.start()),
                asyncio.create_task(copy_bot.start()),
                asyncio.create_task(arbitrage_bot.start())
            ]
            
            # Laisser les bots tourner
            await asyncio.sleep(2)
            
            # Vérifier que tous les bots sont actifs
            assert scalping_bot.is_running == True
            assert copy_bot.is_running == True
            assert arbitrage_bot.is_running == True
            
            # Arrêter tous les bots
            await scalping_bot.stop()
            await copy_bot.stop()
            await arbitrage_bot.stop()
            
            # Annuler les tâches
            for task in tasks:
                task.cancel()
            
            await asyncio.gather(*tasks, return_exceptions=True)
    
    @pytest.mark.asyncio
    async def test_resource_management(self):
        """Test de la gestion des ressources"""
        # Créer un bot
        config = ScalpingConfig(target_pairs=['ETH/USDC'])
        bot = DexScalpingBot(config)
        
        # Mesurer l'utilisation mémoire initiale
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss
        
        # Démarrer et arrêter le bot plusieurs fois
        for _ in range(3):
            bot_task = asyncio.create_task(bot.start())
            await asyncio.sleep(0.5)
            await bot.stop()
            bot_task.cancel()
            
            try:
                await bot_task
            except asyncio.CancelledError:
                pass
        
        # Mesurer l'utilisation mémoire finale
        final_memory = process.memory_info().rss
        memory_increase = final_memory - initial_memory
        
        # Vérifier qu'il n'y a pas de fuite mémoire importante (< 50MB)
        assert memory_increase < 50 * 1024 * 1024, f"Fuite mémoire détectée: {memory_increase} bytes"
    
    @pytest.mark.asyncio
    async def test_configuration_hot_reload(self):
        """Test du rechargement à chaud de la configuration"""
        # Créer un bot avec une configuration initiale
        config = ScalpingConfig(
            target_pairs=['ETH/USDC'],
            min_profit_usd=10.0
        )
        bot = DexScalpingBot(config)
        
        # Vérifier la configuration initiale
        assert bot.config.min_profit_usd == 10.0
        assert len(bot.config.target_pairs) == 1
        
        # Modifier la configuration pendant l'exécution
        new_config = {
            'min_profit_usd': 20.0,
            'target_pairs': ['ETH/USDC', 'WBTC/USDC']
        }
        
        bot.update_config(new_config)
        
        # Vérifier que la configuration a été mise à jour
        assert bot.config.min_profit_usd == 20.0
        assert len(bot.config.target_pairs) == 2
        assert 'WBTC/USDC' in bot.config.target_pairs
    
    @pytest.mark.asyncio
    async def test_graceful_shutdown(self):
        """Test de l'arrêt gracieux des bots"""
        # Créer un bot
        config = ScalpingConfig(target_pairs=['ETH/USDC'])
        bot = DexScalpingBot(config)
        
        # Mock pour simuler des opérations en cours
        with patch.object(bot.scalping_engine, 'execute_scalping_trade') as mock_execute:
            # Simuler une exécution lente
            async def slow_execution(opportunity):
                await asyncio.sleep(2)
                return True
            
            mock_execute.side_effect = slow_execution
            
            # Démarrer le bot
            bot_task = asyncio.create_task(bot.start())
            await asyncio.sleep(0.5)
            
            # Déclencher un arrêt gracieux
            shutdown_start = asyncio.get_event_loop().time()
            await bot.stop()
            shutdown_time = asyncio.get_event_loop().time() - shutdown_start
            
            # Vérifier que l'arrêt s'est fait rapidement (< 1 seconde)
            assert shutdown_time < 1.0
            
            # Nettoyer
            bot_task.cancel()
            try:
                await bot_task
            except asyncio.CancelledError:
                pass
