"""
🔒 Module de sécurité pour botCrypto
Validations et vérifications de sécurité avant les trades
"""

import re
import time
import requests
import logging
from typing import Dict, List, Optional, Tuple
from web3 import Web3
from config.settings import config

class SecurityValidator:
    """Validateur de sécurité pour les trades automatiques"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.web3 = None
        self._init_web3()
        
        # Listes de sécurité
        self.token_blacklist = set()
        self.trusted_tokens = {
            "******************************************",  # WBNB
            "******************************************",  # USDT
            "******************************************",  # BUSD
            "******************************************",  # USDC
        }
        
        # Limites de sécurité
        self.max_investment_per_token = 0.1  # BNB
        self.min_liquidity_usd = 10000  # USD
        self.max_slippage = 10  # %
        self.min_token_age_hours = 1  # heures
    
    def _init_web3(self):
        """Initialise la connexion Web3"""
        try:
            self.web3 = Web3(Web3.HTTPProvider(config.bsc_rpc_url))
            if not self.web3.is_connected():
                self.logger.error("❌ Impossible de se connecter à BSC")
        except Exception as e:
            self.logger.error(f"❌ Erreur initialisation Web3: {e}")
    
    def is_valid_address(self, address: str) -> bool:
        """Vérifie si une adresse est valide"""
        try:
            return Web3.is_address(address) and Web3.is_checksum_address(Web3.to_checksum_address(address))
        except:
            return False
    
    def is_token_blacklisted(self, token_address: str) -> bool:
        """Vérifie si un token est dans la blacklist"""
        return token_address.lower() in self.token_blacklist
    
    def add_to_blacklist(self, token_address: str, reason: str = "Manual"):
        """Ajoute un token à la blacklist"""
        self.token_blacklist.add(token_address.lower())
        self.logger.warning(f"🚫 Token blacklisté: {token_address} - Raison: {reason}")
    
    def check_token_contract(self, token_address: str) -> Dict[str, any]:
        """
        Vérifie les propriétés du contrat du token
        
        Returns:
            Dict avec les résultats de vérification
        """
        result = {
            "is_valid": False,
            "is_contract": False,
            "has_source_code": False,
            "is_verified": False,
            "warnings": []
        }
        
        if not self.is_valid_address(token_address):
            result["warnings"].append("Adresse invalide")
            return result
        
        try:
            # Vérifier si c'est un contrat
            code = self.web3.eth.get_code(Web3.to_checksum_address(token_address))
            result["is_contract"] = len(code) > 0
            
            if not result["is_contract"]:
                result["warnings"].append("Pas un contrat")
                return result
            
            # Vérifier via BSCScan API
            bsc_info = self._check_bscscan_contract(token_address)
            result.update(bsc_info)
            
            result["is_valid"] = True
            
        except Exception as e:
            result["warnings"].append(f"Erreur vérification: {e}")
            self.logger.error(f"❌ Erreur vérification contrat {token_address}: {e}")
        
        return result
    
    def _check_bscscan_contract(self, token_address: str) -> Dict[str, any]:
        """Vérifie le contrat via BSCScan API"""
        result = {"has_source_code": False, "is_verified": False, "warnings": []}
        
        if not config.bsc_scan_api_key:
            result["warnings"].append("BSCScan API key manquante")
            return result
        
        try:
            url = "https://api.bscscan.com/api"
            params = {
                "module": "contract",
                "action": "getsourcecode",
                "address": token_address,
                "apikey": config.bsc_scan_api_key
            }
            
            response = requests.get(url, params=params, timeout=10)
            data = response.json()
            
            if data.get("status") == "1" and data.get("result"):
                source_code = data["result"][0].get("SourceCode", "")
                result["has_source_code"] = len(source_code) > 0
                result["is_verified"] = data["result"][0].get("ABI") != "Contract source code not verified"
                
                if not result["is_verified"]:
                    result["warnings"].append("Contrat non vérifié")
            
        except Exception as e:
            result["warnings"].append(f"Erreur BSCScan: {e}")
        
        return result
    
    def check_liquidity(self, token_address: str) -> Dict[str, any]:
        """
        Vérifie la liquidité du token sur PancakeSwap
        
        Returns:
            Dict avec les informations de liquidité
        """
        result = {
            "has_sufficient_liquidity": False,
            "liquidity_usd": 0,
            "warnings": []
        }
        
        try:
            # Utiliser DexScreener API pour obtenir les infos de liquidité
            url = f"https://api.dexscreener.com/latest/dex/tokens/{token_address}"
            response = requests.get(url, timeout=10)
            data = response.json()
            
            if data.get("pairs"):
                # Prendre la paire avec la plus grande liquidité
                best_pair = max(data["pairs"], key=lambda x: float(x.get("liquidity", {}).get("usd", 0)))
                liquidity_usd = float(best_pair.get("liquidity", {}).get("usd", 0))
                
                result["liquidity_usd"] = liquidity_usd
                result["has_sufficient_liquidity"] = liquidity_usd >= self.min_liquidity_usd
                
                if not result["has_sufficient_liquidity"]:
                    result["warnings"].append(f"Liquidité insuffisante: ${liquidity_usd:,.0f} < ${self.min_liquidity_usd:,.0f}")
            else:
                result["warnings"].append("Aucune paire trouvée")
                
        except Exception as e:
            result["warnings"].append(f"Erreur vérification liquidité: {e}")
            self.logger.error(f"❌ Erreur liquidité {token_address}: {e}")
        
        return result
    
    def check_token_age(self, token_address: str) -> Dict[str, any]:
        """
        Vérifie l'âge du token (première transaction)
        
        Returns:
            Dict avec les informations d'âge
        """
        result = {
            "is_old_enough": False,
            "age_hours": 0,
            "warnings": []
        }
        
        try:
            if not config.bsc_scan_api_key:
                result["warnings"].append("BSCScan API key manquante")
                return result
            
            url = "https://api.bscscan.com/api"
            params = {
                "module": "account",
                "action": "txlist",
                "address": token_address,
                "startblock": 0,
                "endblock": ********,
                "page": 1,
                "offset": 1,
                "sort": "asc",
                "apikey": config.bsc_scan_api_key
            }
            
            response = requests.get(url, params=params, timeout=10)
            data = response.json()
            
            if data.get("status") == "1" and data.get("result"):
                first_tx = data["result"][0]
                creation_time = int(first_tx["timeStamp"])
                current_time = int(time.time())
                age_hours = (current_time - creation_time) / 3600
                
                result["age_hours"] = age_hours
                result["is_old_enough"] = age_hours >= self.min_token_age_hours
                
                if not result["is_old_enough"]:
                    result["warnings"].append(f"Token trop récent: {age_hours:.1f}h < {self.min_token_age_hours}h")
            else:
                result["warnings"].append("Impossible de déterminer l'âge")
                
        except Exception as e:
            result["warnings"].append(f"Erreur vérification âge: {e}")
            self.logger.error(f"❌ Erreur âge {token_address}: {e}")
        
        return result
    
    def validate_trade_amount(self, amount_bnb: float) -> Dict[str, any]:
        """Valide le montant d'un trade"""
        result = {
            "is_valid": False,
            "warnings": []
        }
        
        if amount_bnb <= 0:
            result["warnings"].append("Montant invalide")
        elif amount_bnb > self.max_investment_per_token:
            result["warnings"].append(f"Montant trop élevé: {amount_bnb} > {self.max_investment_per_token} BNB")
        else:
            result["is_valid"] = True
        
        return result
    
    def comprehensive_token_check(self, token_address: str, amount_bnb: float) -> Dict[str, any]:
        """
        Effectue une vérification complète d'un token avant trade
        
        Returns:
            Dict avec tous les résultats de vérification
        """
        self.logger.info(f"🔍 Vérification complète du token: {token_address}")
        
        result = {
            "is_safe_to_trade": False,
            "risk_level": "HIGH",
            "warnings": [],
            "checks": {}
        }
        
        # Vérifications de base
        if self.is_token_blacklisted(token_address):
            result["warnings"].append("Token blacklisté")
            return result
        
        # Vérification du montant
        amount_check = self.validate_trade_amount(amount_bnb)
        result["checks"]["amount"] = amount_check
        
        # Vérification du contrat
        contract_check = self.check_token_contract(token_address)
        result["checks"]["contract"] = contract_check
        
        # Vérification de la liquidité
        liquidity_check = self.check_liquidity(token_address)
        result["checks"]["liquidity"] = liquidity_check
        
        # Vérification de l'âge
        age_check = self.check_token_age(token_address)
        result["checks"]["age"] = age_check
        
        # Compilation des warnings
        for check in result["checks"].values():
            result["warnings"].extend(check.get("warnings", []))
        
        # Détermination du niveau de risque
        risk_score = 0
        if amount_check.get("is_valid"): risk_score += 1
        if contract_check.get("is_verified"): risk_score += 2
        if liquidity_check.get("has_sufficient_liquidity"): risk_score += 2
        if age_check.get("is_old_enough"): risk_score += 1
        
        if risk_score >= 5:
            result["risk_level"] = "LOW"
            result["is_safe_to_trade"] = True
        elif risk_score >= 3:
            result["risk_level"] = "MEDIUM"
            result["is_safe_to_trade"] = False  # Prudence par défaut
        else:
            result["risk_level"] = "HIGH"
            result["is_safe_to_trade"] = False
        
        self.logger.info(f"🎯 Résultat: {result['risk_level']} risk, Safe: {result['is_safe_to_trade']}")
        
        return result

# Instance globale du validateur de sécurité
security = SecurityValidator()
