#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 Fix Dependencies
Corrige les conflits de dépendances et réinstalle proprement
"""

import subprocess
import sys
import os

def run_command(command, description):
    """Exécute une commande et affiche le résultat"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {description} réussi")
            return True
        else:
            print(f"❌ {description} échoué:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ Erreur {description}: {e}")
        return False

def create_minimal_requirements():
    """Crée un fichier requirements minimal pour le bot"""
    
    minimal_deps = """# Dépendances essentielles pour botCrypto
requests>=2.28.0
pandas>=1.5.0
numpy>=1.21.0
matplotlib>=3.5.0
seaborn>=0.11.0
python-dotenv>=0.19.0
psutil>=5.9.0

# Trading et crypto
ccxt>=4.0.0

# Optionnel pour l'interface web (si nécessaire)
# fastapi>=0.104.1
# uvicorn[standard]>=0.24.0

# Optionnel pour l'IA (si nécessaire)
# openai>=1.63.2
"""
    
    with open('requirements_minimal.txt', 'w', encoding='utf-8') as f:
        f.write(minimal_deps)
    
    print("✅ Fichier requirements_minimal.txt créé")

def main():
    """Fonction principale"""
    print("🔧 CORRECTION DES DÉPENDANCES")
    print("=" * 50)
    
    # 1. Créer un requirements minimal
    create_minimal_requirements()
    
    # 2. Proposer les options
    print("\n💡 OPTIONS DE CORRECTION:")
    print("1. 🧹 Installation minimale (recommandé)")
    print("2. 🔄 Réinstallation complète")
    print("3. 🎯 Installation sélective")
    
    choice = input("\nChoisissez une option (1-3): ").strip()
    
    if choice == "1":
        print("\n🧹 INSTALLATION MINIMALE")
        print("=" * 30)
        
        # Installer les dépendances minimales
        if run_command("pip install -r requirements_minimal.txt", "Installation dépendances minimales"):
            print("\n✅ INSTALLATION MINIMALE TERMINÉE")
            print("\n🚀 COMMANDES POUR TESTER:")
            print("python simple_test.py")
            print("python bots/safe_bot.py --mode testnet")
        
    elif choice == "2":
        print("\n🔄 RÉINSTALLATION COMPLÈTE")
        print("=" * 30)
        
        # Désinstaller tout
        run_command("pip freeze > installed_packages.txt", "Sauvegarde des packages installés")
        run_command("pip uninstall -y -r installed_packages.txt", "Désinstallation complète")
        
        # Réinstaller avec le nouveau requirements.txt
        if run_command("pip install -r requirements.txt", "Réinstallation complète"):
            print("\n✅ RÉINSTALLATION TERMINÉE")
        
    elif choice == "3":
        print("\n🎯 INSTALLATION SÉLECTIVE")
        print("=" * 30)
        
        # Installer par groupes
        essential = ["requests", "pandas", "numpy", "matplotlib", "python-dotenv", "ccxt"]
        
        print("📦 Installation des dépendances essentielles...")
        for package in essential:
            run_command(f"pip install {package}", f"Installation {package}")
        
        print("\n💡 Dépendances optionnelles:")
        print("- FastAPI: pip install fastapi uvicorn[standard]")
        print("- OpenAI: pip install openai")
        print("- Analyse avancée: pip install scipy scikit-learn")
        
    else:
        print("❌ Option invalide")
        return
    
    print("\n🎉 CORRECTION TERMINÉE")
    print("\n📋 PROCHAINES ÉTAPES:")
    print("1. Testez la connexion: python simple_test.py")
    print("2. Lancez le bot: python bots/safe_bot.py --mode testnet")
    print("3. Si problème, utilisez requirements_minimal.txt")

if __name__ == "__main__":
    main()
