# ⚡ Bot de Scalping DEX Uniswap v3

## 🎯 Vue d'ensemble

Bot de trading haute fréquence spécialisé dans le scalping sur Uniswap v3. Optimisé pour la paire ETH-WBTC avec gestion avancée des frais, protection MEV et stratégies de timing intelligent.

## 🏗️ Architecture

```
bots/dex_scalping/
├── __init__.py                 # Module principal
├── uniswap_v3_connector.py     # Interface Uniswap v3
├── scalping_engine.py          # Moteur de scalping
├── mev_optimizer.py           # Optimiseur MEV et frais
├── dex_scalping_bot.py        # Bot principal intégré
└── README.md                  # Cette documentation
```

## 🚀 Fonctionnalités

### Trading Haute Fréquence
- **Scalping automatisé** sur Uniswap v3
- **Détection d'opportunités** en temps réel
- **Exécution rapide** avec optimisation gas
- **Gestion des positions** automatique

### Protection MEV
- **Détection de sandwich attacks**
- **Optimisation du timing** d'exécution
- **Protection contre le frontrunning**
- **Mempool privé** (Flashbots compatible)

### Gestion des Frais
- **Stratégies de gas dynamiques**
- **Optimisation des coûts** de transaction
- **Calcul de rentabilité** après frais
- **Monitoring des conditions réseau**

### Gestion des Risques
- **Position sizing** intelligent
- **Stop-loss adaptatifs**
- **Limites quotidiennes**
- **Arrêt d'urgence** automatique

## 🛠️ Installation et Configuration

### 1. Prérequis

```bash
# Dépendances Python
pip install web3 eth-account numpy pandas asyncio

# Variables d'environnement
export WEB3_PROVIDER="https://mainnet.infura.io/v3/YOUR_PROJECT_ID"
export PRIVATE_KEY="your_private_key_here"
```

### 2. Configuration

Créer un fichier `config/dex_scalping_config.json` :

```json
{
  "web3_provider": "https://mainnet.infura.io/v3/YOUR_PROJECT_ID",
  "private_key": "YOUR_PRIVATE_KEY",
  "network": "mainnet",
  "trading_pair": "ETH/WBTC",
  "pool_fee": 3000,
  "min_profit_threshold": 0.002,
  "max_position_size": 0.02,
  "max_daily_trades": 50,
  "max_daily_loss": 0.01,
  "use_mev_protection": true,
  "max_gas_price_gwei": 150,
  "enable_notifications": true
}
```

### 3. Lancement

```python
from bots.dex_scalping.dex_scalping_bot import DEXScalpingBot, BotConfig

# Configuration
config = BotConfig(
    web3_provider="https://mainnet.infura.io/v3/YOUR_PROJECT_ID",
    private_key="YOUR_PRIVATE_KEY",
    trading_pair="ETH/WBTC",
    min_profit_threshold=0.002,
    use_mev_protection=True
)

# Créer et lancer le bot
bot = DEXScalpingBot(config)
await bot.initialize()
await bot.start()
```

## 📊 Stratégies de Scalping

### Détection d'Opportunités

Le bot utilise plusieurs signaux pour détecter les opportunités :

1. **Analyse de tendance** (moyennes mobiles)
2. **Signaux de retournement** (RSI, divergences)
3. **Volatilité optimale** (ni trop faible, ni trop élevée)
4. **Liquidité suffisante** (éviter l'impact de marché)

### Calcul de Rentabilité

```python
# Exemple de calcul
entry_price = 15.5  # ETH/WBTC
target_price = 15.52  # +0.13%
stop_loss = 15.48  # -0.13%

# Frais estimés
pool_fee = 0.3%  # Uniswap v3
gas_cost = 0.01 ETH
slippage = 0.1%

# Profit net = (target - entry) - frais - gas - slippage
net_profit = (15.52 - 15.5) / 15.5 - 0.003 - gas_cost - 0.001
```

### Gestion des Positions

| Paramètre | Valeur | Description |
|-----------|--------|-------------|
| **Taille max** | 2-5% | Du capital total |
| **Hold time** | 1-5 min | Durée max par position |
| **Take profit** | 0.2-0.5% | Objectif de profit |
| **Stop loss** | 0.1-0.3% | Limite de perte |

## 🛡️ Protection MEV

### Types de Protection

1. **Sandwich Detection**
   - Analyse du mempool
   - Détection de patterns suspects
   - Délai d'exécution si nécessaire

2. **Frontrun Protection**
   - Gas pricing intelligent
   - Timing optimal
   - Mempool privé

3. **Slippage Protection**
   - Calcul dynamique du slippage
   - Ajustement selon la liquidité
   - Limites strictes

### Configuration MEV

```python
mev_protection = MEVProtection(
    use_private_mempool=True,
    max_slippage_protection=0.001,  # 0.1%
    sandwich_detection=True,
    frontrun_protection=True,
    deadline_buffer=30  # secondes
)
```

## ⛽ Optimisation des Frais

### Stratégies de Gas

| Stratégie | Gas Price | Priority Fee | Usage |
|-----------|-----------|--------------|-------|
| **Conservative** | Base + 10% | 1 gwei | Conditions normales |
| **Aggressive** | Base + 50% | 5 gwei | Opportunités urgentes |
| **Dynamic** | Calculé | Variable | Adaptation automatique |

### Calcul des Coûts

```python
# Coûts totaux d'un trade
total_cost = (
    gas_used * gas_price +           # Frais de gas
    amount * pool_fee_rate +         # Frais Uniswap
    amount * slippage_rate           # Coût du slippage
)

# Seuil de rentabilité
min_profit = total_cost * 1.2  # 20% de marge
```

## 📈 Métriques de Performance

### KPIs Principaux

- **Win Rate** : Pourcentage de trades profitables
- **Profit Factor** : Ratio profits/pertes
- **Sharpe Ratio** : Rendement ajusté du risque
- **Max Drawdown** : Perte maximale
- **Gas Efficiency** : Coût moyen par trade

### Monitoring

```python
# Statistiques en temps réel
stats = bot.get_performance_stats()
{
    'total_trades': 45,
    'win_rate': 67.5,
    'total_profit': 0.0234,  # ETH
    'gas_spent': 0.0156,     # ETH
    'avg_hold_time': 2.3,    # minutes
    'mev_protection_rate': 95.2
}
```

## 🔧 Configuration Avancée

### Paramètres de Scalping

```python
scalping_config = ScalpingConfig(
    # Seuils de profit
    min_profit_threshold=0.002,      # 0.2%
    min_profit_after_fees=0.0005,    # 0.05%
    
    # Gestion des positions
    max_position_size=0.05,          # 5%
    max_hold_time=300,               # 5 minutes
    
    # Conditions de marché
    min_volatility=0.002,            # 0.2%
    max_volatility=0.05,             # 5%
    min_liquidity=1000000,           # $1M
    
    # Timing
    check_interval=0.5,              # 500ms
    max_gas_price_gwei=200
)
```

### Gestion des Risques

```python
risk_config = {
    'max_daily_trades': 100,
    'max_daily_loss': 0.02,          # 2%
    'emergency_stop_loss': 0.05,     # 5%
    'max_consecutive_losses': 5,
    'position_size_scaling': True,
    'volatility_adjustment': True
}
```

## 🧪 Tests et Validation

### Tests Unitaires

```bash
# Tester les composants
python -m pytest tests/test_uniswap_connector.py
python -m pytest tests/test_scalping_engine.py
python -m pytest tests/test_mev_optimizer.py
```

### Simulation

```bash
# Lancer l'exemple
python examples/dex_scalping_example.py

# Simulation avec configuration custom
python examples/dex_scalping_example.py --config custom_config.json
```

### Backtesting

```python
from backtesting.backtest_engine import BacktestEngine

# Backtest sur données historiques
backtest = BacktestEngine()
results = await backtest.run_dex_scalping_strategy(
    start_date="2024-01-01",
    end_date="2024-01-31",
    initial_capital=1.0,  # ETH
    config=scalping_config
)
```

## 📊 Exemples d'Utilisation

### 1. Configuration Basique

```python
# Bot simple pour débutants
config = BotConfig(
    web3_provider="https://mainnet.infura.io/v3/YOUR_ID",
    private_key="YOUR_KEY",
    trading_pair="ETH/WBTC",
    min_profit_threshold=0.005,      # 0.5% - plus conservateur
    max_position_size=0.01,          # 1% - petites positions
    max_daily_trades=20,             # Limite basse
    use_mev_protection=True
)
```

### 2. Configuration Agressive

```python
# Bot pour traders expérimentés
config = BotConfig(
    web3_provider="https://mainnet.infura.io/v3/YOUR_ID",
    private_key="YOUR_KEY",
    trading_pair="ETH/WBTC",
    min_profit_threshold=0.001,      # 0.1% - plus agressif
    max_position_size=0.1,           # 10% - positions importantes
    max_daily_trades=200,            # Haute fréquence
    max_gas_price_gwei=300,          # Gas élevé accepté
    use_mev_protection=True
)
```

### 3. Mode Test

```python
# Configuration pour testnet
config = BotConfig(
    web3_provider="https://goerli.infura.io/v3/YOUR_ID",
    private_key="YOUR_TEST_KEY",
    network="goerli",
    trading_pair="ETH/WBTC",
    min_profit_threshold=0.01,       # 1% - seuil élevé pour tests
    enable_notifications=False       # Pas de notifications en test
)
```

## ⚠️ Avertissements et Risques

### Risques Techniques
- **Smart contract risk** : Bugs dans Uniswap v3
- **Slippage** : Impact sur les gros ordres
- **Gas wars** : Coûts élevés en période de congestion
- **MEV attacks** : Sandwich, frontrunning

### Risques Financiers
- **Perte en capital** : Trading à haute fréquence risqué
- **Impermanent loss** : Variations de prix
- **Frais cumulés** : Coûts de transaction élevés

### Bonnes Pratiques
1. **Commencer petit** : Tester avec de petits montants
2. **Utiliser testnet** : Valider avant production
3. **Surveiller activement** : Monitoring continu
4. **Limites strictes** : Stop-loss et limites quotidiennes
5. **Backup funds** : Garder des réserves pour le gas

## 📞 Support et Maintenance

### Logs et Debugging

```python
# Activer les logs détaillés
import logging
logging.basicConfig(level=logging.DEBUG)

# Consulter les logs
tail -f logs/trading/trading.log
```

### Monitoring

- **Dashboard** : Métriques en temps réel
- **Alertes** : Notifications Telegram/Discord
- **Rapports** : Analyses de performance quotidiennes

---

**⚠️ DISCLAIMER** : Ce bot est fourni à des fins éducatives. Le trading de cryptomonnaies comporte des risques importants. Utilisez à vos propres risques.
