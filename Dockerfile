# 🤖 BotCrypto - Dockerfile
# Système de trading automatisé avancé

# ===== STAGE 1: Base Image =====
FROM python:3.10-slim as base

# Métadonnées
LABEL maintainer="<EMAIL>"
LABEL version="1.0.0"
LABEL description="BotCrypto - Système de trading automatisé avancé"
LABEL org.opencontainers.image.source="https://github.com/JeremieN6/botCrypto"

# Variables d'environnement
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV ENVIRONMENT=production
ENV DEBIAN_FRONTEND=noninteractive

# ===== STAGE 2: Dependencies =====
FROM base as dependencies

# Installer les dépendances système
RUN apt-get update && apt-get install -y \
    # Compilateurs et outils de build
    gcc \
    g++ \
    make \
    # Bibliothèques de développement
    libffi-dev \
    libssl-dev \
    libpq-dev \
    # Outils réseau
    curl \
    wget \
    # Utilitaires
    git \
    && rm -rf /var/lib/apt/lists/*

# Mettre à jour pip
RUN pip install --no-cache-dir --upgrade pip setuptools wheel

# ===== STAGE 3: Application =====
FROM dependencies as application

# Créer un utilisateur non-root
RUN groupadd -r botcrypto && useradd -r -g botcrypto -s /bin/bash botcrypto

# Créer les répertoires nécessaires
RUN mkdir -p /app /app/logs /app/data /app/config && \
    chown -R botcrypto:botcrypto /app

# Définir le répertoire de travail
WORKDIR /app

# Copier les requirements en premier (pour le cache Docker)
COPY requirements.txt .

# Installer les dépendances Python
RUN pip install --no-cache-dir -r requirements.txt

# Copier le code source
COPY . .

# Créer les répertoires de données
RUN mkdir -p logs data config/secrets && \
    chown -R botcrypto:botcrypto /app

# Copier la configuration d'exemple
RUN cp config/config.example.json config/config.json

# Permissions
RUN chmod +x scripts/*.sh 2>/dev/null || true
RUN chown -R botcrypto:botcrypto /app

# Passer à l'utilisateur non-root
USER botcrypto

# ===== STAGE 4: Production =====
FROM application as production

# Variables d'environnement de production
ENV ENVIRONMENT=production
ENV DEBUG=false
ENV LOG_LEVEL=INFO

# Volumes pour la persistance des données
VOLUME ["/app/logs", "/app/data", "/app/config"]

# Ports exposés
EXPOSE 8080 9090

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/api/health || exit 1

# Point d'entrée
ENTRYPOINT ["python", "-m"]
CMD ["dashboard.dashboard_manager"]

# ===== STAGE 5: Development =====
FROM application as development

# Variables d'environnement de développement
ENV ENVIRONMENT=development
ENV DEBUG=true
ENV LOG_LEVEL=DEBUG

# Installer les dépendances de développement
RUN pip install --no-cache-dir \
    pytest \
    pytest-asyncio \
    pytest-cov \
    flake8 \
    black \
    isort \
    mypy

# Commande par défaut pour le développement
CMD ["python", "-m", "dashboard.dashboard_manager"]

# ===== MULTI-STAGE BUILD TARGETS =====

# Target par défaut (production)
FROM production

# ===== DOCKER COMPOSE SUPPORT =====

# Variables d'environnement pour Docker Compose
ENV DATABASE_URL=*********************************************/botcrypto
ENV REDIS_URL=redis://redis:6379/0

# ===== KUBERNETES SUPPORT =====

# Labels pour Kubernetes
LABEL app.kubernetes.io/name="botcrypto"
LABEL app.kubernetes.io/component="trading-bot"
LABEL app.kubernetes.io/part-of="botcrypto-system"

# ===== SECURITY =====

# Utiliser un utilisateur non-root
USER botcrypto

# Répertoire de travail sécurisé
WORKDIR /app

# ===== OPTIMIZATION =====

# Optimisations Python
ENV PYTHONOPTIMIZE=1
ENV PYTHONHASHSEED=random

# ===== MONITORING =====

# Métriques Prometheus
EXPOSE 9090

# ===== DOCUMENTATION =====

# Documentation intégrée
EXPOSE 8080

# ===== BUILD ARGS =====

# Arguments de build
ARG BUILD_DATE
ARG VCS_REF
ARG VERSION

# Labels avec informations de build
LABEL org.opencontainers.image.created=$BUILD_DATE
LABEL org.opencontainers.image.revision=$VCS_REF
LABEL org.opencontainers.image.version=$VERSION

# ===== USAGE EXAMPLES =====

# Build de base:
# docker build -t botcrypto .

# Build de développement:
# docker build --target development -t botcrypto:dev .

# Build avec arguments:
# docker build \
#   --build-arg BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ') \
#   --build-arg VCS_REF=$(git rev-parse --short HEAD) \
#   --build-arg VERSION=1.0.0 \
#   -t botcrypto:1.0.0 .

# Run simple:
# docker run -p 8080:8080 botcrypto

# Run avec variables d'environnement:
# docker run -p 8080:8080 \
#   -e WEB3_PROVIDER_URL="https://mainnet.infura.io/v3/YOUR_KEY" \
#   -e PRIVATE_KEY="your_private_key" \
#   botcrypto

# Run avec volumes:
# docker run -p 8080:8080 \
#   -v $(pwd)/logs:/app/logs \
#   -v $(pwd)/data:/app/data \
#   -v $(pwd)/config:/app/config \
#   botcrypto

# ===== DOCKER COMPOSE =====

# Utilisation avec docker-compose.yml:
# version: '3.8'
# services:
#   botcrypto:
#     build: .
#     ports:
#       - "8080:8080"
#     environment:
#       - DATABASE_URL=************************************/db
#     volumes:
#       - ./logs:/app/logs
#       - ./data:/app/data

# ===== KUBERNETES =====

# Déploiement Kubernetes:
# apiVersion: apps/v1
# kind: Deployment
# metadata:
#   name: botcrypto
# spec:
#   replicas: 2
#   selector:
#     matchLabels:
#       app: botcrypto
#   template:
#     metadata:
#       labels:
#         app: botcrypto
#     spec:
#       containers:
#       - name: botcrypto
#         image: botcrypto:latest
#         ports:
#         - containerPort: 8080

# ===== TROUBLESHOOTING =====

# Debug du container:
# docker run -it --entrypoint /bin/bash botcrypto

# Logs du container:
# docker logs -f container_name

# Inspection du container:
# docker inspect container_name

# ===== SECURITY NOTES =====

# - Utilise un utilisateur non-root
# - Pas de secrets dans l'image
# - Volumes pour les données sensibles
# - Health checks intégrés
# - Multi-stage build pour optimiser la taille

# ===== PERFORMANCE NOTES =====

# - Image optimisée avec multi-stage build
# - Cache des layers Docker
# - Dépendances installées en premier
# - Variables d'environnement optimisées

# ===== END OF DOCKERFILE =====
