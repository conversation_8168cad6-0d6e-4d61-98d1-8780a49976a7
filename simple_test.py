#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test simple sans emojis
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Configuration Unicode pour Windows
if sys.platform.startswith('win'):
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    if hasattr(sys.stdout, 'reconfigure'):
        try:
            sys.stdout.reconfigure(encoding='utf-8')
            sys.stderr.reconfigure(encoding='utf-8')
        except:
            pass

def test_connection():
    """Test de connexion simple"""
    
    # Charger l'environnement
    env_file = Path(".env.local")
    if env_file.exists():
        load_dotenv(env_file)
        print("[OK] Configuration chargée")
    else:
        print("[ERROR] Fichier .env.local manquant")
        return False
    
    # Tester les clés API
    api_key = os.getenv("safe_bot_TEST_API_KEY")
    api_secret = os.getenv("safe_bot_TEST_API_SECRET")
    
    if api_key and api_secret:
        print("[OK] Clés API testnet trouvées")
        print(f"[INFO] API Key: {api_key[:8]}...")
        return True
    else:
        print("[ERROR] Clés API manquantes")
        return False

if __name__ == "__main__":
    print("=== TEST DE CONNEXION SIMPLE ===")
    
    if test_connection():
        print("[SUCCESS] Test réussi")
    else:
        print("[FAILED] Test échoué")
