# 🛡️ Système de Gestion d'Erreurs Robuste

## 🎯 Vue d'ensemble

Le système de gestion d'erreurs de botCrypto fournit une approche robuste et centralisée pour gérer les erreurs, avec retry automatique, circuit breakers, fallback strategies et monitoring en temps réel.

## 🏗️ Architecture

```
utils/
├── error_handler.py         # Gestionnaire d'erreurs centralisé
├── network_handler.py       # Gestion spécialisée erreurs réseau
├── trading_handler.py       # Gestion spécialisée erreurs trading
└── README_error_handling.md # Cette documentation

monitoring/
└── error_monitor.py         # Monitoring et alertes en temps réel
```

## 🚀 Utilisation Rapide

### 1. Gestion d'erreurs basique

```python
from utils.error_handler import error_handler, ErrorCategory, ErrorSeverity

try:
    # Code qui peut échouer
    risky_operation()
except Exception as e:
    error_handler.handle_error(
        e, 
        ErrorCategory.NETWORK, 
        ErrorSeverity.HIGH,
        context={'operation': 'api_call', 'endpoint': '/api/data'}
    )
```

### 2. Décorateur retry automatique

```python
from utils.error_handler import with_retry, ErrorCategory, ErrorSeverity

@with_retry(
    category=ErrorCategory.API,
    severity=ErrorSeverity.MEDIUM,
    fallback_on_failure=True
)
def unstable_api_call():
    # Code qui peut échouer temporairement
    response = requests.get('https://api.example.com/data')
    return response.json()
```

### 3. Exécution sécurisée

```python
from utils.error_handler import safe_execute

result = safe_execute(
    risky_function, 
    arg1, arg2,
    default_return=None,
    category=ErrorCategory.SYSTEM
)
```

## 📊 Catégories d'Erreurs

| Catégorie | Description | Exemples |
|-----------|-------------|----------|
| **NETWORK** | Erreurs réseau et connectivité | Timeout, DNS, SSL |
| **API** | Erreurs d'API externes | Rate limit, Auth, Format |
| **TRADING** | Erreurs de trading | Fonds insuffisants, Ordre rejeté |
| **DATA** | Erreurs de données | Parsing, Validation, Format |
| **SYSTEM** | Erreurs système | Mémoire, Disque, Permissions |
| **VALIDATION** | Erreurs de validation | Paramètres invalides |

## ⚡ Niveaux de Sévérité

| Niveau | Description | Action |
|--------|-------------|--------|
| **LOW** | Erreur mineure | Log seulement |
| **MEDIUM** | Erreur modérée | Log + Retry |
| **HIGH** | Erreur importante | Log + Retry + Notification |
| **CRITICAL** | Erreur critique | Log + Retry + Notification + Circuit Breaker |

## 🔄 Stratégies de Retry

### Configuration par défaut

```python
# Stratégies par catégorie
NETWORK: RetryStrategy(max_attempts=5, base_delay=2.0)
API: RetryStrategy(max_attempts=3, base_delay=1.0)
TRADING: RetryStrategy(max_attempts=2, base_delay=0.5)
DATA: RetryStrategy(max_attempts=4, base_delay=1.5)
```

### Stratégie personnalisée

```python
from utils.error_handler import RetryStrategy

custom_strategy = RetryStrategy(
    max_attempts=5,
    base_delay=1.0,
    max_delay=60.0,
    exponential_backoff=True,
    jitter=True
)

@with_retry(retry_strategy=custom_strategy)
def my_function():
    # Votre code ici
    pass
```

## 🔌 Circuit Breakers

Les circuit breakers protègent contre les cascades d'erreurs :

```python
# Vérifier si un circuit breaker est actif
if error_handler.is_circuit_breaker_active(ErrorCategory.NETWORK):
    print("Service réseau temporairement indisponible")
    return

# Réinitialiser un circuit breaker
error_handler.reset_circuit_breaker(ErrorCategory.NETWORK)
```

### Seuils par défaut

- **NETWORK**: 10 erreurs consécutives
- **API**: 5 erreurs consécutives  
- **TRADING**: 3 erreurs consécutives

## 🌐 Gestion Réseau Spécialisée

```python
from utils.network_handler import network_handler

# Requête GET avec retry automatique
response = network_handler.get('https://api.example.com/data')

# Test de connectivité
connectivity = network_handler.check_connectivity([
    'api.binance.com', 'api.coingecko.com'
])

# Statut réseau complet
status = network_handler.get_network_status()
```

### Fallbacks automatiques

Le système utilise automatiquement des URLs de fallback :

```python
fallback_urls = {
    'binance': [
        'https://api.binance.com',
        'https://api1.binance.com',
        'https://api2.binance.com'
    ]
}
```

## 💹 Gestion Trading Spécialisée

```python
from utils.trading_handler import trading_handler

# Validation d'ordre
order_data = {
    'symbol': 'BTC/USDT',
    'side': 'buy',
    'quantity': 0.001,
    'price': 50000
}

validation = trading_handler.validate_order(order_data)
if validation['valid']:
    # Exécuter l'ordre
    pass
else:
    print(f"Ordre invalide: {validation['errors']}")
```

### Protection du capital

- **Limite de perte quotidienne**: 5% par défaut
- **Taille de position max**: 10% du capital
- **Arrêt d'urgence**: Blocage de tous les nouveaux ordres
- **Limite de fréquence**: 10 ordres/minute max

```python
# Activer l'arrêt d'urgence
trading_handler.activate_emergency_stop("Volatilité excessive")

# Désactiver l'arrêt d'urgence
trading_handler.deactivate_emergency_stop()
```

## 📊 Monitoring en Temps Réel

```python
from monitoring.error_monitor import error_monitor

# Démarrer le monitoring
error_monitor.start_monitoring(interval=30)

# Dashboard de monitoring
dashboard = error_monitor.get_monitoring_dashboard()
print(f"Score de santé: {dashboard['health_metrics']['system_health_score']}")

# Rapport d'erreurs
report = error_monitor.generate_error_report(timeframe_hours=24)
```

### Métriques surveillées

- **Taux d'erreurs par heure**
- **Score de santé système (0-100)**
- **Circuit breakers actifs**
- **Connectivité réseau**
- **État d'urgence trading**

## 🔄 Stratégies de Fallback

### Enregistrer une stratégie

```python
def my_fallback_strategy(*args, **kwargs):
    # Logique de fallback
    return backup_data

error_handler.register_fallback_strategy(
    ErrorCategory.API, 
    my_fallback_strategy, 
    priority=1
)
```

### Utilisation automatique

```python
@with_retry(fallback_on_failure=True)
def api_call_with_fallback():
    # Si toutes les tentatives échouent, 
    # les fallbacks seront automatiquement exécutés
    pass
```

## 📱 Notifications et Alertes

### Configuration automatique

- **Erreurs HIGH/CRITICAL**: Notification Telegram
- **Circuit breakers**: Alerte immédiate
- **Seuils dépassés**: Alerte avec cooldown (15 min)
- **Arrêt d'urgence**: Notification critique

### Personnalisation

```python
# Les notifications sont automatiques selon la sévérité
error_handler.handle_error(
    exception, 
    category, 
    severity,
    notify=True  # Force la notification
)
```

## 🧪 Tests et Validation

### Exécuter les exemples

```python
# Exemples complets
python examples/error_handling_example.py
```

### Tests unitaires

```python
# Tests du système d'erreurs
python -m pytest tests/test_error_handling.py -v
```

## 📝 Bonnes Pratiques

### 1. Catégorisation appropriée

```python
# ✅ Bon
error_handler.handle_error(e, ErrorCategory.NETWORK, ErrorSeverity.HIGH)

# ❌ Mauvais
error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.LOW)  # Pour une erreur réseau
```

### 2. Contexte informatif

```python
# ✅ Bon
context = {
    'function': 'get_price',
    'symbol': 'BTC/USDT',
    'exchange': 'binance',
    'attempt': 2
}

# ❌ Mauvais
context = {'error': True}  # Pas assez informatif
```

### 3. Gestion des retry

```python
# ✅ Bon - Retry pour erreurs temporaires
@with_retry(category=ErrorCategory.NETWORK)
def network_call():
    pass

# ❌ Mauvais - Retry pour erreurs permanentes
@with_retry(category=ErrorCategory.VALIDATION)  # Les erreurs de validation ne se corrigent pas
def validate_input():
    pass
```

### 4. Circuit breakers

```python
# ✅ Bon - Vérifier avant opération critique
if not error_handler.is_circuit_breaker_active(ErrorCategory.TRADING):
    execute_trade()
else:
    log_warning("Trading temporairement désactivé")
```

## 🔧 Configuration Avancée

### Personnaliser les seuils

```python
# Modifier les seuils de circuit breaker
error_handler.circuit_breaker_thresholds[ErrorCategory.API] = 3

# Modifier les stratégies de retry
error_handler.default_strategies[ErrorCategory.NETWORK] = RetryStrategy(
    max_attempts=10,
    base_delay=0.5
)
```

### Monitoring personnalisé

```python
# Modifier les seuils d'alerte
error_monitor.alert_thresholds[ErrorCategory.TRADING] = {
    'hourly': 5,
    'critical_count': 1
}
```

## 🚨 Gestion des Urgences

### Arrêt d'urgence global

```python
# Activer l'arrêt d'urgence
trading_handler.activate_emergency_stop("Anomalie détectée")

# Vérifier l'état
if trading_handler.emergency_stop:
    # Arrêter toutes les opérations de trading
    pass
```

### Réinitialisation complète

```python
# Réinitialiser tous les circuit breakers
for category in ErrorCategory:
    error_handler.reset_circuit_breaker(category)

# Réinitialiser les compteurs de trading
trading_handler.reset_error_counters()
```

---

**💡 Conseil** : Utilisez toujours la catégorie et la sévérité appropriées pour une gestion d'erreurs efficace. Le système s'adapte automatiquement selon ces paramètres.
