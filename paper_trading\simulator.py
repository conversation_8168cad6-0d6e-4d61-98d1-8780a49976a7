"""
🧪 Simulateur de paper trading
Simule le trading en temps réel sans risque financier
"""

import time
import threading
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable
import pandas as pd
import numpy as np
from pathlib import Path
import sys
import json

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from config.settings import config
from utils.notifications import notifier
from monitoring.metrics import get_metrics_collector

class PaperTradingSimulator:
    """Simulateur de paper trading en temps réel"""
    
    def __init__(self, initial_capital: float = 1000, symbol: str = 'BTC/USDT'):
        self.logger = logging.getLogger(__name__)
        self.initial_capital = initial_capital
        self.symbol = symbol
        
        # État du portefeuille
        self.current_capital = initial_capital
        self.positions = {}  # symbol -> quantity
        self.open_orders = {}  # order_id -> order_info
        self.trade_history = []
        self.price_history = []
        
        # Configuration
        self.trading_fee = 0.001  # 0.1%
        self.slippage = 0.0005   # 0.05%
        self.update_interval = 60  # Secondes entre les mises à jour
        
        # État de simulation
        self.is_running = False
        self.simulation_thread = None
        self.strategy_func = None
        self.strategy_name = "PaperTrading"
        
        # Métriques
        self.metrics = get_metrics_collector(f"PaperTrading_{symbol}", "simulation", "paper")
        
        # Données de marché simulées
        self.current_price = 50000  # Prix initial BTC
        self.price_volatility = 0.02  # 2% de volatilité
        self.trend_factor = 0.0001  # Légère tendance haussière
        
        self.logger.info(f"🧪 Simulateur paper trading initialisé: {symbol}, Capital: {initial_capital}")
    
    def set_strategy(self, strategy_func: Callable, strategy_name: str = "Custom"):
        """Définit la stratégie à utiliser"""
        self.strategy_func = strategy_func
        self.strategy_name = strategy_name
        self.logger.info(f"📈 Stratégie définie: {strategy_name}")
    
    def set_real_market_data(self, use_real_data: bool = True):
        """Configure l'utilisation de données de marché réelles"""
        self.use_real_data = use_real_data
        if use_real_data:
            self.logger.info("📊 Utilisation de données de marché réelles")
        else:
            self.logger.info("🎲 Utilisation de données simulées")
    
    def generate_simulated_price(self) -> float:
        """Génère un prix simulé basé sur un mouvement brownien"""
        # Mouvement brownien géométrique simplifié
        random_change = np.random.normal(self.trend_factor, self.price_volatility)
        self.current_price *= (1 + random_change)
        
        # Éviter les prix négatifs
        self.current_price = max(self.current_price, 100)
        
        return self.current_price
    
    def get_real_market_price(self) -> Optional[float]:
        """Récupère le prix réel du marché"""
        try:
            import ccxt
            exchange = ccxt.binance()
            ticker = exchange.fetch_ticker(self.symbol)
            return float(ticker['last'])
        except Exception as e:
            self.logger.error(f"❌ Erreur récupération prix réel: {e}")
            return None
    
    def get_current_price(self) -> float:
        """Récupère le prix actuel (réel ou simulé)"""
        if hasattr(self, 'use_real_data') and self.use_real_data:
            real_price = self.get_real_market_price()
            if real_price:
                self.current_price = real_price
                return real_price
        
        return self.generate_simulated_price()
    
    def execute_trade(self, side: str, quantity: float, price: Optional[float] = None) -> bool:
        """
        Exécute un trade en paper trading
        
        Args:
            side: 'buy' ou 'sell'
            quantity: Quantité à trader
            price: Prix d'exécution (None = prix de marché)
            
        Returns:
            bool: True si le trade a été exécuté
        """
        if quantity <= 0:
            self.logger.warning("⚠️ Quantité invalide")
            return False
        
        # Utiliser le prix de marché si non spécifié
        if price is None:
            price = self.get_current_price()
        
        # Appliquer le slippage
        if side.lower() == 'buy':
            execution_price = price * (1 + self.slippage)
            trade_quantity = quantity
        else:  # sell
            execution_price = price * (1 - self.slippage)
            trade_quantity = -quantity
        
        # Calculer les coûts
        trade_value = abs(trade_quantity) * execution_price
        fees = trade_value * self.trading_fee
        total_cost = trade_value + fees
        
        # Vérifications
        if side.lower() == 'buy' and total_cost > self.current_capital:
            self.logger.warning(f"⚠️ Capital insuffisant: {total_cost:.2f} > {self.current_capital:.2f}")
            return False
        
        current_position = self.positions.get(self.symbol, 0)
        if side.lower() == 'sell' and quantity > current_position:
            self.logger.warning(f"⚠️ Position insuffisante: {quantity} > {current_position}")
            return False
        
        # Exécuter le trade
        timestamp = datetime.now()
        
        if side.lower() == 'buy':
            self.current_capital -= total_cost
            self.positions[self.symbol] = self.positions.get(self.symbol, 0) + quantity
        else:  # sell
            self.current_capital += trade_value - fees
            self.positions[self.symbol] = self.positions.get(self.symbol, 0) - quantity
        
        # Calculer le P&L (simplifié)
        pnl = 0
        if side.lower() == 'sell':
            # Estimation du P&L basée sur le prix moyen d'achat
            avg_buy_price = getattr(self, 'avg_buy_price', execution_price)
            pnl = (execution_price - avg_buy_price) * quantity - fees
        
        # Enregistrer le trade
        trade_record = {
            'timestamp': timestamp,
            'symbol': self.symbol,
            'side': side.lower(),
            'quantity': quantity,
            'price': execution_price,
            'fees': fees,
            'pnl': pnl,
            'portfolio_value': self.get_portfolio_value(),
            'strategy': self.strategy_name
        }
        
        self.trade_history.append(trade_record)
        
        # Métriques
        self.metrics.log_trade(
            self.symbol, side, quantity, execution_price, 
            'executed', None, pnl
        )
        
        # Notification
        notifier.send_trade_executed(side, quantity, execution_price, self.symbol)
        
        self.logger.info(f"✅ Trade exécuté: {side.upper()} {quantity} {self.symbol} @ {execution_price:.2f}")
        return True
    
    def get_portfolio_value(self) -> float:
        """Calcule la valeur actuelle du portefeuille"""
        total_value = self.current_capital
        
        for symbol, quantity in self.positions.items():
            if quantity != 0:
                if symbol == self.symbol:
                    total_value += quantity * self.current_price
                else:
                    # Pour d'autres symboles, utiliser le dernier prix connu
                    total_value += quantity * 50000  # Prix par défaut
        
        return total_value
    
    def get_performance_summary(self) -> Dict:
        """Génère un résumé de performance"""
        portfolio_value = self.get_portfolio_value()
        total_return = ((portfolio_value / self.initial_capital) - 1) * 100
        
        # Calculer les métriques de trading
        total_trades = len(self.trade_history)
        winning_trades = sum(1 for trade in self.trade_history if trade['pnl'] > 0)
        total_pnl = sum(trade['pnl'] for trade in self.trade_history)
        
        return {
            'initial_capital': self.initial_capital,
            'current_capital': self.current_capital,
            'portfolio_value': portfolio_value,
            'total_return_percent': total_return,
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'win_rate_percent': (winning_trades / total_trades * 100) if total_trades > 0 else 0,
            'total_pnl': total_pnl,
            'current_position': self.positions.get(self.symbol, 0),
            'current_price': self.current_price
        }
    
    def create_market_data_frame(self, lookback_periods: int = 100) -> pd.DataFrame:
        """Crée un DataFrame avec l'historique des prix pour la stratégie"""
        if len(self.price_history) < lookback_periods:
            # Générer des données historiques simulées
            timestamps = [datetime.now() - timedelta(minutes=i) for i in range(lookback_periods, 0, -1)]
            prices = []
            
            base_price = self.current_price * 0.95  # Commencer un peu plus bas
            for i in range(lookback_periods):
                random_change = np.random.normal(0.0001, 0.01)
                base_price *= (1 + random_change)
                prices.append(base_price)
            
            # Ajouter les prix réels si disponibles
            for price_record in self.price_history:
                timestamps.append(price_record['timestamp'])
                prices.append(price_record['price'])
        else:
            # Utiliser l'historique existant
            recent_history = self.price_history[-lookback_periods:]
            timestamps = [record['timestamp'] for record in recent_history]
            prices = [record['price'] for record in recent_history]
        
        # Créer le DataFrame OHLCV simplifié
        df = pd.DataFrame({
            'open': prices,
            'high': [p * 1.005 for p in prices],  # High légèrement supérieur
            'low': [p * 0.995 for p in prices],   # Low légèrement inférieur
            'close': prices,
            'volume': [np.random.randint(100, 1000) for _ in prices]
        }, index=timestamps)
        
        return df
    
    def simulation_loop(self):
        """Boucle principale de simulation"""
        self.logger.info(f"🚀 Démarrage simulation paper trading: {self.strategy_name}")
        
        # Démarrer le monitoring
        self.metrics.start_monitoring()
        
        # Notification de démarrage
        notifier.send_bot_start(
            f"PaperTrading_{self.strategy_name}", 
            self.current_capital, 
            self.current_price, 
            "simulation"
        )
        
        iteration = 0
        
        while self.is_running:
            try:
                # Mettre à jour le prix
                current_price = self.get_current_price()
                
                # Enregistrer dans l'historique
                self.price_history.append({
                    'timestamp': datetime.now(),
                    'price': current_price
                })
                
                # Limiter l'historique
                if len(self.price_history) > 1000:
                    self.price_history = self.price_history[-500:]
                
                # Créer les données de marché pour la stratégie
                market_data = self.create_market_data_frame()
                
                # Exécuter la stratégie si définie
                if self.strategy_func and len(market_data) > 50:
                    try:
                        # Adapter l'interface pour la stratégie
                        class StrategyAdapter:
                            def __init__(self, simulator):
                                self.simulator = simulator
                                self.positions = {simulator.symbol: simulator.positions.get(simulator.symbol, 0)}
                                self.current_capital = simulator.current_capital
                            
                            def execute_trade(self, symbol, side, quantity, price, timestamp, strategy_name):
                                return self.simulator.execute_trade(side, quantity, price)
                        
                        adapter = StrategyAdapter(self)
                        self.strategy_func(adapter, market_data, len(market_data) - 1, self.symbol)
                        
                    except Exception as e:
                        self.logger.error(f"❌ Erreur stratégie: {e}")
                
                # Métriques de performance
                performance = self.get_performance_summary()
                self.metrics.log_performance_metrics(
                    performance['portfolio_value'],
                    performance['total_pnl'],
                    0,  # open_orders
                    0   # drawdown
                )
                
                # Log périodique
                if iteration % 10 == 0:  # Toutes les 10 itérations
                    self.logger.info(f"📊 Prix: {current_price:.2f}, Portfolio: {performance['portfolio_value']:.2f}, "
                                   f"Return: {performance['total_return_percent']:.2f}%, Trades: {performance['total_trades']}")
                
                # Notification horaire
                if iteration % 60 == 0 and iteration > 0:  # Toutes les heures
                    notifier.send_hourly_summary(
                        performance['portfolio_value'],
                        performance['total_pnl'],
                        performance['total_return_percent'],
                        0  # open_orders
                    )
                
                iteration += 1
                time.sleep(self.update_interval)
                
            except KeyboardInterrupt:
                self.logger.info("🛑 Simulation interrompue par l'utilisateur")
                break
            except Exception as e:
                self.logger.error(f"❌ Erreur simulation: {e}")
                time.sleep(self.update_interval)
        
        # Arrêter le monitoring
        self.metrics.stop_monitoring()
        
        # Résumé final
        final_performance = self.get_performance_summary()
        self.logger.info(f"📊 Simulation terminée:")
        self.logger.info(f"   Capital final: {final_performance['current_capital']:.2f}")
        self.logger.info(f"   Valeur portfolio: {final_performance['portfolio_value']:.2f}")
        self.logger.info(f"   Rendement total: {final_performance['total_return_percent']:.2f}%")
        self.logger.info(f"   Nombre de trades: {final_performance['total_trades']}")
        
        # Notification finale
        notifier.send_telegram(
            f"🏁 <b>Simulation terminée</b>\n"
            f"📊 Stratégie: {self.strategy_name}\n"
            f"💰 Rendement: {final_performance['total_return_percent']:.2f}%\n"
            f"🔢 Trades: {final_performance['total_trades']}\n"
            f"🎯 Taux réussite: {final_performance['win_rate_percent']:.1f}%"
        )
    
    def start_simulation(self):
        """Démarre la simulation"""
        if self.is_running:
            self.logger.warning("⚠️ Simulation déjà en cours")
            return False
        
        if not self.strategy_func:
            self.logger.error("❌ Aucune stratégie définie")
            return False
        
        self.is_running = True
        self.simulation_thread = threading.Thread(target=self.simulation_loop, daemon=True)
        self.simulation_thread.start()
        
        self.logger.info("✅ Simulation démarrée")
        return True
    
    def stop_simulation(self):
        """Arrête la simulation"""
        if not self.is_running:
            self.logger.warning("⚠️ Aucune simulation en cours")
            return False
        
        self.is_running = False
        
        if self.simulation_thread:
            self.simulation_thread.join(timeout=10)
        
        self.logger.info("🛑 Simulation arrêtée")
        return True
    
    def save_results(self, filepath: Optional[str] = None) -> str:
        """Sauvegarde les résultats de la simulation"""
        if not filepath:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filepath = f"assets/results/paper_trading/simulation_{self.strategy_name}_{timestamp}.json"
        
        # Créer le répertoire si nécessaire
        Path(filepath).parent.mkdir(parents=True, exist_ok=True)
        
        # Préparer les données
        results = {
            'simulation_info': {
                'strategy_name': self.strategy_name,
                'symbol': self.symbol,
                'initial_capital': self.initial_capital,
                'trading_fee': self.trading_fee,
                'slippage': self.slippage,
                'start_time': self.trade_history[0]['timestamp'].isoformat() if self.trade_history else None,
                'end_time': datetime.now().isoformat()
            },
            'performance': self.get_performance_summary(),
            'trades': [
                {
                    **trade,
                    'timestamp': trade['timestamp'].isoformat()
                }
                for trade in self.trade_history
            ],
            'price_history': [
                {
                    **price_record,
                    'timestamp': price_record['timestamp'].isoformat()
                }
                for price_record in self.price_history[-100:]  # Garder les 100 derniers
            ]
        }
        
        # Sauvegarder
        with open(filepath, 'w') as f:
            json.dump(results, f, indent=2)
        
        self.logger.info(f"💾 Résultats sauvegardés: {filepath}")
        return filepath

# Instance globale
paper_trading_simulator = PaperTradingSimulator()
