"""
🧪 Tests unitaires pour le gestionnaire de portefeuille
"""

import pytest
import asyncio
from decimal import Decimal
from unittest.mock import Mock, patch
from datetime import datetime, timedelta
import sys
from pathlib import Path

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent.parent))

from risk_management.portfolio_manager import PortfolioManager, Position, PositionType
from utils.error_handler import ErrorCategory, ErrorSeverity

@pytest.mark.unit
class TestPortfolioManager:
    """Tests pour le gestionnaire de portefeuille"""
    
    def setup_method(self):
        """Configuration avant chaque test"""
        self.portfolio_manager = PortfolioManager()
        self.portfolio_manager.total_balance = 10000.0
        self.portfolio_manager.available_balance = 8000.0
    
    def test_initialization(self):
        """Test de l'initialisation"""
        pm = PortfolioManager()
        assert pm.total_balance == 0.0
        assert pm.available_balance == 0.0
        assert len(pm.positions) == 0
        assert pm.max_position_size_pct == 10.0
        assert pm.max_daily_loss_pct == 5.0
    
    def test_get_portfolio_value(self):
        """Test du calcul de la valeur du portefeuille"""
        # Test avec balance seulement
        value = self.portfolio_manager.get_portfolio_value()
        assert value == 10000.0
        
        # Test avec positions
        position = Position(
            id="test_pos_1",
            symbol="ETH/USDC",
            position_type=PositionType.LONG,
            size=1.0,
            entry_price=2000.0,
            current_price=2100.0,
            timestamp=datetime.now()
        )
        self.portfolio_manager.positions["test_pos_1"] = position
        
        # Valeur = balance + valeur des positions
        expected_value = 10000.0 + (2100.0 * 1.0)
        assert self.portfolio_manager.get_portfolio_value() == expected_value
    
    def test_get_available_balance(self):
        """Test de la balance disponible"""
        assert self.portfolio_manager.get_available_balance() == 8000.0
        
        # Test avec réservation
        self.portfolio_manager.reserved_balance = 1000.0
        assert self.portfolio_manager.get_available_balance() == 7000.0
    
    def test_calculate_position_size(self):
        """Test du calcul de la taille de position"""
        # Test avec pourcentage
        size = self.portfolio_manager.calculate_position_size(
            symbol="ETH/USDC",
            price=2000.0,
            risk_percentage=5.0
        )
        expected_size = (10000.0 * 0.05) / 2000.0  # 0.25 ETH
        assert abs(size - expected_size) < 0.001
        
        # Test avec montant fixe
        size = self.portfolio_manager.calculate_position_size(
            symbol="ETH/USDC",
            price=2000.0,
            amount_usd=1000.0
        )
        expected_size = 1000.0 / 2000.0  # 0.5 ETH
        assert abs(size - expected_size) < 0.001
    
    def test_check_risk_limits(self):
        """Test de la vérification des limites de risque"""
        # Test position normale
        assert self.portfolio_manager.check_risk_limits(
            symbol="ETH/USDC",
            size=0.5,
            price=2000.0
        ) == True
        
        # Test position trop grande
        assert self.portfolio_manager.check_risk_limits(
            symbol="ETH/USDC",
            size=10.0,  # 20000 USD > 10% de 10000
            price=2000.0
        ) == False
        
        # Test avec balance insuffisante
        assert self.portfolio_manager.check_risk_limits(
            symbol="ETH/USDC",
            size=5.0,  # 10000 USD > balance disponible
            price=2000.0
        ) == False
    
    def test_open_position(self):
        """Test d'ouverture de position"""
        position_id = self.portfolio_manager.open_position(
            symbol="ETH/USDC",
            position_type=PositionType.LONG,
            size=1.0,
            entry_price=2000.0
        )
        
        assert position_id is not None
        assert position_id in self.portfolio_manager.positions
        
        position = self.portfolio_manager.positions[position_id]
        assert position.symbol == "ETH/USDC"
        assert position.position_type == PositionType.LONG
        assert position.size == 1.0
        assert position.entry_price == 2000.0
        assert position.current_price == 2000.0
        
        # Vérifier que la balance a été mise à jour
        assert self.portfolio_manager.available_balance == 6000.0  # 8000 - 2000
    
    def test_close_position(self):
        """Test de fermeture de position"""
        # Ouvrir une position d'abord
        position_id = self.portfolio_manager.open_position(
            symbol="ETH/USDC",
            position_type=PositionType.LONG,
            size=1.0,
            entry_price=2000.0
        )
        
        # Fermer la position avec profit
        pnl = self.portfolio_manager.close_position(position_id, exit_price=2100.0)
        
        assert pnl == 100.0  # (2100 - 2000) * 1.0
        assert position_id not in self.portfolio_manager.positions
        
        # Vérifier que la balance a été mise à jour
        expected_balance = 8000.0 + 100.0  # Balance initiale + profit
        assert self.portfolio_manager.available_balance == expected_balance
    
    def test_update_position_price(self):
        """Test de mise à jour du prix d'une position"""
        # Ouvrir une position
        position_id = self.portfolio_manager.open_position(
            symbol="ETH/USDC",
            position_type=PositionType.LONG,
            size=1.0,
            entry_price=2000.0
        )
        
        # Mettre à jour le prix
        self.portfolio_manager.update_position_price(position_id, 2150.0)
        
        position = self.portfolio_manager.positions[position_id]
        assert position.current_price == 2150.0
        assert position.unrealized_pnl == 150.0  # (2150 - 2000) * 1.0
    
    def test_get_position_pnl(self):
        """Test du calcul du P&L d'une position"""
        # Position longue avec profit
        position = Position(
            id="test_pos_1",
            symbol="ETH/USDC",
            position_type=PositionType.LONG,
            size=1.0,
            entry_price=2000.0,
            current_price=2100.0,
            timestamp=datetime.now()
        )
        
        pnl = self.portfolio_manager.get_position_pnl(position)
        assert pnl == 100.0
        
        # Position courte avec profit
        position.position_type = PositionType.SHORT
        position.current_price = 1900.0
        
        pnl = self.portfolio_manager.get_position_pnl(position)
        assert pnl == 100.0  # (2000 - 1900) * 1.0
    
    def test_get_total_pnl(self):
        """Test du calcul du P&L total"""
        # Ajouter quelques positions
        position1 = Position(
            id="pos1",
            symbol="ETH/USDC",
            position_type=PositionType.LONG,
            size=1.0,
            entry_price=2000.0,
            current_price=2100.0,
            timestamp=datetime.now()
        )
        
        position2 = Position(
            id="pos2",
            symbol="BTC/USDC",
            position_type=PositionType.SHORT,
            size=0.1,
            entry_price=50000.0,
            current_price=49000.0,
            timestamp=datetime.now()
        )
        
        self.portfolio_manager.positions["pos1"] = position1
        self.portfolio_manager.positions["pos2"] = position2
        
        total_pnl = self.portfolio_manager.get_total_pnl()
        expected_pnl = 100.0 + 100.0  # Profit de chaque position
        assert total_pnl == expected_pnl
    
    def test_get_daily_pnl(self):
        """Test du calcul du P&L quotidien"""
        # Ajouter des trades d'aujourd'hui
        today = datetime.now().date()
        self.portfolio_manager.daily_trades[today] = [
            {'pnl': 50.0, 'timestamp': datetime.now()},
            {'pnl': -20.0, 'timestamp': datetime.now()},
            {'pnl': 30.0, 'timestamp': datetime.now()}
        ]
        
        daily_pnl = self.portfolio_manager.get_daily_pnl()
        assert daily_pnl == 60.0  # 50 - 20 + 30
    
    def test_check_daily_loss_limit(self):
        """Test de la vérification de la limite de perte quotidienne"""
        # Test sans perte
        assert self.portfolio_manager.check_daily_loss_limit() == True
        
        # Ajouter des pertes importantes
        today = datetime.now().date()
        self.portfolio_manager.daily_trades[today] = [
            {'pnl': -600.0, 'timestamp': datetime.now()}  # 6% de perte sur 10000
        ]
        
        # Dépasse la limite de 5%
        assert self.portfolio_manager.check_daily_loss_limit() == False
    
    def test_get_portfolio_statistics(self):
        """Test des statistiques du portefeuille"""
        # Ajouter quelques données
        position = Position(
            id="pos1",
            symbol="ETH/USDC",
            position_type=PositionType.LONG,
            size=1.0,
            entry_price=2000.0,
            current_price=2100.0,
            timestamp=datetime.now()
        )
        self.portfolio_manager.positions["pos1"] = position
        
        today = datetime.now().date()
        self.portfolio_manager.daily_trades[today] = [
            {'pnl': 50.0, 'timestamp': datetime.now()},
            {'pnl': 30.0, 'timestamp': datetime.now()}
        ]
        
        stats = self.portfolio_manager.get_portfolio_statistics()
        
        assert stats['total_balance'] == 10000.0
        assert stats['available_balance'] == 8000.0
        assert stats['total_positions'] == 1
        assert stats['total_unrealized_pnl'] == 100.0
        assert stats['daily_pnl'] == 80.0
        assert 'portfolio_value' in stats
        assert 'daily_trades_count' in stats
    
    def test_reserve_balance(self):
        """Test de réservation de balance"""
        # Réserver de la balance
        success = self.portfolio_manager.reserve_balance(1000.0)
        assert success == True
        assert self.portfolio_manager.reserved_balance == 1000.0
        assert self.portfolio_manager.get_available_balance() == 7000.0
        
        # Essayer de réserver plus que disponible
        success = self.portfolio_manager.reserve_balance(8000.0)
        assert success == False
        assert self.portfolio_manager.reserved_balance == 1000.0  # Inchangé
    
    def test_release_balance(self):
        """Test de libération de balance"""
        # Réserver puis libérer
        self.portfolio_manager.reserve_balance(1000.0)
        self.portfolio_manager.release_balance(500.0)
        
        assert self.portfolio_manager.reserved_balance == 500.0
        assert self.portfolio_manager.get_available_balance() == 7500.0
        
        # Libérer plus que réservé
        self.portfolio_manager.release_balance(1000.0)
        assert self.portfolio_manager.reserved_balance == 0.0
    
    @pytest.mark.asyncio
    async def test_async_operations(self):
        """Test des opérations asynchrones"""
        # Test de mise à jour asynchrone des prix
        position_id = self.portfolio_manager.open_position(
            symbol="ETH/USDC",
            position_type=PositionType.LONG,
            size=1.0,
            entry_price=2000.0
        )
        
        # Simuler une mise à jour de prix asynchrone
        async def update_price():
            await asyncio.sleep(0.1)
            self.portfolio_manager.update_position_price(position_id, 2200.0)
        
        await update_price()
        
        position = self.portfolio_manager.positions[position_id]
        assert position.current_price == 2200.0
        assert position.unrealized_pnl == 200.0
    
    def test_error_handling(self):
        """Test de la gestion d'erreurs"""
        # Test avec position inexistante
        pnl = self.portfolio_manager.close_position("inexistant", 2000.0)
        assert pnl is None
        
        # Test avec prix négatif
        position_id = self.portfolio_manager.open_position(
            symbol="ETH/USDC",
            position_type=PositionType.LONG,
            size=1.0,
            entry_price=-100.0  # Prix invalide
        )
        assert position_id is None
        
        # Test avec taille négative
        position_id = self.portfolio_manager.open_position(
            symbol="ETH/USDC",
            position_type=PositionType.LONG,
            size=-1.0,  # Taille invalide
            entry_price=2000.0
        )
        assert position_id is None
    
    def test_position_limits(self):
        """Test des limites de position"""
        # Test du nombre maximum de positions
        original_max = self.portfolio_manager.max_positions
        self.portfolio_manager.max_positions = 2
        
        # Ouvrir 2 positions (limite)
        pos1 = self.portfolio_manager.open_position("ETH/USDC", PositionType.LONG, 0.5, 2000.0)
        pos2 = self.portfolio_manager.open_position("BTC/USDC", PositionType.LONG, 0.01, 50000.0)
        
        assert pos1 is not None
        assert pos2 is not None
        
        # Essayer d'ouvrir une 3ème position (doit échouer)
        pos3 = self.portfolio_manager.open_position("LINK/USDC", PositionType.LONG, 10.0, 15.0)
        assert pos3 is None
        
        # Restaurer la limite originale
        self.portfolio_manager.max_positions = original_max
    
    def test_correlation_limits(self):
        """Test des limites de corrélation"""
        # Ouvrir plusieurs positions sur le même symbole
        pos1 = self.portfolio_manager.open_position("ETH/USDC", PositionType.LONG, 0.5, 2000.0)
        pos2 = self.portfolio_manager.open_position("ETH/USDC", PositionType.LONG, 0.5, 2000.0)
        
        assert pos1 is not None
        assert pos2 is not None
        
        # Vérifier que les positions sont bien distinctes
        assert pos1 != pos2
        assert len(self.portfolio_manager.positions) == 2
        
        # Calculer l'exposition totale sur ETH/USDC
        total_exposure = sum(
            pos.size * pos.current_price 
            for pos in self.portfolio_manager.positions.values()
            if pos.symbol == "ETH/USDC"
        )
        assert total_exposure == 2000.0  # 0.5 + 0.5 = 1.0 ETH * 2000 USD
