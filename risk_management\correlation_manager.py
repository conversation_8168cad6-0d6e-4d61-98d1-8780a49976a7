"""
📊 Gestionnaire de corrélations et diversification
Analyse des corrélations entre actifs et optimisation de la diversification
"""

import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from pathlib import Path
import sys

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from utils.notifications import notifier
from utils.error_handler import error_handler, ErrorCategory, ErrorSeverity

@dataclass
class CorrelationAlert:
    """Alerte de corrélation"""
    symbol1: str
    symbol2: str
    correlation: float
    threshold: float
    alert_time: datetime
    severity: str

class CorrelationManager:
    """Gestionnaire de corrélations et diversification"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Historique des prix pour calcul de corrélations
        self.price_data: Dict[str, List[Tuple[datetime, float]]] = {}
        
        # Matrice de corrélations
        self.correlation_matrix: Optional[pd.DataFrame] = None
        self.last_correlation_update: Optional[datetime] = None
        
        # Seuils d'alerte
        self.high_correlation_threshold = 0.8    # Corrélation élevée
        self.very_high_correlation_threshold = 0.9  # Corrélation très élevée
        
        # Alertes de corrélation
        self.correlation_alerts: List[CorrelationAlert] = []
        
        # Configuration de diversification
        self.max_sector_allocation = 0.4         # 40% max par secteur
        self.max_correlated_allocation = 0.3     # 30% max en actifs corrélés
        
        # Secteurs des cryptomonnaies (simplification)
        self.crypto_sectors = {
            'BTC': 'Store of Value',
            'ETH': 'Smart Contracts',
            'BNB': 'Exchange Token',
            'ADA': 'Smart Contracts',
            'SOL': 'Smart Contracts',
            'DOT': 'Interoperability',
            'LINK': 'Oracle',
            'UNI': 'DeFi',
            'AAVE': 'DeFi',
            'SUSHI': 'DeFi',
            'MATIC': 'Layer 2',
            'AVAX': 'Smart Contracts',
            'ATOM': 'Interoperability',
            'XRP': 'Payments',
            'LTC': 'Payments'
        }
        
        self.logger.info("📊 Gestionnaire de corrélations initialisé")
    
    def add_price_data(self, symbol: str, price: float, timestamp: Optional[datetime] = None):
        """Ajoute des données de prix pour un symbole"""
        if timestamp is None:
            timestamp = datetime.now()
        
        if symbol not in self.price_data:
            self.price_data[symbol] = []
        
        self.price_data[symbol].append((timestamp, price))
        
        # Limiter l'historique (garder 500 points)
        if len(self.price_data[symbol]) > 500:
            self.price_data[symbol] = self.price_data[symbol][-250:]
    
    def calculate_correlation_matrix(self, period_days: int = 30, min_data_points: int = 20) -> pd.DataFrame:
        """
        Calcule la matrice de corrélations
        
        Args:
            period_days: Période en jours pour le calcul
            min_data_points: Nombre minimum de points de données
            
        Returns:
            DataFrame avec la matrice de corrélations
        """
        try:
            # Filtrer les symboles avec suffisamment de données
            valid_symbols = []
            cutoff_time = datetime.now() - timedelta(days=period_days)
            
            for symbol, data in self.price_data.items():
                recent_data = [(t, p) for t, p in data if t >= cutoff_time]
                if len(recent_data) >= min_data_points:
                    valid_symbols.append(symbol)
            
            if len(valid_symbols) < 2:
                self.logger.warning("⚠️ Pas assez de données pour calculer les corrélations")
                return pd.DataFrame()
            
            # Créer un DataFrame avec les rendements
            returns_data = {}
            
            for symbol in valid_symbols:
                recent_data = [(t, p) for t, p in self.price_data[symbol] if t >= cutoff_time]
                prices = [p for _, p in sorted(recent_data)]
                
                # Calculer les rendements
                returns = []
                for i in range(1, len(prices)):
                    ret = (prices[i] - prices[i-1]) / prices[i-1]
                    returns.append(ret)
                
                returns_data[symbol] = returns
            
            # Aligner les longueurs (prendre la plus petite)
            min_length = min(len(returns) for returns in returns_data.values())
            for symbol in returns_data:
                returns_data[symbol] = returns_data[symbol][-min_length:]
            
            # Créer le DataFrame et calculer les corrélations
            df_returns = pd.DataFrame(returns_data)
            correlation_matrix = df_returns.corr()
            
            self.correlation_matrix = correlation_matrix
            self.last_correlation_update = datetime.now()
            
            self.logger.info(f"📊 Matrice de corrélations calculée pour {len(valid_symbols)} symboles")
            return correlation_matrix
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.DATA, ErrorSeverity.MEDIUM, {
                'function': 'calculate_correlation_matrix',
                'symbols_count': len(self.price_data)
            })
            return pd.DataFrame()
    
    def get_high_correlations(self, threshold: float = None) -> List[Tuple[str, str, float]]:
        """
        Retourne les paires d'actifs avec corrélation élevée
        
        Returns:
            Liste de tuples (symbol1, symbol2, correlation)
        """
        if self.correlation_matrix is None or self.correlation_matrix.empty:
            self.calculate_correlation_matrix()
        
        if self.correlation_matrix is None or self.correlation_matrix.empty:
            return []
        
        threshold = threshold or self.high_correlation_threshold
        high_correlations = []
        
        # Parcourir la matrice triangulaire supérieure
        for i in range(len(self.correlation_matrix.columns)):
            for j in range(i + 1, len(self.correlation_matrix.columns)):
                symbol1 = self.correlation_matrix.columns[i]
                symbol2 = self.correlation_matrix.columns[j]
                correlation = self.correlation_matrix.iloc[i, j]
                
                if abs(correlation) >= threshold:
                    high_correlations.append((symbol1, symbol2, correlation))
        
        # Trier par corrélation décroissante
        high_correlations.sort(key=lambda x: abs(x[2]), reverse=True)
        
        return high_correlations
    
    def check_correlation_alerts(self, portfolio_positions: Dict[str, float]):
        """
        Vérifie les alertes de corrélation pour le portefeuille
        
        Args:
            portfolio_positions: Dict {symbol: weight} des positions du portefeuille
        """
        if not portfolio_positions or len(portfolio_positions) < 2:
            return
        
        # Recalculer les corrélations si nécessaire
        if (self.correlation_matrix is None or 
            self.last_correlation_update is None or 
            datetime.now() - self.last_correlation_update > timedelta(hours=6)):
            self.calculate_correlation_matrix()
        
        if self.correlation_matrix is None or self.correlation_matrix.empty:
            return
        
        # Vérifier les corrélations entre positions du portefeuille
        portfolio_symbols = list(portfolio_positions.keys())
        new_alerts = []
        
        for i, symbol1 in enumerate(portfolio_symbols):
            for symbol2 in portfolio_symbols[i+1:]:
                if symbol1 in self.correlation_matrix.index and symbol2 in self.correlation_matrix.columns:
                    correlation = self.correlation_matrix.loc[symbol1, symbol2]
                    
                    # Vérifier les seuils
                    if abs(correlation) >= self.very_high_correlation_threshold:
                        severity = "CRITICAL"
                        threshold = self.very_high_correlation_threshold
                    elif abs(correlation) >= self.high_correlation_threshold:
                        severity = "HIGH"
                        threshold = self.high_correlation_threshold
                    else:
                        continue
                    
                    # Créer l'alerte
                    alert = CorrelationAlert(
                        symbol1=symbol1,
                        symbol2=symbol2,
                        correlation=correlation,
                        threshold=threshold,
                        alert_time=datetime.now(),
                        severity=severity
                    )
                    
                    new_alerts.append(alert)
                    self.correlation_alerts.append(alert)
        
        # Envoyer les notifications pour les nouvelles alertes
        for alert in new_alerts:
            self._send_correlation_alert(alert, portfolio_positions)
    
    def _send_correlation_alert(self, alert: CorrelationAlert, portfolio_positions: Dict[str, float]):
        """Envoie une notification d'alerte de corrélation"""
        weight1 = portfolio_positions.get(alert.symbol1, 0) * 100
        weight2 = portfolio_positions.get(alert.symbol2, 0) * 100
        combined_weight = weight1 + weight2
        
        severity_emoji = "🚨" if alert.severity == "CRITICAL" else "⚠️"
        
        message = (
            f"{severity_emoji} <b>ALERTE CORRÉLATION</b>\n"
            f"📊 {alert.symbol1} ↔️ {alert.symbol2}\n"
            f"📈 Corrélation: {alert.correlation:.2f}\n"
            f"⚖️ Poids combiné: {combined_weight:.1f}%\n"
            f"💡 Risque de concentration élevé"
        )
        
        if alert.severity == "CRITICAL":
            message += "\n🔴 Action recommandée: Réduire l'exposition"
        
        notifier.send_telegram(message)
        self.logger.warning(f"⚠️ Alerte corrélation: {alert.symbol1}-{alert.symbol2} ({alert.correlation:.2f})")
    
    def calculate_diversification_score(self, portfolio_positions: Dict[str, float]) -> float:
        """
        Calcule un score de diversification (0-100)
        
        Args:
            portfolio_positions: Dict {symbol: weight} des positions
            
        Returns:
            Score de diversification (100 = parfaitement diversifié)
        """
        if not portfolio_positions:
            return 100
        
        score = 100
        
        # Pénalité pour concentration par actif
        max_position = max(portfolio_positions.values()) if portfolio_positions else 0
        if max_position > 0.2:  # Plus de 20% dans un actif
            score -= min(30, (max_position - 0.2) * 150)  # Pénalité progressive
        
        # Pénalité pour concentration par secteur
        sector_weights = self._calculate_sector_weights(portfolio_positions)
        max_sector_weight = max(sector_weights.values()) if sector_weights else 0
        if max_sector_weight > self.max_sector_allocation:
            score -= min(25, (max_sector_weight - self.max_sector_allocation) * 100)
        
        # Pénalité pour corrélations élevées
        correlation_penalty = self._calculate_correlation_penalty(portfolio_positions)
        score -= min(25, correlation_penalty)
        
        # Pénalité pour nombre insuffisant d'actifs
        num_positions = len(portfolio_positions)
        if num_positions < 3:
            score -= (3 - num_positions) * 10
        
        return max(0, score)
    
    def _calculate_sector_weights(self, portfolio_positions: Dict[str, float]) -> Dict[str, float]:
        """Calcule les poids par secteur"""
        sector_weights = {}
        
        for symbol, weight in portfolio_positions.items():
            # Extraire le symbole de base (ex: BTC/USDT -> BTC)
            base_symbol = symbol.split('/')[0] if '/' in symbol else symbol
            sector = self.crypto_sectors.get(base_symbol, 'Other')
            
            sector_weights[sector] = sector_weights.get(sector, 0) + weight
        
        return sector_weights
    
    def _calculate_correlation_penalty(self, portfolio_positions: Dict[str, float]) -> float:
        """Calcule la pénalité due aux corrélations élevées"""
        if self.correlation_matrix is None or len(portfolio_positions) < 2:
            return 0
        
        penalty = 0
        portfolio_symbols = list(portfolio_positions.keys())
        
        for i, symbol1 in enumerate(portfolio_symbols):
            for symbol2 in portfolio_symbols[i+1:]:
                if symbol1 in self.correlation_matrix.index and symbol2 in self.correlation_matrix.columns:
                    correlation = abs(self.correlation_matrix.loc[symbol1, symbol2])
                    weight1 = portfolio_positions[symbol1]
                    weight2 = portfolio_positions[symbol2]
                    
                    # Pénalité basée sur la corrélation et les poids
                    if correlation > self.high_correlation_threshold:
                        combined_weight = weight1 + weight2
                        penalty += (correlation - self.high_correlation_threshold) * combined_weight * 50
        
        return penalty
    
    def suggest_diversification_improvements(self, portfolio_positions: Dict[str, float]) -> List[str]:
        """
        Suggère des améliorations pour la diversification
        
        Returns:
            Liste de suggestions
        """
        suggestions = []
        
        if not portfolio_positions:
            return ["Aucune position dans le portefeuille"]
        
        # Vérifier la concentration par actif
        max_position = max(portfolio_positions.values())
        if max_position > 0.25:
            max_symbol = max(portfolio_positions, key=portfolio_positions.get)
            suggestions.append(f"Réduire l'exposition à {max_symbol} ({max_position*100:.1f}% du portefeuille)")
        
        # Vérifier la concentration par secteur
        sector_weights = self._calculate_sector_weights(portfolio_positions)
        for sector, weight in sector_weights.items():
            if weight > self.max_sector_allocation:
                suggestions.append(f"Réduire l'exposition au secteur {sector} ({weight*100:.1f}%)")
        
        # Vérifier les corrélations élevées
        high_correlations = self.get_high_correlations()
        for symbol1, symbol2, correlation in high_correlations:
            if symbol1 in portfolio_positions and symbol2 in portfolio_positions:
                suggestions.append(f"Corrélation élevée entre {symbol1} et {symbol2} ({correlation:.2f})")
        
        # Suggérer plus de diversification
        if len(portfolio_positions) < 5:
            suggestions.append("Considérer l'ajout de plus d'actifs pour améliorer la diversification")
        
        # Suggérer des secteurs sous-représentés
        represented_sectors = set(sector_weights.keys())
        all_sectors = set(self.crypto_sectors.values())
        missing_sectors = all_sectors - represented_sectors
        
        if missing_sectors and len(missing_sectors) <= 3:
            suggestions.append(f"Considérer l'exposition aux secteurs: {', '.join(missing_sectors)}")
        
        return suggestions if suggestions else ["Portefeuille bien diversifié"]
    
    def get_correlation_report(self) -> Dict[str, Any]:
        """Génère un rapport complet sur les corrélations"""
        if self.correlation_matrix is None:
            self.calculate_correlation_matrix()
        
        high_correlations = self.get_high_correlations()
        
        return {
            'last_update': self.last_correlation_update.isoformat() if self.last_correlation_update else None,
            'symbols_analyzed': len(self.price_data),
            'correlation_matrix_size': len(self.correlation_matrix) if self.correlation_matrix is not None else 0,
            'high_correlations_count': len(high_correlations),
            'high_correlations': [
                {
                    'symbol1': symbol1,
                    'symbol2': symbol2,
                    'correlation': round(correlation, 3)
                }
                for symbol1, symbol2, correlation in high_correlations[:10]  # Top 10
            ],
            'recent_alerts': len([a for a in self.correlation_alerts 
                                if datetime.now() - a.alert_time < timedelta(hours=24)]),
            'thresholds': {
                'high_correlation': self.high_correlation_threshold,
                'very_high_correlation': self.very_high_correlation_threshold
            }
        }
    
    def cleanup_old_alerts(self, max_age_hours: int = 24):
        """Nettoie les anciennes alertes"""
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        
        old_count = len(self.correlation_alerts)
        self.correlation_alerts = [
            alert for alert in self.correlation_alerts 
            if alert.alert_time >= cutoff_time
        ]
        
        cleaned_count = old_count - len(self.correlation_alerts)
        if cleaned_count > 0:
            self.logger.info(f"🧹 {cleaned_count} anciennes alertes de corrélation supprimées")

# Instance globale
correlation_manager = CorrelationManager()
