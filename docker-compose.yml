# 🤖 BotCrypto - Docker Compose
# Environnement de développement complet

version: '3.8'

services:
  # ===== APPLICATION PRINCIPALE =====
  botcrypto:
    build:
      context: .
      target: development
      dockerfile: Dockerfile
    container_name: botcrypto-app
    restart: unless-stopped
    ports:
      - "8080:8080"  # Dashboard web
      - "9090:9090"  # Métriques Prometheus
    environment:
      - ENVIRONMENT=development
      - DEBUG=true
      - DATABASE_URL=postgresql://botcrypto:${DB_PASSWORD:-botcrypto123}@postgres:5432/botcrypto
      - REDIS_URL=redis://redis:6379/0
      - WEB3_PROVIDER_URL=${WEB3_PROVIDER_URL:-https://mainnet.infura.io/v3/YOUR_KEY}
      - PRIVATE_KEY=${PRIVATE_KEY:-your_private_key_here}
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN:-}
      - TELEGRAM_CHAT_ID=${TELEGRAM_CHAT_ID:-}
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
      - ./config:/app/config
      - .:/app  # Mount source code for development
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - botcrypto-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # ===== BASE DE DONNÉES =====
  postgres:
    image: postgres:15-alpine
    container_name: botcrypto-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: botcrypto
      POSTGRES_USER: botcrypto
      POSTGRES_PASSWORD: ${DB_PASSWORD:-botcrypto123}
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "5432:5432"
    networks:
      - botcrypto-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U botcrypto -d botcrypto"]
      interval: 10s
      timeout: 5s
      retries: 5

  # ===== CACHE REDIS =====
  redis:
    image: redis:7-alpine
    container_name: botcrypto-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis123}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - botcrypto-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # ===== MONITORING =====
  prometheus:
    image: prom/prometheus:latest
    container_name: botcrypto-prometheus
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    ports:
      - "9091:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - botcrypto-network
    depends_on:
      - botcrypto

  grafana:
    image: grafana/grafana:latest
    container_name: botcrypto-grafana
    restart: unless-stopped
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
      - GF_USERS_ALLOW_SIGN_UP=false
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards:ro
    networks:
      - botcrypto-network
    depends_on:
      - prometheus

  # ===== REVERSE PROXY =====
  nginx:
    image: nginx:alpine
    container_name: botcrypto-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./nginx/logs:/var/log/nginx
    depends_on:
      - botcrypto
    networks:
      - botcrypto-network

  # ===== OUTILS DE DÉVELOPPEMENT =====
  
  # Adminer pour la gestion de base de données
  adminer:
    image: adminer:latest
    container_name: botcrypto-adminer
    restart: unless-stopped
    ports:
      - "8081:8080"
    environment:
      - ADMINER_DEFAULT_SERVER=postgres
    networks:
      - botcrypto-network
    depends_on:
      - postgres

  # Redis Commander pour la gestion Redis
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: botcrypto-redis-commander
    restart: unless-stopped
    environment:
      - REDIS_HOSTS=local:redis:6379:0:${REDIS_PASSWORD:-redis123}
    ports:
      - "8082:8081"
    networks:
      - botcrypto-network
    depends_on:
      - redis

  # ===== TESTS =====
  test-runner:
    build:
      context: .
      target: development
    container_name: botcrypto-tests
    environment:
      - ENVIRONMENT=test
      - DATABASE_URL=postgresql://botcrypto:${DB_PASSWORD:-botcrypto123}@postgres:5432/botcrypto_test
    volumes:
      - .:/app
      - ./test-results:/app/test-results
    command: python tests/run_tests.py --all
    depends_on:
      - postgres
      - redis
    networks:
      - botcrypto-network
    profiles:
      - testing

# ===== VOLUMES =====
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

# ===== RÉSEAUX =====
networks:
  botcrypto-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# ===== CONFIGURATION AVANCÉE =====

# Utilisation:
# 
# Démarrage complet:
# docker-compose up -d
#
# Démarrage avec rebuild:
# docker-compose up -d --build
#
# Démarrage des services principaux seulement:
# docker-compose up -d botcrypto postgres redis
#
# Voir les logs:
# docker-compose logs -f botcrypto
#
# Arrêt:
# docker-compose down
#
# Arrêt avec suppression des volumes:
# docker-compose down -v
#
# Exécuter les tests:
# docker-compose --profile testing up test-runner
#
# Accès aux services:
# - Dashboard: http://localhost:8080
# - Grafana: http://localhost:3000 (admin/admin123)
# - Prometheus: http://localhost:9091
# - Adminer: http://localhost:8081
# - Redis Commander: http://localhost:8082
#
# Variables d'environnement (.env):
# DB_PASSWORD=your_secure_db_password
# REDIS_PASSWORD=your_secure_redis_password
# GRAFANA_PASSWORD=your_secure_grafana_password
# WEB3_PROVIDER_URL=https://mainnet.infura.io/v3/YOUR_KEY
# PRIVATE_KEY=your_private_key
# TELEGRAM_BOT_TOKEN=your_telegram_token
# TELEGRAM_CHAT_ID=your_chat_id
