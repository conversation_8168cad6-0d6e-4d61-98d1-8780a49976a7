# 🌉 Bot d'Arbitrage Cross-Chain

## 🎯 Vue d'ensemble

Bot automatisé pour l'arbitrage de stablecoins entre différentes blockchains (Ethereum, BSC, Polygon, Arbitrum). Détecte et exploite les écarts de prix pour générer des profits avec une gestion avancée des risques.

## 🏗️ Architecture

```
bots/cross_chain_arbitrage/
├── __init__.py                      # Module principal
├── chain_connector.py               # Interface multi-chaînes
├── arbitrage_detector.py            # Détection d'opportunités
├── arbitrage_executor.py            # Exécution des trades
├── cross_chain_arbitrage_bot.py     # Bot principal intégré
└── README.md                        # Cette documentation
```

## 🚀 Fonctionnalités

### Détection d'Opportunités
- **Scan multi-chaînes** en temps réel
- **Calcul de rentabilité** après frais
- **Scoring de confiance** et d'urgence
- **Filtrage intelligent** des opportunités

### Exécution Automatisée
- **Achat automatique** sur la chaîne source
- **Bridge cross-chain** optimisé
- **Vente automatique** sur la chaîne destination
- **Gestion des timeouts** et erreurs

### Gestion des Risques
- **Position sizing** intelligent
- **Limites de capital** par trade
- **Stop-loss** et limites quotidiennes
- **Monitoring** des exécutions

### Support Multi-Chaînes
- **Ethereum** (Uniswap, SushiSwap)
- **BSC** (PancakeSwap, Biswap)
- **Polygon** (QuickSwap, SushiSwap)
- **Arbitrum** (Uniswap v3, Camelot)

## 💰 Stablecoins Supportés

| Token | Ethereum | BSC | Polygon | Arbitrum |
|-------|----------|-----|---------|----------|
| **USDT** | ✅ | ✅ | ✅ | ✅ |
| **USDC** | ✅ | ✅ | ✅ | ✅ |
| **DAI** | ✅ | ✅ | ✅ | ✅ |
| **BUSD** | ✅ | ✅ | ❌ | ❌ |

## 🛠️ Installation et Configuration

### 1. Configuration de Base

```python
from bots.cross_chain_arbitrage.cross_chain_arbitrage_bot import CrossChainArbitrageBot, BotConfig

config = BotConfig(
    min_profit_usd=20.0,              # Profit minimum par trade
    min_roi_percentage=1.0,           # ROI minimum (%)
    max_concurrent_trades=2,          # Trades simultanés max
    max_capital_per_trade=5000.0,     # Capital max par trade
    monitored_chains=['ethereum', 'bsc', 'polygon'],
    monitored_tokens=['USDT', 'USDC', 'DAI'],
    scan_interval_seconds=30,         # Fréquence de scan
    enable_notifications=True
)

bot = CrossChainArbitrageBot(config)
await bot.start()
```

### 2. Configuration Avancée

```json
{
  "min_profit_usd": 25.0,
  "min_roi_percentage": 1.5,
  "max_concurrent_trades": 3,
  "max_capital_per_trade": 10000.0,
  "monitored_chains": ["ethereum", "bsc", "polygon", "arbitrum"],
  "monitored_tokens": ["USDT", "USDC", "DAI"],
  "scan_interval_seconds": 20,
  "opportunity_timeout_seconds": 300,
  "enable_notifications": true,
  "notification_min_profit": 50.0,
  "emergency_stop_loss_percentage": 5.0,
  "max_daily_loss": 1000.0
}
```

## 📊 Types d'Opportunités

### 1. Arbitrage Simple
```
Ethereum USDT: $1.0010
BSC USDT:      $1.0025
Écart:         0.15% (profitable si > frais)
```

### 2. Arbitrage Triangulaire
```
ETH → USDT → Bridge → BSC → USDT → ETH
Profit net après tous les frais
```

### 3. Arbitrage de Liquidité
```
Pool faible liquidité: Prix déformé
Pool forte liquidité:  Prix normal
Opportunité temporaire
```

## 🔍 Détection d'Opportunités

### Algorithme de Scan

1. **Récupération des prix** sur toutes les chaînes
2. **Calcul des écarts** pour chaque paire
3. **Estimation des frais** (bridge + gas + DEX)
4. **Calcul de rentabilité** nette
5. **Scoring** de confiance et urgence
6. **Filtrage** selon les critères

### Métriques de Qualité

```python
# Score de confiance (0-1)
confidence = base_score * liquidity_factor * freshness_factor

# Score d'urgence (0-1)  
urgency = (price_gap / 5%) * confidence * time_factor

# Niveau de risque
risk_level = "LOW" | "MEDIUM" | "HIGH"
```

## ⚡ Processus d'Exécution

### Étapes d'Arbitrage

1. **Achat** sur chaîne source
   - Swap token natif → stablecoin
   - Vérification slippage
   - Confirmation transaction

2. **Bridge** cross-chain
   - Sélection route optimale
   - Exécution bridge
   - Attente confirmation

3. **Vente** sur chaîne destination
   - Swap stablecoin → token natif
   - Vérification slippage
   - Calcul profit réalisé

### Gestion des Erreurs

```python
# Timeouts
transaction_timeout = 300s
bridge_timeout = 3600s
opportunity_timeout = 300s

# Retry logic
max_retries = 3
retry_delay = exponential_backoff

# Fallback
emergency_exit = True
partial_execution = Allowed
```

## 💸 Calcul des Frais

### Types de Frais

| Type | Estimation | Variable |
|------|------------|----------|
| **Gas Ethereum** | $20-100 | Congestion réseau |
| **Gas BSC** | $0.50-2 | Stable |
| **Gas Polygon** | $0.01-0.10 | Très bas |
| **Bridge** | 0-0.1% | Selon protocole |
| **DEX Slippage** | 0.1-0.5% | Liquidité |

### Calcul de Rentabilité

```python
# Revenus
gross_profit = amount * (sell_price - buy_price)

# Coûts
bridge_fees = amount * bridge_fee_rate
gas_fees = gas_price * gas_units * native_token_price
dex_fees = amount * dex_fee_rate

# Profit net
net_profit = gross_profit - bridge_fees - gas_fees - dex_fees

# ROI
roi = (net_profit / (amount + total_fees)) * 100
```

## 🛡️ Gestion des Risques

### Limites de Position

```python
# Par trade
max_amount_per_trade = min(
    config.max_capital_per_trade,
    available_liquidity * 0.05,  # 5% max de la liquidité
    portfolio_value * 0.10       # 10% max du portefeuille
)

# Globales
max_concurrent_trades = 3
max_daily_trades = 50
max_daily_loss = 2% of portfolio
```

### Protection contre les Risques

- **Slippage Protection** : Limites strictes
- **Timeout Protection** : Annulation automatique
- **Liquidity Check** : Vérification avant exécution
- **Price Staleness** : Données fraîches uniquement
- **Bridge Risk** : Sélection protocoles fiables

## 📈 Métriques de Performance

### KPIs Principaux

```python
performance_metrics = {
    'total_opportunities': 1250,
    'executed_trades': 89,
    'success_rate': 94.4,          # %
    'total_profit': 2847.50,       # USD
    'average_roi': 2.3,            # %
    'average_execution_time': 8.5,  # minutes
    'best_opportunity': 127.30,     # USD profit
    'win_rate': 91.0               # %
}
```

### Analyse par Chaîne

| Chaîne | Opportunités | Profit Moyen | Temps Moyen |
|--------|--------------|--------------|-------------|
| **ETH→BSC** | 35% | $32.50 | 12 min |
| **BSC→Polygon** | 28% | $18.75 | 6 min |
| **Polygon→ETH** | 22% | $45.20 | 25 min |
| **ETH→Arbitrum** | 15% | $28.90 | 8 min |

## 🔧 Configuration Optimale

### Configuration Conservative

```python
conservative_config = BotConfig(
    min_profit_usd=30.0,           # Seuil élevé
    min_roi_percentage=2.0,        # ROI élevé
    max_concurrent_trades=1,       # Un seul trade
    max_capital_per_trade=2000.0,  # Capital limité
    scan_interval_seconds=60       # Scan moins fréquent
)
```

### Configuration Agressive

```python
aggressive_config = BotConfig(
    min_profit_usd=15.0,           # Seuil bas
    min_roi_percentage=0.8,        # ROI bas
    max_concurrent_trades=5,       # Plusieurs trades
    max_capital_per_trade=20000.0, # Capital élevé
    scan_interval_seconds=15       # Scan fréquent
)
```

## 🧪 Tests et Validation

### Tests Unitaires

```bash
# Tester les composants
python -m pytest tests/test_chain_connector.py
python -m pytest tests/test_arbitrage_detector.py
python -m pytest tests/test_arbitrage_executor.py
```

### Simulation

```bash
# Lancer l'exemple
python examples/cross_chain_arbitrage_example.py

# Test avec configuration custom
python examples/cross_chain_arbitrage_example.py --config config.json
```

### Backtesting

```python
from backtesting.backtest_engine import BacktestEngine

backtest = BacktestEngine()
results = await backtest.run_arbitrage_strategy(
    start_date="2024-01-01",
    end_date="2024-01-31",
    initial_capital=10000,
    config=arbitrage_config
)
```

## 📊 Monitoring et Alertes

### Dashboard Temps Réel

- **Opportunités actives** : Nombre et valeur
- **Exécutions en cours** : Statut et progression
- **Performance** : Profit, ROI, taux de réussite
- **Santé des chaînes** : Connectivité et latence

### Notifications

```python
# Alertes automatiques
notifications = {
    'new_opportunity': profit > $50,
    'execution_completed': always,
    'execution_failed': always,
    'daily_summary': end_of_day,
    'emergency_stop': always
}
```

## ⚠️ Risques et Limitations

### Risques Techniques

- **Bridge Risk** : Échec ou délai de bridge
- **Slippage Risk** : Prix défavorable à l'exécution
- **Gas Risk** : Coûts élevés en période de congestion
- **Smart Contract Risk** : Bugs dans les protocoles

### Risques de Marché

- **Volatilité** : Changement de prix pendant l'exécution
- **Liquidité** : Manque de liquidité sur les DEX
- **Competition** : Autres bots d'arbitrage
- **Regulatory Risk** : Changements réglementaires

### Limitations

- **Capital Requirements** : Besoin de fonds sur plusieurs chaînes
- **Time Sensitivity** : Opportunités de courte durée
- **Complexity** : Gestion multi-chaînes complexe
- **Fees Impact** : Frais peuvent annuler les profits

## 📞 Support et Maintenance

### Monitoring

```bash
# Logs en temps réel
tail -f logs/trading/trading.log

# Métriques de performance
curl http://localhost:8080/api/metrics

# Santé des chaînes
curl http://localhost:8080/api/health
```

### Maintenance

- **Mise à jour des prix** : Toutes les 30 secondes
- **Vérification santé** : Toutes les 5 minutes
- **Nettoyage logs** : Quotidien
- **Backup données** : Hebdomadaire

---

**💡 Conseil** : Commencez avec des montants faibles et augmentez progressivement selon les performances. L'arbitrage cross-chain nécessite une surveillance active et une bonne compréhension des risques.
