#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 Lanceur du Bot Optimisé - Interface Simplifiée
Script principal pour lancer et gérer le bot de trading optimisé
"""

import os
import sys
import time
import json
import argparse
from pathlib import Path
from datetime import datetime, timedelta

# Ajouter le répertoire du projet au path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from bots.optimized_safe_bot import OptimizedSafeBot
from utils.performance_analyzer import PerformanceAnalyzer

def print_banner():
    """Affiche la bannière du bot"""
    print("""
╔══════════════════════════════════════════════════════════════════════════════╗
║                    🤖 OPTIMIZED SAFE BOT v2.0                               ║
║                   Bot de Trading Crypto Intelligent                         ║
║                                                                              ║
║  🎯 Objectif: Maximiser la rentabilité en mode test                        ║
║  🔄 Réplication: Test → Production automatique                             ║
║  📊 Analytics: Métriques avancées en temps réel                            ║
║  🧠 IA: Optimisation automatique des paramètres                            ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """)

def show_menu():
    """Affiche le menu principal"""
    print("\n📋 MENU PRINCIPAL:")
    print("1. 🧪 Lancer un test d'optimisation (Testnet)")
    print("2. 🚀 Déployer en production")
    print("3. 📊 Analyser les performances")
    print("4. 🔧 Configurer les paramètres")
    print("5. 📁 Gérer les configurations")
    print("6. 📈 Voir les statistiques")
    print("7. ❓ Aide et documentation")
    print("0. 🚪 Quitter")
    print("-" * 50)

def launch_test_optimization():
    """Lance un test d'optimisation"""
    print("\n🧪 CONFIGURATION DU TEST D'OPTIMISATION")
    print("=" * 50)
    
    # Durée du test
    print("\n⏱️ Durée du test:")
    print("1. Test rapide (1 heure)")
    print("2. Test court (6 heures)")
    print("3. Test moyen (24 heures)")
    print("4. Test long (1 semaine - 168 heures)")
    print("5. Durée personnalisée")
    
    choice = input("\nChoisir la durée (1-5): ").strip()
    
    duration_map = {
        '1': 1,
        '2': 6,
        '3': 24,
        '4': 168,
    }
    
    if choice in duration_map:
        duration = duration_map[choice]
    elif choice == '5':
        try:
            duration = int(input("Durée en heures: "))
            if duration <= 0:
                print("❌ Durée invalide")
                return
        except ValueError:
            print("❌ Durée invalide")
            return
    else:
        print("❌ Choix invalide")
        return
    
    print(f"\n✅ Test configuré pour {duration} heure(s)")
    print(f"📅 Fin prévue: {(datetime.now() + timedelta(hours=duration)).strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Confirmation
    confirm = input("\n🚀 Lancer le test ? (oui/non): ").lower()
    if confirm not in ['oui', 'o', 'yes', 'y']:
        print("❌ Test annulé")
        return
    
    # Lancement du bot
    try:
        bot = OptimizedSafeBot(mode="testnet")
        bot.run_optimization_test(duration)
    except KeyboardInterrupt:
        print("\n🛑 Test interrompu par l'utilisateur")
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")

def deploy_to_production():
    """Déploie en production"""
    print("\n🚀 DÉPLOIEMENT EN PRODUCTION")
    print("=" * 50)
    
    # Recherche des configurations disponibles
    config_files = list(Path(".").glob("optimal_config_testnet_*.json"))
    
    if not config_files:
        print("❌ Aucune configuration testnet trouvée")
        print("💡 Lancez d'abord un test d'optimisation pour générer une configuration")
        return
    
    print("\n📁 Configurations disponibles:")
    for i, config_file in enumerate(config_files, 1):
        # Lire les métriques de la configuration
        try:
            with open(config_file, 'r') as f:
                config_data = json.load(f)
            
            metrics = config_data.get('performance_metrics', {})
            pnl = metrics.get('pnl_percentage', 0)
            win_rate = metrics.get('win_rate', 0)
            trades = metrics.get('total_trades', 0)
            
            print(f"{i}. {config_file.name}")
            print(f"   📊 P&L: {pnl:+.2f}% | Win Rate: {win_rate:.1f}% | Trades: {trades}")
            
        except Exception as e:
            print(f"{i}. {config_file.name} (erreur lecture: {e})")
    
    # Sélection de la configuration
    try:
        choice = int(input(f"\nChoisir une configuration (1-{len(config_files)}): "))
        if 1 <= choice <= len(config_files):
            selected_config = config_files[choice - 1]
        else:
            print("❌ Choix invalide")
            return
    except ValueError:
        print("❌ Choix invalide")
        return
    
    print(f"\n✅ Configuration sélectionnée: {selected_config.name}")
    
    # Vérification des performances
    try:
        with open(selected_config, 'r') as f:
            config_data = json.load(f)
        
        metrics = config_data.get('performance_metrics', {})
        pnl = metrics.get('pnl_percentage', 0)
        
        if pnl <= 0:
            print(f"⚠️ ATTENTION: Configuration non rentable ({pnl:.2f}%)")
            confirm = input("Continuer quand même ? (oui/non): ").lower()
            if confirm not in ['oui', 'o', 'yes', 'y']:
                print("❌ Déploiement annulé")
                return
        else:
            print(f"✅ Configuration rentable: {pnl:.2f}%")
    
    except Exception as e:
        print(f"❌ Erreur lecture configuration: {e}")
        return
    
    # Avertissement final
    print("\n⚠️ ATTENTION: DÉPLOIEMENT EN PRODUCTION")
    print("💰 Trading avec de l'argent réel")
    print("🛡️ Capital limité à 10% du testnet pour sécurité")
    
    confirm = input("\n🚀 Confirmer le déploiement ? (oui/non): ").lower()
    if confirm not in ['oui', 'o', 'yes', 'y']:
        print("❌ Déploiement annulé")
        return
    
    # Lancement en production
    try:
        bot = OptimizedSafeBot(mode="production")
        bot.replicate_to_production(str(selected_config))
    except Exception as e:
        print(f"❌ Erreur déploiement: {e}")

def analyze_performance():
    """Analyse les performances"""
    print("\n📊 ANALYSE DES PERFORMANCES")
    print("=" * 50)
    
    # Recherche des logs disponibles
    log_files = list(Path(".").glob("optimized_safe_bot_*.log"))
    
    if not log_files:
        print("❌ Aucun log trouvé")
        return
    
    print("\n📁 Logs disponibles:")
    for i, log_file in enumerate(log_files, 1):
        size_mb = log_file.stat().st_size / (1024 * 1024)
        modified = datetime.fromtimestamp(log_file.stat().st_mtime)
        print(f"{i}. {log_file.name} ({size_mb:.1f} MB, modifié: {modified.strftime('%Y-%m-%d %H:%M')})")
    
    # Sélection du log
    try:
        choice = int(input(f"\nChoisir un log (1-{len(log_files)}): "))
        if 1 <= choice <= len(log_files):
            selected_log = log_files[choice - 1]
        else:
            print("❌ Choix invalide")
            return
    except ValueError:
        print("❌ Choix invalide")
        return
    
    # Recherche du log de performance correspondant
    perf_log_name = selected_log.name.replace('.log', '_performance.log')
    perf_log = Path(perf_log_name)
    
    print(f"\n📖 Analyse de: {selected_log.name}")
    
    # Lancement de l'analyse
    try:
        analyzer = PerformanceAnalyzer(".")
        
        # Parse des logs
        trades = analyzer.parse_trading_logs(selected_log.name)
        print(f"✅ {len(trades)} trades analysés")
        
        if perf_log.exists():
            performance = analyzer.parse_performance_logs(perf_log.name)
            print(f"✅ {len(performance)} entrées de performance analysées")
        
        if trades:
            # Génération du rapport
            print("\n📄 Génération du rapport...")
            report = analyzer.generate_performance_report()
            print(report)
            
            # Proposition de graphiques
            create_charts = input("\n📊 Créer des graphiques ? (oui/non): ").lower()
            if create_charts in ['oui', 'o', 'yes', 'y']:
                analyzer.create_performance_charts()
        else:
            print("❌ Aucune donnée de trading trouvée dans le log")
    
    except Exception as e:
        print(f"❌ Erreur analyse: {e}")

def configure_parameters():
    """Configure les paramètres"""
    print("\n🔧 CONFIGURATION DES PARAMÈTRES")
    print("=" * 50)
    
    env_file = Path(".env.local")
    
    if not env_file.exists():
        print("❌ Fichier .env.local non trouvé")
        return
    
    print("\n📋 Paramètres actuels:")
    
    # Lecture des paramètres actuels
    current_params = {}
    with open(env_file, 'r') as f:
        for line in f:
            if '=' in line and not line.strip().startswith('#'):
                key, value = line.strip().split('=', 1)
                current_params[key] = value
    
    # Affichage des paramètres de trading
    trading_params = [
        'GRID_SIZE', 'GRID_SPACING', 'ORDER_SIZE', 'CAPITAL_MAX',
        'STOP_LOSS_PERCENT', 'TAKE_PROFIT_PERCENT', 'MIN_VOLUME_24H',
        'MIN_PRICE_CHANGE', 'MAX_SPREAD', 'MIN_PROFIT_THRESHOLD'
    ]
    
    for param in trading_params:
        value = current_params.get(param, 'Non défini')
        print(f"   • {param}: {value}")
    
    print("\n💡 Pour modifier les paramètres, éditez le fichier .env.local")
    print("⚠️ Redémarrez le bot après modification")

def manage_configurations():
    """Gère les configurations"""
    print("\n📁 GESTION DES CONFIGURATIONS")
    print("=" * 50)
    
    config_files = list(Path(".").glob("optimal_config_*.json"))
    
    if not config_files:
        print("❌ Aucune configuration trouvée")
        return
    
    print(f"\n📋 {len(config_files)} configuration(s) trouvée(s):")
    
    for config_file in config_files:
        try:
            with open(config_file, 'r') as f:
                config_data = json.load(f)
            
            timestamp = config_data.get('timestamp', 'Inconnu')
            mode = config_data.get('mode', 'Inconnu')
            metrics = config_data.get('performance_metrics', {})
            
            print(f"\n📄 {config_file.name}")
            print(f"   📅 Créé: {timestamp}")
            print(f"   🔧 Mode: {mode}")
            print(f"   📊 P&L: {metrics.get('pnl_percentage', 0):+.2f}%")
            print(f"   🎯 Win Rate: {metrics.get('win_rate', 0):.1f}%")
            print(f"   📈 Trades: {metrics.get('total_trades', 0)}")
            
        except Exception as e:
            print(f"\n📄 {config_file.name} (erreur: {e})")

def show_statistics():
    """Affiche les statistiques"""
    print("\n📈 STATISTIQUES GÉNÉRALES")
    print("=" * 50)
    
    # Compter les fichiers
    log_files = list(Path(".").glob("optimized_safe_bot_*.log"))
    config_files = list(Path(".").glob("optimal_config_*.json"))
    
    print(f"📁 Logs de trading: {len(log_files)}")
    print(f"📁 Configurations: {len(config_files)}")
    
    # Analyser les configurations pour des stats globales
    if config_files:
        total_trades = 0
        total_pnl = 0
        profitable_configs = 0
        
        for config_file in config_files:
            try:
                with open(config_file, 'r') as f:
                    config_data = json.load(f)
                
                metrics = config_data.get('performance_metrics', {})
                trades = metrics.get('total_trades', 0)
                pnl = metrics.get('pnl_percentage', 0)
                
                total_trades += trades
                total_pnl += pnl
                
                if pnl > 0:
                    profitable_configs += 1
                    
            except Exception:
                continue
        
        if config_files:
            avg_pnl = total_pnl / len(config_files)
            success_rate = profitable_configs / len(config_files) * 100
            
            print(f"\n📊 Statistiques des configurations:")
            print(f"   • Trades totaux: {total_trades}")
            print(f"   • P&L moyen: {avg_pnl:+.2f}%")
            print(f"   • Taux de succès: {success_rate:.1f}%")
            print(f"   • Configs rentables: {profitable_configs}/{len(config_files)}")

def show_help():
    """Affiche l'aide"""
    print("\n❓ AIDE ET DOCUMENTATION")
    print("=" * 50)
    
    print("""
🎯 OBJECTIF DU BOT:
   Le bot optimisé vise à maximiser la rentabilité en mode test,
   puis répliquer la stratégie gagnante vers la production.

🧪 PROCESSUS DE TEST:
   1. Lancer un test d'optimisation en testnet
   2. Le bot ajuste automatiquement ses paramètres
   3. Analyser les performances avec l'outil d'analyse
   4. Si rentable, déployer en production

📊 MÉTRIQUES IMPORTANTES:
   • P&L: Profit & Loss en pourcentage
   • Win Rate: Taux de trades gagnants
   • Profit Factor: Ratio profits/pertes
   • Sharpe Ratio: Rendement ajusté au risque

🔧 OPTIMISATION AUTOMATIQUE:
   Le bot teste différentes configurations et garde les meilleures.
   L'optimisation se fait toutes les heures pendant le test.

⚠️ SÉCURITÉ:
   • Mode testnet: Argent fictif, pas de risque
   • Mode production: Capital limité à 10% pour sécurité
   • Arrêts d'urgence automatiques en cas de perte

📁 FICHIERS IMPORTANTS:
   • .env.local: Configuration des paramètres
   • optimal_config_*.json: Configurations optimales sauvegardées
   • *.log: Logs détaillés de trading et performance

💡 CONSEILS:
   • Commencez par des tests courts (1-6h)
   • Analysez les résultats avant de passer en production
   • Surveillez les logs en temps réel
   • Gardez les configurations rentables
    """)

def main():
    """Fonction principale"""
    print_banner()
    
    while True:
        show_menu()
        
        try:
            choice = input("Votre choix: ").strip()
            
            if choice == '1':
                launch_test_optimization()
            elif choice == '2':
                deploy_to_production()
            elif choice == '3':
                analyze_performance()
            elif choice == '4':
                configure_parameters()
            elif choice == '5':
                manage_configurations()
            elif choice == '6':
                show_statistics()
            elif choice == '7':
                show_help()
            elif choice == '0':
                print("\n👋 Au revoir!")
                break
            else:
                print("❌ Choix invalide")
            
            input("\n⏸️ Appuyez sur Entrée pour continuer...")
            
        except KeyboardInterrupt:
            print("\n\n👋 Au revoir!")
            break
        except Exception as e:
            print(f"❌ Erreur: {e}")
            input("\n⏸️ Appuyez sur Entrée pour continuer...")

if __name__ == "__main__":
    main()
