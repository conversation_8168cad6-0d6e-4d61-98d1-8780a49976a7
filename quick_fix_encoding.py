#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 Quick Fix Encoding
Correction rapide et simple des problèmes d'encodage
"""

import os
import sys

def main():
    """Solution rapide: définir l'encodage et tester"""
    
    print("🔧 SOLUTION RAPIDE POUR L'ENCODAGE")
    print("=" * 50)
    
    # 1. Définir les variables d'environnement
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    
    # 2. Reconfigurer stdout/stderr si possible
    if hasattr(sys.stdout, 'reconfigure'):
        try:
            sys.stdout.reconfigure(encoding='utf-8')
            sys.stderr.reconfigure(encoding='utf-8')
            print("✅ Encodage UTF-8 configuré pour stdout/stderr")
        except:
            print("⚠️ Impossible de reconfigurer stdout/stderr")
    
    # 3. <PERSON><PERSON>er un script de test simple
    test_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test simple sans emojis
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Configuration Unicode pour Windows
if sys.platform.startswith('win'):
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    if hasattr(sys.stdout, 'reconfigure'):
        try:
            sys.stdout.reconfigure(encoding='utf-8')
            sys.stderr.reconfigure(encoding='utf-8')
        except:
            pass

def test_connection():
    """Test de connexion simple"""
    
    # Charger l'environnement
    env_file = Path(".env.local")
    if env_file.exists():
        load_dotenv(env_file)
        print("[OK] Configuration chargée")
    else:
        print("[ERROR] Fichier .env.local manquant")
        return False
    
    # Tester les clés API
    api_key = os.getenv("safe_bot_TEST_API_KEY")
    api_secret = os.getenv("safe_bot_TEST_API_SECRET")
    
    if api_key and api_secret:
        print("[OK] Clés API testnet trouvées")
        print(f"[INFO] API Key: {api_key[:8]}...")
        return True
    else:
        print("[ERROR] Clés API manquantes")
        return False

if __name__ == "__main__":
    print("=== TEST DE CONNEXION SIMPLE ===")
    
    if test_connection():
        print("[SUCCESS] Test réussi")
    else:
        print("[FAILED] Test échoué")
'''
    
    # Sauvegarder le script de test
    with open('simple_test.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("✅ Script de test simple créé: simple_test.py")
    
    # 4. Instructions pour l'utilisateur
    print("\n💡 INSTRUCTIONS:")
    print("1. Exécutez: python simple_test.py")
    print("2. Si ça marche, le problème vient des emojis dans le bot optimisé")
    print("3. Utilisez le bot safe_bot.py normal à la place:")
    print("   python launcher.py")
    print("   Choisissez option 1 (Safe Bot)")
    
    print("\n🔧 ALTERNATIVE:")
    print("Utilisez le bot principal sans emojis:")
    print("python bots/safe_bot.py --mode testnet")

if __name__ == "__main__":
    main()
