#!/usr/bin/env python3
"""
🛡️ Exemple d'utilisation du système de gestion d'erreurs
Démontre les fonctionnalités de gestion d'erreurs robuste
"""

import sys
import time
import logging
from pathlib import Path

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from utils.error_handler import (
    error_handler, with_retry, safe_execute, 
    ErrorCategory, ErrorSeverity, RetryStrategy
)
from utils.network_handler import network_handler, api_handler
from utils.trading_handler import trading_handler
from monitoring.error_monitor import error_monitor

def setup_logging():
    """Configure le logging pour l'exemple"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def example_basic_error_handling():
    """Exemple de gestion d'erreurs basique"""
    print("🛡️ EXEMPLE 1: Gestion d'erreurs basique")
    print("="*60)
    
    # Simuler une erreur réseau
    try:
        raise ConnectionError("Connexion impossible au serveur")
    except Exception as e:
        error_info = error_handler.handle_error(
            e, 
            ErrorCategory.NETWORK, 
            ErrorSeverity.HIGH,
            context={'server': 'api.example.com', 'port': 443}
        )
        print(f"✅ Erreur gérée: {error_info['error_type']}")
    
    # Simuler une erreur de trading
    try:
        raise ValueError("Fonds insuffisants pour l'ordre")
    except Exception as e:
        error_info = error_handler.handle_error(
            e,
            ErrorCategory.TRADING,
            ErrorSeverity.CRITICAL,
            context={'order_value': 1000, 'available_balance': 500}
        )
        print(f"✅ Erreur critique gérée: {error_info['error_type']}")

def example_retry_decorator():
    """Exemple d'utilisation du décorateur retry"""
    print("\n🛡️ EXEMPLE 2: Décorateur retry")
    print("="*60)
    
    # Fonction qui échoue les premières fois
    attempt_count = 0
    
    @with_retry(
        category=ErrorCategory.API,
        severity=ErrorSeverity.MEDIUM,
        retry_strategy=RetryStrategy(max_attempts=3, base_delay=0.5)
    )
    def unstable_api_call():
        nonlocal attempt_count
        attempt_count += 1
        print(f"📞 Tentative API #{attempt_count}")
        
        if attempt_count < 3:
            raise ConnectionError(f"Échec tentative {attempt_count}")
        
        return {"status": "success", "data": "API response"}
    
    try:
        result = unstable_api_call()
        print(f"✅ API call réussie: {result}")
    except Exception as e:
        print(f"❌ API call échouée après toutes les tentatives: {e}")

def example_safe_execute():
    """Exemple d'exécution sécurisée"""
    print("\n🛡️ EXEMPLE 3: Exécution sécurisée")
    print("="*60)
    
    def risky_calculation(x, y):
        """Fonction qui peut échouer"""
        if y == 0:
            raise ZeroDivisionError("Division par zéro")
        return x / y
    
    # Exécution sécurisée avec valeur par défaut
    result1 = safe_execute(
        risky_calculation, 10, 2,
        default_return=0,
        category=ErrorCategory.SYSTEM,
        severity=ErrorSeverity.LOW
    )
    print(f"✅ Calcul réussi: 10/2 = {result1}")
    
    # Exécution sécurisée qui échoue
    result2 = safe_execute(
        risky_calculation, 10, 0,
        default_return=-1,
        category=ErrorCategory.SYSTEM,
        severity=ErrorSeverity.MEDIUM
    )
    print(f"✅ Calcul avec erreur: 10/0 = {result2} (valeur par défaut)")

def example_network_handling():
    """Exemple de gestion d'erreurs réseau"""
    print("\n🛡️ EXEMPLE 4: Gestion réseau avancée")
    print("="*60)
    
    # Test de connectivité
    connectivity = network_handler.check_connectivity([
        'google.com', 'api.binance.com', 'nonexistent.domain.com'
    ])
    
    print("🌐 Test de connectivité:")
    for host, status in connectivity.items():
        status_emoji = "✅" if status else "❌"
        print(f"   {status_emoji} {host}")
    
    # Test d'endpoint
    test_result = network_handler.test_endpoint('https://httpbin.org/status/200')
    print(f"\n📊 Test endpoint: {test_result['success']} "
          f"({test_result['response_time']:.2f}s)")
    
    # Statut réseau complet
    network_status = network_handler.get_network_status()
    print(f"📈 Connectivité globale: {network_status['connectivity_percentage']:.1f}%")

def example_trading_validation():
    """Exemple de validation de trading"""
    print("\n🛡️ EXEMPLE 5: Validation de trading")
    print("="*60)
    
    # Ordre valide
    valid_order = {
        'symbol': 'BTC/USDT',
        'side': 'buy',
        'quantity': 0.001,
        'price': 50000,
        'type': 'LIMIT'
    }
    
    validation = trading_handler.validate_order(valid_order)
    print(f"✅ Ordre valide: {validation['valid']}")
    if validation['warnings']:
        print(f"⚠️ Avertissements: {validation['warnings']}")
    
    # Ordre invalide (quantité trop importante)
    invalid_order = {
        'symbol': 'ETH/USDT',
        'side': 'buy',
        'quantity': 1000,  # Trop gros
        'price': 3000
    }
    
    validation = trading_handler.validate_order(invalid_order)
    print(f"❌ Ordre invalide: {validation['valid']}")
    if validation['errors']:
        print(f"🚫 Erreurs: {validation['errors']}")
    
    # Statistiques de trading
    stats = trading_handler.get_trading_stats()
    print(f"📊 Arrêt d'urgence: {stats['emergency_stop']}")
    print(f"📊 P&L quotidien: {stats['daily_pnl']}")

def example_circuit_breaker():
    """Exemple de circuit breaker"""
    print("\n🛡️ EXEMPLE 6: Circuit breaker")
    print("="*60)
    
    # Simuler plusieurs erreurs pour déclencher le circuit breaker
    for i in range(3):
        try:
            raise ConnectionError(f"Erreur réseau #{i+1}")
        except Exception as e:
            error_handler.handle_error(
                e, ErrorCategory.NETWORK, ErrorSeverity.HIGH,
                context={'attempt': i+1}
            )
    
    # Vérifier si le circuit breaker est actif
    is_active = error_handler.is_circuit_breaker_active(ErrorCategory.NETWORK)
    print(f"🔌 Circuit breaker réseau actif: {is_active}")
    
    # Réinitialiser le circuit breaker
    if is_active:
        error_handler.reset_circuit_breaker(ErrorCategory.NETWORK)
        print("🔄 Circuit breaker réinitialisé")

def example_error_monitoring():
    """Exemple de monitoring d'erreurs"""
    print("\n🛡️ EXEMPLE 7: Monitoring d'erreurs")
    print("="*60)
    
    # Démarrer le monitoring
    error_monitor.start_monitoring(interval=5)
    print("📊 Monitoring démarré")
    
    # Simuler quelques erreurs
    for i in range(3):
        try:
            if i == 0:
                raise ValueError("Erreur de validation")
            elif i == 1:
                raise ConnectionError("Erreur réseau")
            else:
                raise RuntimeError("Erreur système")
        except Exception as e:
            category = [ErrorCategory.VALIDATION, ErrorCategory.NETWORK, ErrorCategory.SYSTEM][i]
            error_info = error_handler.handle_error(e, category, ErrorSeverity.MEDIUM)
            error_monitor.add_error_to_history(error_info)
        
        time.sleep(1)
    
    # Attendre un peu pour que le monitoring collecte les données
    time.sleep(6)
    
    # Récupérer le dashboard
    dashboard = error_monitor.get_monitoring_dashboard()
    print(f"📈 Score de santé: {dashboard['health_metrics']['system_health_score']:.1f}/100")
    print(f"🔢 Erreurs 24h: {dashboard['error_stats']['last_24h_total']}")
    print(f"⚠️ Alertes actives: {dashboard['active_alerts']}")
    
    # Générer un rapport
    report = error_monitor.generate_error_report(timeframe_hours=1)
    print(f"\n📄 Rapport d'erreurs:\n{report}")
    
    # Arrêter le monitoring
    error_monitor.stop_monitoring()
    print("🛑 Monitoring arrêté")

def example_fallback_strategies():
    """Exemple de stratégies de fallback"""
    print("\n🛡️ EXEMPLE 8: Stratégies de fallback")
    print("="*60)
    
    # Enregistrer une stratégie de fallback personnalisée
    def custom_fallback(*args, **kwargs):
        print("🔄 Exécution de la stratégie de fallback personnalisée")
        return {"fallback": True, "data": "Données de secours"}
    
    error_handler.register_fallback_strategy(
        ErrorCategory.API, custom_fallback, priority=1
    )
    
    # Tester le fallback
    @with_retry(
        category=ErrorCategory.API,
        severity=ErrorSeverity.MEDIUM,
        fallback_on_failure=True
    )
    def failing_api_call():
        raise ConnectionError("API indisponible")
    
    try:
        result = failing_api_call()
        print(f"✅ Résultat (avec fallback): {result}")
    except Exception as e:
        print(f"❌ Échec même avec fallback: {e}")

def show_error_statistics():
    """Affiche les statistiques d'erreurs"""
    print("\n📊 STATISTIQUES D'ERREURS")
    print("="*60)
    
    stats = error_handler.get_error_stats()
    
    print(f"🔢 Total erreurs: {stats['total_errors']}")
    print(f"📂 Erreurs par type:")
    for error_type, count in stats['error_counts'].items():
        print(f"   • {error_type}: {count}")
    
    print(f"🔌 Circuit breakers actifs: {len(stats['active_circuit_breakers'])}")
    for category, info in stats['active_circuit_breakers'].items():
        print(f"   • {category}: {info['error_count']} erreurs")

def main():
    """Fonction principale des exemples"""
    setup_logging()
    
    print("🛡️ EXEMPLES DE GESTION D'ERREURS ROBUSTE")
    print("="*80)
    
    try:
        # Exemples de base
        example_basic_error_handling()
        example_retry_decorator()
        example_safe_execute()
        
        # Exemples avancés
        example_network_handling()
        example_trading_validation()
        example_circuit_breaker()
        
        # Monitoring
        example_error_monitoring()
        
        # Fallbacks
        example_fallback_strategies()
        
        # Statistiques finales
        show_error_statistics()
        
        print("\n✅ Tous les exemples ont été exécutés avec succès!")
        print("💡 Le système de gestion d'erreurs est maintenant opérationnel")
        
    except KeyboardInterrupt:
        print("\n🛑 Exemples interrompus par l'utilisateur")
    except Exception as e:
        print(f"\n❌ Erreur inattendue: {e}")

if __name__ == "__main__":
    main()
