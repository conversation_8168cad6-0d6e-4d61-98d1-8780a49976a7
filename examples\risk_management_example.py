#!/usr/bin/env python3
"""
⚖️ Exemple d'utilisation du système de gestion des risques
Démontre les fonctionnalités avancées de money management
"""

import sys
import time
import random
from pathlib import Path

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from risk_management.portfolio_manager import portfolio_manager, PositionType, RiskLevel
from risk_management.stop_loss_manager import stop_loss_manager, StopLossConfig, TakeProfitConfig, StopLossType, TakeProfitType
from risk_management.correlation_manager import correlation_manager
from risk_management.risk_engine import risk_engine, RiskAction
import logging

def setup_logging():
    """Configure le logging pour l'exemple"""
    logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(filename, encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)s - %(levelname)s - %(message)s'
    )

def example_portfolio_management():
    """Exemple de gestion de portefeuille"""
    print("💼 EXEMPLE 1: Gestion de portefeuille")
    print("="*60)
    
    # Réinitialiser le portefeuille
    portfolio_manager.current_capital = portfolio_manager.initial_capital
    portfolio_manager.positions.clear()
    
    # Calculer la taille de position optimale
    symbol = 'BTC/USDT'
    entry_price = 50000
    
    optimal_size = portfolio_manager.calculate_position_size(symbol, entry_price)
    print(f"📏 Taille optimale pour {symbol}: {optimal_size:.6f} BTC")
    
    # Vérifier si on peut ouvrir la position
    can_open, reason = portfolio_manager.can_open_position(symbol, optimal_size, entry_price)
    print(f"✅ Peut ouvrir position: {can_open} - {reason}")
    
    if can_open:
        # Ouvrir la position
        success = portfolio_manager.open_position(
            symbol=symbol,
            position_type=PositionType.LONG,
            entry_price=entry_price,
            quantity=optimal_size,
            stop_loss=47500,  # -5%
            take_profit=55000  # +10%
        )
        print(f"📈 Position ouverte: {success}")
        
        # Afficher le résumé du portefeuille
        summary = portfolio_manager.get_portfolio_summary()
        print(f"💰 Valeur portefeuille: {summary['portfolio_value']:.2f}")
        print(f"📊 Exposition totale: {summary['total_exposure']:.2f}")
        print(f"⚖️ Score de risque: {summary['risk_score']:.1f}/100")

def example_stop_loss_management():
    """Exemple de gestion des stop-loss"""
    print("\n🛑 EXEMPLE 2: Gestion des stop-loss")
    print("="*60)
    
    symbol = 'ETH/USDT'
    entry_price = 3000
    
    # Configuration d'un stop-loss ATR
    atr_config = StopLossConfig(
        stop_type=StopLossType.ATR_BASED,
        initial_distance=0.05,
        atr_multiplier=2.0,
        min_distance=0.02,
        max_distance=0.1
    )
    stop_loss_manager.set_stop_loss_config(symbol, atr_config)
    
    # Ajouter des données de prix pour le calcul ATR
    base_price = entry_price
    for i in range(20):
        # Simuler des variations de prix
        variation = random.uniform(-0.03, 0.03)
        price = base_price * (1 + variation)
        stop_loss_manager.update_price_history(symbol, price)
        base_price = price
    
    # Calculer le stop-loss
    stop_price = stop_loss_manager.calculate_stop_loss(symbol, entry_price, 'LONG')
    print(f"🛑 Stop-loss calculé pour {symbol}: {stop_price:.2f}")
    
    # Calculer les take-profits
    tp_config = TakeProfitConfig(
        tp_type=TakeProfitType.SCALED,
        target_distance=0.1,
        scale_levels=[0.05, 0.1, 0.15]
    )
    stop_loss_manager.set_take_profit_config(symbol, tp_config)
    
    tp_levels = stop_loss_manager.calculate_take_profit(symbol, entry_price, 'LONG', stop_price)
    print(f"🎯 Take-profits: {[f'{tp:.2f}' for tp in tp_levels]}")
    
    # Créer un stop-loss suiveur
    trailing_stop_id = stop_loss_manager.create_trailing_stop(symbol, entry_price, 'LONG', 0.03)
    print(f"🔄 Stop suiveur créé: {trailing_stop_id}")

def example_correlation_analysis():
    """Exemple d'analyse de corrélations"""
    print("\n📊 EXEMPLE 3: Analyse de corrélations")
    print("="*60)
    
    # Simuler des données de prix pour plusieurs symboles
    symbols = ['BTC/USDT', 'ETH/USDT', 'ADA/USDT', 'SOL/USDT']
    
    # Générer des prix corrélés (BTC et ETH) et moins corrélés (ADA, SOL)
    btc_base = 50000
    eth_base = 3000
    ada_base = 1.5
    sol_base = 100
    
    for i in range(50):
        # BTC avec tendance
        btc_variation = random.uniform(-0.02, 0.03)
        btc_price = btc_base * (1 + btc_variation)
        correlation_manager.add_price_data('BTC/USDT', btc_price)
        btc_base = btc_price
        
        # ETH corrélé avec BTC (70% de corrélation)
        if random.random() < 0.7:
            eth_variation = btc_variation * 0.8  # Suit BTC
        else:
            eth_variation = random.uniform(-0.03, 0.03)  # Mouvement indépendant
        eth_price = eth_base * (1 + eth_variation)
        correlation_manager.add_price_data('ETH/USDT', eth_price)
        eth_base = eth_price
        
        # ADA moins corrélé
        ada_variation = random.uniform(-0.05, 0.05)
        ada_price = ada_base * (1 + ada_variation)
        correlation_manager.add_price_data('ADA/USDT', ada_price)
        ada_base = ada_price
        
        # SOL indépendant
        sol_variation = random.uniform(-0.04, 0.04)
        sol_price = sol_base * (1 + sol_variation)
        correlation_manager.add_price_data('SOL/USDT', sol_price)
        sol_base = sol_price
    
    # Calculer la matrice de corrélations
    correlation_matrix = correlation_manager.calculate_correlation_matrix()
    print("📈 Matrice de corrélations calculée:")
    if not correlation_matrix.empty:
        print(correlation_matrix.round(2))
    
    # Identifier les corrélations élevées
    high_correlations = correlation_manager.get_high_correlations(threshold=0.6)
    print(f"\n🔗 Corrélations élevées (>60%):")
    for symbol1, symbol2, correlation in high_correlations:
        print(f"   {symbol1} ↔️ {symbol2}: {correlation:.2f}")
    
    # Simuler un portefeuille et vérifier la diversification
    portfolio_positions = {
        'BTC/USDT': 0.4,  # 40%
        'ETH/USDT': 0.3,  # 30%
        'ADA/USDT': 0.2,  # 20%
        'SOL/USDT': 0.1   # 10%
    }
    
    diversification_score = correlation_manager.calculate_diversification_score(portfolio_positions)
    print(f"\n📊 Score de diversification: {diversification_score:.1f}/100")
    
    suggestions = correlation_manager.suggest_diversification_improvements(portfolio_positions)
    print("💡 Suggestions d'amélioration:")
    for suggestion in suggestions:
        print(f"   • {suggestion}")

def example_risk_assessment():
    """Exemple d'évaluation des risques"""
    print("\n⚖️ EXEMPLE 4: Évaluation des risques")
    print("="*60)
    
    # Ouvrir quelques positions pour avoir un portefeuille
    portfolio_manager.open_position('BTC/USDT', PositionType.LONG, 50000, 0.01)
    portfolio_manager.open_position('ETH/USDT', PositionType.LONG, 3000, 0.1)
    
    # Évaluer un nouveau trade
    symbol = 'ADA/USDT'
    price = 1.5
    quantity = 500  # Grosse position
    
    assessment = risk_engine.assess_trade_risk(symbol, 'buy', quantity, price)
    
    print(f"📊 Évaluation pour {symbol}:")
    print(f"   Action recommandée: {assessment.action.value}")
    print(f"   Score de risque: {assessment.risk_score:.1f}/100")
    print(f"   Taille max recommandée: {assessment.max_position_size:.6f}")
    
    if assessment.reasons:
        print("   ⚠️ Raisons:")
        for reason in assessment.reasons:
            print(f"      • {reason}")
    
    if assessment.recommendations:
        print("   💡 Recommandations:")
        for rec in assessment.recommendations:
            print(f"      • {rec}")
    
    # Test avec une position plus raisonnable
    print(f"\n📊 Évaluation avec position réduite:")
    small_quantity = 100
    assessment2 = risk_engine.assess_trade_risk(symbol, 'buy', small_quantity, price)
    print(f"   Action: {assessment2.action.value} (score: {assessment2.risk_score:.1f})")

def example_risk_monitoring():
    """Exemple de monitoring des risques"""
    print("\n📡 EXEMPLE 5: Monitoring des risques")
    print("="*60)
    
    # Démarrer le monitoring
    risk_engine.start_monitoring(interval=2)  # 2 secondes pour la démo
    print("🚀 Monitoring des risques démarré")
    
    # Laisser tourner pendant quelques secondes
    print("⏳ Simulation en cours...")
    time.sleep(10)
    
    # Récupérer le dashboard
    dashboard = risk_engine.get_risk_dashboard()
    
    print(f"📊 Dashboard des risques:")
    print(f"   Score de santé: {dashboard['health_score']:.1f}/100")
    print(f"   Valeur portefeuille: {dashboard['portfolio']['total_value']:.2f}")
    print(f"   Rendement total: {dashboard['portfolio']['total_return']:.2f}%")
    print(f"   Score de risque: {dashboard['portfolio']['risk_score']:.1f}/100")
    print(f"   Alertes actives: {dashboard['alert_counts']['total']}")
    
    if dashboard['recent_alerts']:
        print("   🚨 Alertes récentes:")
        for alert in dashboard['recent_alerts'][-3:]:  # 3 dernières
            print(f"      • {alert['type']}: {alert['message']}")
    
    # Arrêter le monitoring
    risk_engine.stop_monitoring()
    print("🛑 Monitoring arrêté")

def example_complete_workflow():
    """Exemple de workflow complet"""
    print("\n🔄 EXEMPLE 6: Workflow complet")
    print("="*60)
    
    # 1. Analyser un trade potentiel
    symbol = 'SOL/USDT'
    entry_price = 100
    
    print(f"1️⃣ Analyse du trade {symbol} @ {entry_price}")
    
    # 2. Évaluation des risques
    assessment = risk_engine.assess_trade_risk(symbol, 'buy', 5, entry_price)
    print(f"   Évaluation: {assessment.action.value}")
    
    if assessment.action in [RiskAction.ALLOW, RiskAction.REDUCE_SIZE]:
        # 3. Calculer la taille optimale
        optimal_size = assessment.max_position_size or 1
        if assessment.action == RiskAction.REDUCE_SIZE:
            optimal_size *= 0.5  # Réduire de 50%
        
        print(f"2️⃣ Taille de position: {optimal_size:.6f}")
        
        # 4. Configurer les stop-loss et take-profit
        stop_price = assessment.suggested_stop_loss
        tp_prices = assessment.suggested_take_profit
        
        print(f"3️⃣ Stop-loss: {stop_price:.2f}")
        print(f"4️⃣ Take-profits: {[f'{tp:.2f}' for tp in tp_prices] if tp_prices else 'N/A'}")
        
        # 5. Ouvrir la position
        success = portfolio_manager.open_position(
            symbol=symbol,
            position_type=PositionType.LONG,
            entry_price=entry_price,
            quantity=optimal_size,
            stop_loss=stop_price,
            take_profit=tp_prices[0] if tp_prices else None
        )
        
        print(f"5️⃣ Position ouverte: {'✅' if success else '❌'}")
        
        if success:
            # 6. Créer un stop-loss suiveur
            trailing_id = stop_loss_manager.create_trailing_stop(symbol, entry_price, 'LONG', 0.03)
            print(f"6️⃣ Stop suiveur créé: {trailing_id}")
            
            # 7. Vérifier la diversification
            portfolio_weights = {}
            portfolio_value = portfolio_manager.get_portfolio_value()
            for sym, pos in portfolio_manager.positions.items():
                portfolio_weights[sym] = pos.market_value / portfolio_value
            
            div_score = correlation_manager.calculate_diversification_score(portfolio_weights)
            print(f"7️⃣ Score de diversification: {div_score:.1f}/100")
    
    else:
        print(f"❌ Trade rejeté: {', '.join(assessment.reasons)}")

def show_final_summary():
    """Affiche un résumé final"""
    print("\n📋 RÉSUMÉ FINAL")
    print("="*60)
    
    # Résumé du portefeuille
    summary = portfolio_manager.get_portfolio_summary()
    print(f"💼 Portefeuille:")
    print(f"   Valeur totale: {summary['portfolio_value']:.2f}")
    print(f"   Rendement: {summary['total_return']:.2f}%")
    print(f"   Positions: {summary['positions_count']}")
    print(f"   Score de risque: {summary['risk_score']:.1f}/100")
    
    # Statistiques des stop-loss
    stop_stats = stop_loss_manager.get_stop_loss_stats()
    print(f"\n🛑 Stop-loss:")
    print(f"   Actifs: {stop_stats['active_stops']}")
    print(f"   Déclenchés: {stop_stats['stops_triggered']}")
    print(f"   Taux de déclenchement: {stop_stats['trigger_rate']:.1f}%")
    
    # Rapport de corrélations
    corr_report = correlation_manager.get_correlation_report()
    print(f"\n📊 Corrélations:")
    print(f"   Symboles analysés: {corr_report['symbols_analyzed']}")
    print(f"   Corrélations élevées: {corr_report['high_correlations_count']}")

def main():
    """Fonction principale des exemples"""
    setup_logging()
    
    print("⚖️ EXEMPLES DE GESTION DES RISQUES AVANCÉE")
    print("="*80)
    
    try:
        # Exemples de base
        example_portfolio_management()
        example_stop_loss_management()
        example_correlation_analysis()
        
        # Exemples avancés
        example_risk_assessment()
        example_risk_monitoring()
        
        # Workflow complet
        example_complete_workflow()
        
        # Résumé final
        show_final_summary()
        
        print("\n✅ Tous les exemples ont été exécutés avec succès!")
        print("💡 Le système de gestion des risques est maintenant opérationnel")
        
    except KeyboardInterrupt:
        print("\n🛑 Exemples interrompus par l'utilisateur")
        risk_engine.stop_monitoring()
    except Exception as e:
        print(f"\n❌ Erreur inattendue: {e}")
        risk_engine.stop_monitoring()

if __name__ == "__main__":
    main()
