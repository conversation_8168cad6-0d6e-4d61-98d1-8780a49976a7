#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 BotCrypto Launcher
Interface principale pour lancer tous les bots de trading
"""

import os
import sys
import subprocess
from pathlib import Path
from dotenv import load_dotenv

class BotLauncher:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.load_environment()
        
    def load_environment(self):
        """Charge les variables d'environnement"""
        env_file = self.project_root / ".env.local"
        if env_file.exists():
            load_dotenv(env_file)
            print("✅ Configuration chargée")
        else:
            print("❌ Fichier .env.local manquant")
            print("📝 Copiez .env.example vers .env.local et configurez vos clés")
            sys.exit(1)
    
    def check_configuration(self):
        """Vérifie la configuration"""
        required_keys = {
            "Production": ["safe_bot_PROD_API_KEY", "safe_bot_PROD_API_SECRET"],
            "Testnet": ["safe_bot_TEST_API_KEY", "safe_bot_TEST_API_SECRET"],
        }
        
        config_status = {}
        for env_type, keys in required_keys.items():
            config_status[env_type] = all(os.getenv(key) for key in keys)
        
        return config_status
    
    def show_menu(self):
        """Affiche le menu principal"""
        config_status = self.check_configuration()
        
        print("\n" + "="*60)
        print("🤖 BOTCRYPTO LAUNCHER")
        print("="*60)
        print("📊 Configuration:")
        for env_type, status in config_status.items():
            status_icon = "✅" if status else "❌"
            print(f"   {status_icon} {env_type}")
        
        print("\n🎯 Bots Disponibles:")
        print("1. 🧪 Safe Bot (Testnet) - Trading avec argent fictif")
        print("2. 💰 Safe Bot (Production) - Trading avec argent réel")
        print("3. 🎯 Sniper Bot - Achat rapide de nouveaux tokens")
        print("4. 📈 Grid Bot v2 - Trading avancé avec grille")
        print("5. 📊 Dashboard - Interface de monitoring")
        print("6. 🧪 Tests & Validation")
        print("0. ❌ Quitter")
        print("="*60)
    
    def launch_safe_bot_testnet(self):
        """Lance le Safe Bot en mode testnet"""
        if not os.getenv("safe_bot_TEST_API_KEY"):
            print("❌ Clés testnet manquantes")
            return
        
        print("🧪 Lancement du Safe Bot (Testnet)...")
        script_path = self.project_root / "bots" / "safe_bot.py"
        subprocess.run([sys.executable, str(script_path), "--mode", "testnet"])
    
    def launch_safe_bot_prod(self):
        """Lance le Safe Bot en mode production"""
        if not os.getenv("safe_bot_PROD_API_KEY"):
            print("❌ Clés production manquantes")
            return
        
        print("⚠️ ATTENTION: Trading avec argent réel !")
        confirm = input("Confirmer ? (oui/non): ").lower()
        if confirm in ['oui', 'o', 'yes', 'y']:
            print("💰 Lancement du Safe Bot (Production)...")
            script_path = self.project_root / "bots" / "safe_bot.py"
            subprocess.run([sys.executable, str(script_path), "--mode", "production"])
    
    def launch_sniper_bot(self):
        """Lance le Sniper Bot"""
        print("🎯 Lancement du Sniper Bot...")
        script_path = self.project_root / "bots" / "sniper_bot_v2.py"
        subprocess.run([sys.executable, str(script_path)])
    
    def launch_grid_bot(self):
        """Lance le Grid Bot v2"""
        print("📈 Lancement du Grid Bot v2...")
        script_path = self.project_root / "bots" / "grid_bot_v2.py"
        subprocess.run([sys.executable, str(script_path)])
    
    def launch_dashboard(self):
        """Lance le dashboard"""
        print("📊 Lancement du Dashboard...")
        script_path = self.project_root / "dashboard" / "web_server.py"
        subprocess.run([sys.executable, str(script_path)])
    
    def run_tests(self):
        """Lance les tests"""
        print("🧪 Lancement des tests...")
        script_path = self.project_root / "tools" / "test_runner.py"
        subprocess.run([sys.executable, str(script_path)])
    
    def run(self):
        """Boucle principale"""
        while True:
            self.show_menu()
            
            try:
                choice = input("\n👉 Votre choix: ").strip()
                
                if choice == "0":
                    print("👋 Au revoir !")
                    break
                elif choice == "1":
                    self.launch_safe_bot_testnet()
                elif choice == "2":
                    self.launch_safe_bot_prod()
                elif choice == "3":
                    self.launch_sniper_bot()
                elif choice == "4":
                    self.launch_grid_bot()
                elif choice == "5":
                    self.launch_dashboard()
                elif choice == "6":
                    self.run_tests()
                else:
                    print("❌ Choix invalide")
                    
            except KeyboardInterrupt:
                print("\n👋 Arrêt demandé")
                break
            except Exception as e:
                print(f"❌ Erreur: {e}")

def main():
    launcher = BotLauncher()
    launcher.run()

if __name__ == "__main__":
    main()
