#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 Unicode Safe Logger
Logger sécurisé pour les caractères Unicode sur Windows
"""

import logging
import sys
import os
from pathlib import Path

class UnicodeSafeLogger:
    """Logger qui gère correctement l'Unicode sur Windows"""
    
    def __init__(self, name, log_file=None):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.INFO)
        
        # Éviter les doublons de handlers
        if self.logger.handlers:
            self.logger.handlers.clear()
        
        # Formatter sans emojis pour les logs
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # Handler pour fichier avec encodage UTF-8
        if log_file:
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)
        
        # Handler pour console avec gestion d'erreur
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
    
    def safe_log(self, level, message):
        """Log un message en gérant les erreurs d'encodage"""
        try:
            # Remplacer les emojis par du texte
            safe_message = self.make_unicode_safe(message)
            getattr(self.logger, level)(safe_message)
        except UnicodeEncodeError:
            # Fallback: message sans caractères spéciaux
            ascii_message = message.encode('ascii', 'ignore').decode('ascii')
            getattr(self.logger, level)(f"[UNICODE_ERROR] {ascii_message}")
    
    def make_unicode_safe(self, text):
        """Remplace les emojis par du texte lisible"""
        emoji_replacements = {
            '🚀': '[START]',
            '✅': '[OK]',
            '❌': '[ERROR]',
            '⚠️': '[WARNING]',
            '📊': '[STATS]',
            '💰': '[MONEY]',
            '🎯': '[TARGET]',
            '📈': '[UP]',
            '📉': '[DOWN]',
            '🔧': '[CONFIG]',
            '⏸️': '[PAUSE]',
            '🛑': '[STOP]',
            '💡': '[INFO]',
            '🔍': '[SEARCH]',
            '📝': '[LOG]',
            '🎉': '[SUCCESS]',
            '⏱️': '[TIME]',
            '🧪': '[TEST]',
            '🔐': '[SECURE]',
            '📦': '[PACKAGE]',
            '🌐': '[NETWORK]',
            '💾': '[SAVE]',
            '🏦': '[BANK]',
            '🎮': '[GAME]',
            '📁': '[FOLDER]',
            '🔄': '[REFRESH]',
            '⚡': '[FAST]',
            '🎲': '[RANDOM]',
            '🔥': '[HOT]',
            '❄️': '[COLD]',
            '🌟': '[STAR]',
            '💎': '[DIAMOND]',
            '🚨': '[ALERT]',
            '🎪': '[CIRCUS]',
            '🎭': '[MASK]',
            '🎨': '[ART]',
            '🎵': '[MUSIC]',
            '🎬': '[MOVIE]',
            '📱': '[PHONE]',
            '💻': '[COMPUTER]',
            '⌚': '[WATCH]',
            '📷': '[CAMERA]',
            '🔊': '[SOUND]',
            '🔇': '[MUTE]',
            '🔋': '[BATTERY]',
            '🔌': '[PLUG]',
            '💡': '[BULB]',
            '🔦': '[FLASHLIGHT]',
            '🕯️': '[CANDLE]',
            '🧯': '[EXTINGUISHER]',
            '⛽': '[FUEL]',
            '🚗': '[CAR]',
            '🚕': '[TAXI]',
            '🚙': '[SUV]',
            '🚌': '[BUS]',
            '🚎': '[TROLLEY]',
            '🏎️': '[RACE_CAR]',
            '🚓': '[POLICE]',
            '🚑': '[AMBULANCE]',
            '🚒': '[FIRE_TRUCK]',
            '🚐': '[VAN]',
            '🛻': '[TRUCK]',
            '🚚': '[DELIVERY]',
            '🚛': '[SEMI]',
            '🚜': '[TRACTOR]'
        }
        
        safe_text = str(text)
        for emoji, replacement in emoji_replacements.items():
            safe_text = safe_text.replace(emoji, replacement)
        
        return safe_text
    
    def info(self, message):
        self.safe_log('info', message)
    
    def warning(self, message):
        self.safe_log('warning', message)
    
    def error(self, message):
        self.safe_log('error', message)
    
    def debug(self, message):
        self.safe_log('debug', message)
    
    def critical(self, message):
        self.safe_log('critical', message)

def get_unicode_safe_logger(name, log_file=None):
    """Fonction helper pour créer un logger sécurisé"""
    return UnicodeSafeLogger(name, log_file)

# Configuration globale pour éviter les erreurs d'encodage
if sys.platform.startswith('win'):
    # Sur Windows, forcer l'encodage UTF-8 pour stdout
    if hasattr(sys.stdout, 'reconfigure'):
        try:
            sys.stdout.reconfigure(encoding='utf-8')
        except:
            pass
