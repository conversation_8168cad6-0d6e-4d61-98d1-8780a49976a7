"""
🌐 Gestionnaire d'erreurs réseau spécialisé
Gestion robuste des erreurs réseau avec fallbacks et retry intelligents
"""

import requests
import time
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
import socket
from pathlib import Path
import sys

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from utils.error_handler import error_handler, ErrorCategory, ErrorSeverity, with_retry

class NetworkHandler:
    """Gestionnaire spécialisé pour les erreurs réseau"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.session = requests.Session()
        
        # Configuration des timeouts
        self.default_timeout = (10, 30)  # (connect, read)
        self.retry_timeout = (5, 15)     # Timeouts réduits pour les retry
        
        # Configuration des headers
        self.session.headers.update({
            'User-Agent': 'botCrypto/2.0',
            'Accept': 'application/json',
            'Connection': 'keep-alive'
        })
        
        # URLs de fallback pour différents services
        self.fallback_urls = {
            'binance': [
                'https://api.binance.com',
                'https://api1.binance.com',
                'https://api2.binance.com',
                'https://api3.binance.com'
            ],
            'coingecko': [
                'https://api.coingecko.com',
                'https://pro-api.coingecko.com'
            ],
            'dexscreener': [
                'https://api.dexscreener.com'
            ]
        }
        
        # Enregistrer les stratégies de fallback
        self._register_fallback_strategies()
        
        self.logger.info("🌐 Gestionnaire réseau initialisé")
    
    def _register_fallback_strategies(self):
        """Enregistre les stratégies de fallback réseau"""
        error_handler.register_fallback_strategy(
            ErrorCategory.NETWORK, 
            self._fallback_request, 
            priority=1
        )
    
    def _fallback_request(self, method: str, url: str, **kwargs) -> requests.Response:
        """Stratégie de fallback pour les requêtes réseau"""
        # Extraire le service de l'URL
        service = self._extract_service_from_url(url)
        
        if service and service in self.fallback_urls:
            fallback_urls = self.fallback_urls[service]
            
            for fallback_url in fallback_urls[1:]:  # Ignorer la première (déjà testée)
                try:
                    # Remplacer l'URL de base
                    original_base = fallback_urls[0]
                    fallback_request_url = url.replace(original_base, fallback_url)
                    
                    self.logger.info(f"🔄 Tentative fallback: {fallback_request_url}")
                    
                    response = self.session.request(
                        method, fallback_request_url, 
                        timeout=self.retry_timeout, 
                        **kwargs
                    )
                    
                    if response.status_code == 200:
                        self.logger.info(f"✅ Fallback réussi: {fallback_url}")
                        return response
                        
                except Exception as e:
                    self.logger.warning(f"❌ Fallback échoué {fallback_url}: {e}")
                    continue
        
        raise requests.RequestException("Tous les fallbacks réseau ont échoué")
    
    def _extract_service_from_url(self, url: str) -> Optional[str]:
        """Extrait le nom du service depuis une URL"""
        if 'binance' in url:
            return 'binance'
        elif 'coingecko' in url:
            return 'coingecko'
        elif 'dexscreener' in url:
            return 'dexscreener'
        return None
    
    @with_retry(
        category=ErrorCategory.NETWORK,
        severity=ErrorSeverity.MEDIUM,
        fallback_on_failure=True
    )
    def get(self, url: str, **kwargs) -> requests.Response:
        """GET request avec gestion d'erreurs"""
        return self._make_request('GET', url, **kwargs)
    
    @with_retry(
        category=ErrorCategory.NETWORK,
        severity=ErrorSeverity.MEDIUM,
        fallback_on_failure=True
    )
    def post(self, url: str, **kwargs) -> requests.Response:
        """POST request avec gestion d'erreurs"""
        return self._make_request('POST', url, **kwargs)
    
    def _make_request(self, method: str, url: str, **kwargs) -> requests.Response:
        """Effectue une requête avec gestion d'erreurs détaillée"""
        # Ajouter timeout par défaut si non spécifié
        if 'timeout' not in kwargs:
            kwargs['timeout'] = self.default_timeout
        
        try:
            start_time = time.time()
            response = self.session.request(method, url, **kwargs)
            duration = time.time() - start_time
            
            # Log de la requête réussie
            self.logger.debug(f"✅ {method} {url} - {response.status_code} ({duration:.2f}s)")
            
            # Vérifier le statut de la réponse
            if response.status_code >= 400:
                self._handle_http_error(response, url)
            
            return response
            
        except requests.exceptions.Timeout as e:
            context = {'url': url, 'method': method, 'timeout': kwargs.get('timeout')}
            error_handler.handle_error(e, ErrorCategory.NETWORK, ErrorSeverity.HIGH, context)
            raise
            
        except requests.exceptions.ConnectionError as e:
            context = {'url': url, 'method': method, 'connection_error': str(e)}
            error_handler.handle_error(e, ErrorCategory.NETWORK, ErrorSeverity.HIGH, context)
            raise
            
        except requests.exceptions.HTTPError as e:
            context = {'url': url, 'method': method, 'http_error': str(e)}
            error_handler.handle_error(e, ErrorCategory.NETWORK, ErrorSeverity.MEDIUM, context)
            raise
            
        except Exception as e:
            context = {'url': url, 'method': method, 'unexpected_error': str(e)}
            error_handler.handle_error(e, ErrorCategory.NETWORK, ErrorSeverity.HIGH, context)
            raise
    
    def _handle_http_error(self, response: requests.Response, url: str):
        """Gère les erreurs HTTP spécifiques"""
        status_code = response.status_code
        
        if status_code == 429:  # Rate limiting
            # Extraire le délai de retry si disponible
            retry_after = response.headers.get('Retry-After')
            if retry_after:
                try:
                    delay = int(retry_after)
                    self.logger.warning(f"⏳ Rate limit atteint, attente {delay}s")
                    time.sleep(delay)
                except ValueError:
                    pass
            
            raise requests.exceptions.HTTPError(f"Rate limit exceeded: {status_code}")
            
        elif status_code >= 500:  # Erreurs serveur
            raise requests.exceptions.HTTPError(f"Server error: {status_code}")
            
        elif status_code >= 400:  # Erreurs client
            try:
                error_data = response.json()
                error_msg = error_data.get('msg', error_data.get('message', 'Unknown error'))
            except:
                error_msg = response.text[:100]
            
            raise requests.exceptions.HTTPError(f"Client error {status_code}: {error_msg}")
    
    def check_connectivity(self, hosts: List[str] = None) -> Dict[str, bool]:
        """Vérifie la connectivité réseau vers différents hosts"""
        if hosts is None:
            hosts = [
                'api.binance.com',
                'api.coingecko.com',
                'google.com',
                '*******'
            ]
        
        results = {}
        
        for host in hosts:
            try:
                # Test de résolution DNS et connectivité
                socket.setdefaulttimeout(5)
                socket.socket(socket.AF_INET, socket.SOCK_STREAM).connect((host, 80))
                results[host] = True
                self.logger.debug(f"✅ Connectivité OK: {host}")
                
            except Exception as e:
                results[host] = False
                self.logger.warning(f"❌ Connectivité KO: {host} - {e}")
        
        return results
    
    def get_network_status(self) -> Dict[str, Any]:
        """Récupère le statut réseau complet"""
        connectivity = self.check_connectivity()
        
        return {
            'connectivity': connectivity,
            'connected_hosts': sum(connectivity.values()),
            'total_hosts': len(connectivity),
            'connectivity_percentage': (sum(connectivity.values()) / len(connectivity)) * 100,
            'session_info': {
                'cookies': len(self.session.cookies),
                'headers': dict(self.session.headers),
                'adapters': list(self.session.adapters.keys())
            },
            'fallback_urls': self.fallback_urls
        }
    
    def reset_session(self):
        """Remet à zéro la session réseau"""
        self.session.close()
        self.session = requests.Session()
        
        # Reconfigurer les headers
        self.session.headers.update({
            'User-Agent': 'botCrypto/2.0',
            'Accept': 'application/json',
            'Connection': 'keep-alive'
        })
        
        self.logger.info("🔄 Session réseau réinitialisée")
    
    def configure_proxy(self, proxy_url: str):
        """Configure un proxy pour les requêtes"""
        proxies = {
            'http': proxy_url,
            'https': proxy_url
        }
        self.session.proxies.update(proxies)
        self.logger.info(f"🔧 Proxy configuré: {proxy_url}")
    
    def test_endpoint(self, url: str, expected_status: int = 200) -> Dict[str, Any]:
        """Teste un endpoint spécifique"""
        test_result = {
            'url': url,
            'timestamp': datetime.now(),
            'success': False,
            'status_code': None,
            'response_time': None,
            'error': None
        }
        
        try:
            start_time = time.time()
            response = self.get(url)
            test_result['response_time'] = time.time() - start_time
            test_result['status_code'] = response.status_code
            test_result['success'] = response.status_code == expected_status
            
        except Exception as e:
            test_result['error'] = str(e)
            test_result['response_time'] = time.time() - start_time
        
        return test_result

class APIHandler:
    """Gestionnaire spécialisé pour les erreurs d'API"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.network = NetworkHandler()
        self.api_keys_rotation = {}  # Rotation des clés API
        self.rate_limits = {}        # Suivi des rate limits
        
        self.logger.info("🔑 Gestionnaire API initialisé")
    
    @with_retry(
        category=ErrorCategory.API,
        severity=ErrorSeverity.MEDIUM
    )
    def call_api(self, url: str, method: str = 'GET', headers: Dict = None, 
                 data: Dict = None, params: Dict = None) -> Dict[str, Any]:
        """Appel d'API avec gestion d'erreurs spécialisée"""
        
        # Vérifier les rate limits
        self._check_rate_limit(url)
        
        # Préparer les headers
        request_headers = headers or {}
        
        try:
            if method.upper() == 'GET':
                response = self.network.get(url, headers=request_headers, params=params)
            elif method.upper() == 'POST':
                response = self.network.post(url, headers=request_headers, json=data, params=params)
            else:
                raise ValueError(f"Méthode HTTP non supportée: {method}")
            
            # Traiter la réponse
            return self._process_api_response(response, url)
            
        except requests.exceptions.HTTPError as e:
            if '429' in str(e):  # Rate limit
                self._handle_rate_limit(url, e)
            raise
    
    def _check_rate_limit(self, url: str):
        """Vérifie les rate limits avant l'appel"""
        service = self._extract_service_from_url(url)
        if service in self.rate_limits:
            limit_info = self.rate_limits[service]
            if datetime.now() < limit_info['reset_time']:
                wait_time = (limit_info['reset_time'] - datetime.now()).total_seconds()
                if wait_time > 0:
                    self.logger.warning(f"⏳ Rate limit actif, attente {wait_time:.1f}s")
                    time.sleep(wait_time)
    
    def _handle_rate_limit(self, url: str, error: Exception):
        """Gère les rate limits"""
        service = self._extract_service_from_url(url)
        
        # Enregistrer le rate limit
        self.rate_limits[service] = {
            'hit_time': datetime.now(),
            'reset_time': datetime.now() + timedelta(minutes=1),  # Par défaut 1 minute
            'error': str(error)
        }
        
        self.logger.warning(f"🚫 Rate limit atteint pour {service}")
    
    def _extract_service_from_url(self, url: str) -> str:
        """Extrait le nom du service depuis une URL"""
        if 'binance' in url:
            return 'binance'
        elif 'coingecko' in url:
            return 'coingecko'
        elif 'dexscreener' in url:
            return 'dexscreener'
        return 'unknown'
    
    def _process_api_response(self, response: requests.Response, url: str) -> Dict[str, Any]:
        """Traite la réponse d'API"""
        try:
            data = response.json()
            
            # Vérifier les erreurs dans la réponse JSON
            if isinstance(data, dict):
                if 'error' in data:
                    raise ValueError(f"API Error: {data['error']}")
                elif 'msg' in data and 'code' in data:
                    raise ValueError(f"API Error {data['code']}: {data['msg']}")
            
            return data
            
        except ValueError as e:
            # Erreur de parsing JSON ou erreur API
            context = {'url': url, 'status_code': response.status_code, 'response_text': response.text[:200]}
            error_handler.handle_error(e, ErrorCategory.API, ErrorSeverity.MEDIUM, context)
            raise
    
    def get_api_status(self) -> Dict[str, Any]:
        """Récupère le statut des APIs"""
        return {
            'rate_limits': self.rate_limits,
            'network_status': self.network.get_network_status(),
            'last_errors': error_handler.get_error_stats()
        }

# Instances globales
network_handler = NetworkHandler()
api_handler = APIHandler()
