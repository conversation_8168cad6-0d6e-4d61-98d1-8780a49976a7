#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

def find_syntax_errors():
    """Trouve les erreurs de syntaxe dans le bot optimisé"""
    
    with open('bots/optimized_safe_bot.py', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    print("🔍 Recherche d'erreurs de syntaxe...")
    
    # Chercher les parenthèses non équilibrées
    for i, line in enumerate(lines, 1):
        # Compter les parenthèses
        open_parens = line.count('(')
        close_parens = line.count(')')
        
        if open_parens != close_parens:
            print(f"⚠️ Ligne {i}: Parenthèses non équilibrées")
            print(f"   {line.strip()}")
            print(f"   Ouvertes: {open_parens}, Fermées: {close_parens}")
        
        # Chercher les doubles parenthèses fermantes
        if '))" in line:
            print(f"❌ Ligne {i}: Double parenthèse fermante détectée")
            print(f"   {line.strip()}")
    
    # Chercher spécifiquement autour de la ligne 247
    print(f"\n📍 Contexte autour de la ligne 247:")
    for i in range(240, min(255, len(lines))):
        line_num = i + 1
        line = lines[i]
        marker = ">>> " if line_num == 247 else "    "
        print(f"{marker}{line_num:3}: {line.rstrip()}")

if __name__ == "__main__":
    find_syntax_errors()
