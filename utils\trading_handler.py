"""
💹 Gestionnaire d'erreurs de trading spécialisé
Gestion robuste des erreurs de trading avec protection du capital
"""

import logging
import time
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
from decimal import Decimal, ROUND_DOWN
from pathlib import Path
import sys

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from utils.error_handler import error_handler, ErrorCategory, ErrorSeverity, with_retry
from utils.notifications import notifier

class TradingErrorHandler:
    """Gestionnaire spécialisé pour les erreurs de trading"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Protection du capital
        self.max_daily_loss = 0.05      # 5% de perte max par jour
        self.max_position_size = 0.1    # 10% du capital max par position
        self.emergency_stop = False     # Arrêt d'urgence
        
        # Suivi des erreurs de trading
        self.failed_orders = []
        self.rejected_trades = []
        self.balance_errors = []
        
        # Limites de sécurité
        self.max_orders_per_minute = 10
        self.min_order_value = 10       # Valeur minimum d'ordre
        self.max_slippage = 0.05        # 5% de slippage max
        
        # Compteurs
        self.orders_this_minute = []
        self.daily_pnl = 0
        self.last_reset = datetime.now().date()
        
        # Enregistrer les stratégies de fallback
        self._register_fallback_strategies()
        
        self.logger.info("💹 Gestionnaire d'erreurs de trading initialisé")
    
    def _register_fallback_strategies(self):
        """Enregistre les stratégies de fallback pour le trading"""
        error_handler.register_fallback_strategy(
            ErrorCategory.TRADING,
            self._fallback_order_placement,
            priority=1
        )
        
        error_handler.register_fallback_strategy(
            ErrorCategory.TRADING,
            self._emergency_position_close,
            priority=2
        )
    
    def _fallback_order_placement(self, order_data: Dict) -> Dict[str, Any]:
        """Stratégie de fallback pour le placement d'ordres"""
        self.logger.info("🔄 Tentative de fallback pour placement d'ordre")
        
        # Réduire la taille de l'ordre
        if 'quantity' in order_data:
            original_qty = order_data['quantity']
            order_data['quantity'] = original_qty * 0.5  # Réduire de moitié
            self.logger.info(f"📉 Quantité réduite: {original_qty} -> {order_data['quantity']}")
        
        # Ajuster le prix si nécessaire
        if order_data.get('type') == 'LIMIT' and 'price' in order_data:
            side = order_data.get('side', '').upper()
            if side == 'BUY':
                # Augmenter légèrement le prix d'achat
                order_data['price'] *= 1.001
            elif side == 'SELL':
                # Diminuer légèrement le prix de vente
                order_data['price'] *= 0.999
        
        return order_data
    
    def _emergency_position_close(self, symbol: str, position_size: float) -> Dict[str, Any]:
        """Fermeture d'urgence d'une position"""
        self.logger.critical(f"🚨 Fermeture d'urgence de position: {symbol}")
        
        # Ordre de marché pour fermeture immédiate
        emergency_order = {
            'symbol': symbol,
            'side': 'SELL' if position_size > 0 else 'BUY',
            'type': 'MARKET',
            'quantity': abs(position_size),
            'emergency': True
        }
        
        # Notification d'urgence
        notifier.send_telegram(
            f"🚨 <b>FERMETURE D'URGENCE</b>\n"
            f"📊 Symbole: {symbol}\n"
            f"📏 Taille: {position_size}\n"
            f"⚠️ Ordre de marché placé"
        )
        
        return emergency_order
    
    def validate_order(self, order_data: Dict) -> Dict[str, Any]:
        """
        Valide un ordre avant exécution
        
        Args:
            order_data: Données de l'ordre
            
        Returns:
            Dict avec le résultat de validation
        """
        validation_result = {
            'valid': True,
            'errors': [],
            'warnings': [],
            'adjusted_order': order_data.copy()
        }
        
        try:
            # Vérifier l'arrêt d'urgence
            if self.emergency_stop:
                validation_result['valid'] = False
                validation_result['errors'].append("Arrêt d'urgence activé")
                return validation_result
            
            # Réinitialiser les compteurs quotidiens si nécessaire
            self._reset_daily_counters()
            
            # Vérifier les limites de fréquence
            if not self._check_rate_limits():
                validation_result['valid'] = False
                validation_result['errors'].append("Limite de fréquence dépassée")
                return validation_result
            
            # Valider les champs obligatoires
            required_fields = ['symbol', 'side', 'quantity']
            for field in required_fields:
                if field not in order_data:
                    validation_result['errors'].append(f"Champ obligatoire manquant: {field}")
            
            # Valider la quantité
            quantity = order_data.get('quantity', 0)
            if quantity <= 0:
                validation_result['errors'].append("Quantité invalide")
            
            # Valider la valeur minimum
            price = order_data.get('price', 0)
            if price > 0:
                order_value = quantity * price
                if order_value < self.min_order_value:
                    validation_result['warnings'].append(
                        f"Valeur d'ordre faible: {order_value} < {self.min_order_value}"
                    )
            
            # Vérifier la taille de position
            if not self._check_position_size(order_data):
                validation_result['valid'] = False
                validation_result['errors'].append("Taille de position trop importante")
            
            # Vérifier les pertes quotidiennes
            if not self._check_daily_loss():
                validation_result['valid'] = False
                validation_result['errors'].append("Limite de perte quotidienne atteinte")
            
            # Ajuster l'ordre si nécessaire
            adjusted_order = self._adjust_order(order_data)
            if adjusted_order != order_data:
                validation_result['adjusted_order'] = adjusted_order
                validation_result['warnings'].append("Ordre ajusté automatiquement")
            
            validation_result['valid'] = len(validation_result['errors']) == 0
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.HIGH, {
                'function': 'validate_order',
                'order_data': order_data
            })
            validation_result['valid'] = False
            validation_result['errors'].append(f"Erreur de validation: {str(e)}")
        
        return validation_result
    
    def _reset_daily_counters(self):
        """Remet à zéro les compteurs quotidiens"""
        today = datetime.now().date()
        if today != self.last_reset:
            self.daily_pnl = 0
            self.last_reset = today
            self.logger.info("🔄 Compteurs quotidiens réinitialisés")
    
    def _check_rate_limits(self) -> bool:
        """Vérifie les limites de fréquence"""
        now = datetime.now()
        
        # Nettoyer les ordres de plus d'une minute
        self.orders_this_minute = [
            order_time for order_time in self.orders_this_minute
            if now - order_time < timedelta(minutes=1)
        ]
        
        # Vérifier la limite
        if len(self.orders_this_minute) >= self.max_orders_per_minute:
            self.logger.warning(f"⚠️ Limite de fréquence atteinte: {len(self.orders_this_minute)}/min")
            return False
        
        return True
    
    def _check_position_size(self, order_data: Dict) -> bool:
        """Vérifie la taille de position"""
        # Cette fonction devrait être connectée au gestionnaire de portefeuille
        # Pour l'instant, on fait une vérification basique
        quantity = order_data.get('quantity', 0)
        price = order_data.get('price', 0)
        
        if price > 0:
            order_value = quantity * price
            # Supposer un capital de 10000 pour l'exemple
            max_value = 10000 * self.max_position_size
            
            if order_value > max_value:
                self.logger.warning(f"⚠️ Position trop importante: {order_value} > {max_value}")
                return False
        
        return True
    
    def _check_daily_loss(self) -> bool:
        """Vérifie les pertes quotidiennes"""
        if self.daily_pnl < 0:
            loss_percentage = abs(self.daily_pnl) / 10000  # Capital supposé
            if loss_percentage > self.max_daily_loss:
                self.logger.critical(f"🚨 Limite de perte quotidienne atteinte: {loss_percentage:.2%}")
                return False
        
        return True
    
    def _adjust_order(self, order_data: Dict) -> Dict:
        """Ajuste automatiquement un ordre"""
        adjusted = order_data.copy()
        
        # Arrondir la quantité selon les règles de l'exchange
        if 'quantity' in adjusted:
            # Exemple d'arrondi pour Binance (8 décimales max)
            quantity = Decimal(str(adjusted['quantity']))
            adjusted['quantity'] = float(quantity.quantize(Decimal('0.00000001'), rounding=ROUND_DOWN))
        
        # Arrondir le prix
        if 'price' in adjusted:
            price = Decimal(str(adjusted['price']))
            adjusted['price'] = float(price.quantize(Decimal('0.01'), rounding=ROUND_DOWN))
        
        return adjusted
    
    @with_retry(
        category=ErrorCategory.TRADING,
        severity=ErrorSeverity.HIGH
    )
    def execute_order(self, order_data: Dict, exchange_client) -> Dict[str, Any]:
        """
        Exécute un ordre avec gestion d'erreurs
        
        Args:
            order_data: Données de l'ordre
            exchange_client: Client de l'exchange
            
        Returns:
            Résultat de l'exécution
        """
        # Valider l'ordre
        validation = self.validate_order(order_data)
        if not validation['valid']:
            raise ValueError(f"Ordre invalide: {validation['errors']}")
        
        # Utiliser l'ordre ajusté
        final_order = validation['adjusted_order']
        
        try:
            # Enregistrer la tentative
            self.orders_this_minute.append(datetime.now())
            
            # Exécuter l'ordre (simulation)
            self.logger.info(f"📤 Exécution ordre: {final_order}")
            
            # Ici, on appellerait exchange_client.create_order(final_order)
            # Pour l'exemple, on simule une réponse
            result = {
                'orderId': f"ORDER_{int(time.time())}",
                'symbol': final_order['symbol'],
                'status': 'FILLED',
                'executedQty': final_order['quantity'],
                'fills': []
            }
            
            # Enregistrer le succès
            self.logger.info(f"✅ Ordre exécuté: {result['orderId']}")
            
            return result
            
        except Exception as e:
            # Enregistrer l'échec
            self.failed_orders.append({
                'timestamp': datetime.now(),
                'order_data': final_order,
                'error': str(e)
            })
            
            context = {
                'order_data': final_order,
                'failed_orders_count': len(self.failed_orders)
            }
            
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.HIGH, context)
            raise
    
    def handle_order_rejection(self, order_data: Dict, rejection_reason: str):
        """Gère le rejet d'un ordre"""
        rejection_info = {
            'timestamp': datetime.now(),
            'order_data': order_data,
            'reason': rejection_reason
        }
        
        self.rejected_trades.append(rejection_info)
        
        self.logger.warning(f"❌ Ordre rejeté: {rejection_reason}")
        
        # Analyser la raison du rejet
        if 'insufficient' in rejection_reason.lower():
            self.logger.critical("💰 Fonds insuffisants détectés")
            # Déclencher une vérification du solde
            
        elif 'price' in rejection_reason.lower():
            self.logger.warning("💲 Problème de prix détecté")
            # Ajuster la stratégie de prix
            
        # Notification si trop de rejets
        if len(self.rejected_trades) > 5:
            notifier.send_telegram(
                f"⚠️ <b>Nombreux ordres rejetés</b>\n"
                f"🔢 Rejets: {len(self.rejected_trades)}\n"
                f"📝 Dernier: {rejection_reason}"
            )
    
    def activate_emergency_stop(self, reason: str = "Arrêt d'urgence manuel"):
        """Active l'arrêt d'urgence"""
        self.emergency_stop = True
        
        self.logger.critical(f"🚨 ARRÊT D'URGENCE ACTIVÉ: {reason}")
        
        # Notification critique
        notifier.send_telegram(
            f"🚨 <b>ARRÊT D'URGENCE</b>\n"
            f"📝 Raison: {reason}\n"
            f"⏰ Heure: {datetime.now().strftime('%H:%M:%S')}\n"
            f"🛑 Tous les nouveaux ordres sont bloqués"
        )
    
    def deactivate_emergency_stop(self):
        """Désactive l'arrêt d'urgence"""
        self.emergency_stop = False
        self.logger.info("✅ Arrêt d'urgence désactivé")
        
        notifier.send_telegram("✅ <b>Arrêt d'urgence désactivé</b>\nTrading autorisé")
    
    def update_daily_pnl(self, pnl: float):
        """Met à jour le P&L quotidien"""
        self.daily_pnl += pnl
        
        # Vérifier les limites
        if self.daily_pnl < 0:
            loss_percentage = abs(self.daily_pnl) / 10000  # Capital supposé
            if loss_percentage > self.max_daily_loss * 0.8:  # 80% de la limite
                self.logger.warning(f"⚠️ Approche de la limite de perte: {loss_percentage:.2%}")
    
    def get_trading_stats(self) -> Dict[str, Any]:
        """Récupère les statistiques de trading"""
        return {
            'emergency_stop': self.emergency_stop,
            'daily_pnl': self.daily_pnl,
            'failed_orders': len(self.failed_orders),
            'rejected_trades': len(self.rejected_trades),
            'orders_this_minute': len(self.orders_this_minute),
            'limits': {
                'max_daily_loss': self.max_daily_loss,
                'max_position_size': self.max_position_size,
                'max_orders_per_minute': self.max_orders_per_minute,
                'min_order_value': self.min_order_value
            },
            'last_reset': self.last_reset.isoformat()
        }
    
    def reset_error_counters(self):
        """Remet à zéro les compteurs d'erreurs"""
        self.failed_orders.clear()
        self.rejected_trades.clear()
        self.balance_errors.clear()
        self.orders_this_minute.clear()
        
        self.logger.info("🔄 Compteurs d'erreurs réinitialisés")

# Instance globale
trading_handler = TradingErrorHandler()
