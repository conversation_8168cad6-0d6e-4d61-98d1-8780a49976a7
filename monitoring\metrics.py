"""
📊 Module de métriques pour botCrypto
Collecte et envoi des métriques de performance des bots
"""

import time
import threading
from typing import Dict, Any, Optional
from pathlib import Path
import sys

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from monitoring.dashboard import monitor

class MetricsCollector:
    """Collecteur de métriques pour les bots"""
    
    def __init__(self, bot_name: str, environment: str = "test"):
        self.bot_name = bot_name
        self.environment = environment
        self.start_time = time.time()
        self.metrics_cache = {}
        self.trade_count = 0
        self.total_profit = 0.0
        self.last_price = 0.0
        
        # Thread pour l'envoi périodique des métriques
        self.metrics_thread = None
        self.running = False
    
    def start_monitoring(self, interval: int = 60):
        """Démarre la collecte automatique de métriques"""
        self.running = True
        self.metrics_thread = threading.Thread(
            target=self._periodic_metrics_sender,
            args=(interval,),
            daemon=True
        )
        self.metrics_thread.start()
        
        # Métrique de démarrage
        self.log_metric("bot_status", 1, {"status": "started"})
    
    def stop_monitoring(self):
        """Arrête la collecte de métriques"""
        self.running = False
        if self.metrics_thread:
            self.metrics_thread.join(timeout=5)
        
        # Métrique d'arrêt
        self.log_metric("bot_status", 0, {"status": "stopped"})
    
    def _periodic_metrics_sender(self, interval: int):
        """Envoie périodiquement les métriques système"""
        while self.running:
            try:
                # Métriques de base
                uptime = time.time() - self.start_time
                self.log_metric("uptime_seconds", uptime)
                self.log_metric("trade_count_total", self.trade_count)
                self.log_metric("profit_total_usdt", self.total_profit)
                
                if self.last_price > 0:
                    self.log_metric("last_price", self.last_price)
                
                time.sleep(interval)
            except Exception as e:
                print(f"❌ Erreur envoi métriques: {e}")
                time.sleep(interval)
    
    def log_metric(self, metric_type: str, value: float, metadata: Dict = None):
        """Enregistre une métrique"""
        try:
            monitor.log_metric(
                self.bot_name,
                self.environment,
                metric_type,
                value,
                metadata
            )
            self.metrics_cache[metric_type] = value
        except Exception as e:
            print(f"❌ Erreur log métrique {metric_type}: {e}")
    
    def log_trade(self, symbol: str, side: str, quantity: float, price: float,
                  status: str = "executed", tx_hash: str = None, profit_loss: float = 0):
        """Enregistre un trade"""
        try:
            monitor.log_trade(
                self.bot_name,
                self.environment,
                symbol,
                side,
                quantity,
                price,
                status,
                tx_hash,
                profit_loss
            )
            
            # Mise à jour des compteurs
            if status == "executed":
                self.trade_count += 1
                self.total_profit += profit_loss
                self.last_price = price
                
                # Métriques dérivées
                self.log_metric("trade_executed", 1, {
                    "symbol": symbol,
                    "side": side,
                    "price": price
                })
                
        except Exception as e:
            print(f"❌ Erreur log trade: {e}")
    
    def log_alert(self, alert_type: str, message: str, severity: str = "INFO"):
        """Enregistre une alerte"""
        try:
            monitor.log_alert(self.bot_name, alert_type, message, severity)
        except Exception as e:
            print(f"❌ Erreur log alerte: {e}")
    
    def log_performance_metrics(self, balance: float, pnl: float, 
                              open_orders: int, drawdown: float = 0):
        """Enregistre les métriques de performance"""
        self.log_metric("balance_usdt", balance)
        self.log_metric("pnl_usdt", pnl)
        self.log_metric("open_orders", open_orders)
        
        if drawdown > 0:
            self.log_metric("max_drawdown", drawdown)
        
        # Calculer le ROI
        if balance > 0 and pnl != 0:
            roi = (pnl / (balance - pnl)) * 100
            self.log_metric("roi_percent", roi)
    
    def log_security_event(self, event_type: str, token_address: str = None, 
                          risk_level: str = None, details: Dict = None):
        """Enregistre un événement de sécurité"""
        metadata = details or {}
        if token_address:
            metadata["token_address"] = token_address
        if risk_level:
            metadata["risk_level"] = risk_level
        
        self.log_metric("security_event", 1, metadata)
        
        # Alerte si risque élevé
        if risk_level == "HIGH":
            self.log_alert("security", f"Token à risque élevé détecté: {token_address}", "WARNING")
    
    def log_error(self, error_type: str, error_message: str, details: Dict = None):
        """Enregistre une erreur"""
        self.log_metric("error_count", 1, {
            "error_type": error_type,
            "message": error_message,
            **(details or {})
        })
        
        self.log_alert("error", f"{error_type}: {error_message}", "ERROR")
    
    def get_current_metrics(self) -> Dict[str, Any]:
        """Retourne les métriques actuelles"""
        return {
            "bot_name": self.bot_name,
            "environment": self.environment,
            "uptime": time.time() - self.start_time,
            "trade_count": self.trade_count,
            "total_profit": self.total_profit,
            "last_price": self.last_price,
            "cached_metrics": self.metrics_cache.copy()
        }

class GridBotMetrics(MetricsCollector):
    """Métriques spécialisées pour le Grid Bot"""
    
    def __init__(self, bot_name: str, environment: str = "test"):
        super().__init__(f"GridBot_{bot_name}", environment)
        self.grid_levels = 0
        self.filled_orders = 0
        self.grid_profit = 0.0
    
    def log_grid_setup(self, grid_size: int, grid_spacing: float, initial_price: float):
        """Enregistre la configuration de la grille"""
        self.grid_levels = grid_size * 2  # Buy + Sell levels
        
        self.log_metric("grid_size", grid_size)
        self.log_metric("grid_spacing", grid_spacing)
        self.log_metric("initial_price", initial_price)
        self.log_metric("grid_levels_total", self.grid_levels)
    
    def log_order_filled(self, side: str, price: float, profit: float = 0):
        """Enregistre un ordre de grille exécuté"""
        self.filled_orders += 1
        self.grid_profit += profit
        
        self.log_metric("grid_orders_filled", self.filled_orders)
        self.log_metric("grid_profit_usdt", self.grid_profit)
        
        # Calculer l'efficacité de la grille
        if self.grid_levels > 0:
            efficiency = (self.filled_orders / self.grid_levels) * 100
            self.log_metric("grid_efficiency_percent", efficiency)

class SniperBotMetrics(MetricsCollector):
    """Métriques spécialisées pour le Sniper Bot"""
    
    def __init__(self, bot_name: str = "SniperBot"):
        super().__init__(bot_name, "mainnet")
        self.tokens_scanned = 0
        self.tokens_rejected = 0
        self.snipes_attempted = 0
        self.snipes_successful = 0
    
    def log_token_scan(self, tokens_found: int):
        """Enregistre un scan de tokens"""
        self.tokens_scanned += tokens_found
        self.log_metric("tokens_scanned_total", self.tokens_scanned)
    
    def log_token_rejection(self, token_address: str, reason: str, risk_level: str):
        """Enregistre le rejet d'un token"""
        self.tokens_rejected += 1
        
        self.log_metric("tokens_rejected_total", self.tokens_rejected)
        self.log_security_event("token_rejected", token_address, risk_level, {
            "reason": reason
        })
    
    def log_snipe_attempt(self, token_address: str, amount_bnb: float):
        """Enregistre une tentative de snipe"""
        self.snipes_attempted += 1
        
        self.log_metric("snipes_attempted_total", self.snipes_attempted)
        self.log_metric("snipe_attempt", 1, {
            "token_address": token_address,
            "amount_bnb": amount_bnb
        })
    
    def log_snipe_result(self, token_address: str, success: bool, tx_hash: str = None):
        """Enregistre le résultat d'un snipe"""
        if success:
            self.snipes_successful += 1
            self.log_metric("snipes_successful_total", self.snipes_successful)
        
        # Calculer le taux de réussite
        if self.snipes_attempted > 0:
            success_rate = (self.snipes_successful / self.snipes_attempted) * 100
            self.log_metric("snipe_success_rate_percent", success_rate)
        
        self.log_metric("snipe_result", 1 if success else 0, {
            "token_address": token_address,
            "tx_hash": tx_hash,
            "success": success
        })

# Instances globales pour faciliter l'utilisation
_metrics_instances = {}

def get_metrics_collector(bot_name: str, environment: str = "test", 
                         bot_type: str = "generic") -> MetricsCollector:
    """Récupère ou crée un collecteur de métriques"""
    key = f"{bot_name}_{environment}"
    
    if key not in _metrics_instances:
        if bot_type == "grid":
            _metrics_instances[key] = GridBotMetrics(bot_name, environment)
        elif bot_type == "sniper":
            _metrics_instances[key] = SniperBotMetrics(bot_name)
        else:
            _metrics_instances[key] = MetricsCollector(bot_name, environment)
    
    return _metrics_instances[key]
