#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📊 Performance Analyzer - Analyseur de Performance Avancé
Analyse les logs et génère des rapports détaillés de performance
"""

import os
import json
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
import re
import statistics
import numpy as np
from typing import Dict, List, Tuple, Optional

class PerformanceAnalyzer:
    """Analyseur de performance pour les logs de trading"""
    
    def __init__(self, log_directory: str = "."):
        self.log_directory = Path(log_directory)
        self.trade_data = []
        self.performance_data = []
        self.metrics_data = []
        
    def parse_trading_logs(self, log_file: str) -> List[Dict]:
        """Parse les logs de trading"""
        trades = []
        
        try:
            with open(self.log_directory / log_file, 'r') as f:
                for line in f:
                    if "TRADE_PLACED" in line:
                        # Format: TRADE_PLACED,BUY,0.002,45000.50,1.25,0.123
                        parts = line.strip().split(' - ')
                        if len(parts) >= 3:
                            timestamp_str = parts[0]
                            message = parts[2]
                            
                            if "TRADE_PLACED," in message:
                                data_part = message.split("TRADE_PLACED,")[1]
                                trade_parts = data_part.split(',')
                                
                                if len(trade_parts) >= 6:
                                    trade = {
                                        'timestamp': datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S,%f'),
                                        'side': trade_parts[0],
                                        'quantity': float(trade_parts[1]),
                                        'price': float(trade_parts[2]),
                                        'potential_profit': float(trade_parts[3]),
                                        'execution_time': float(trade_parts[4])
                                    }
                                    trades.append(trade)
            
            self.trade_data = trades
            return trades
            
        except Exception as e:
            print(f"❌ Erreur parsing logs: {e}")
            return []
    
    def parse_performance_logs(self, log_file: str) -> List[Dict]:
        """Parse les logs de performance"""
        performance = []
        
        try:
            with open(self.log_directory / log_file, 'r') as f:
                for line in f:
                    if "METRICS," in line:
                        # Format: METRICS,{"total_trades": 10, ...}
                        parts = line.strip().split(' - ')
                        if len(parts) >= 3:
                            timestamp_str = parts[0]
                            message = parts[2]
                            
                            if "METRICS," in message:
                                json_part = message.split("METRICS,")[1]
                                try:
                                    metrics = json.loads(json_part)
                                    metrics['timestamp'] = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S,%f')
                                    performance.append(metrics)
                                except json.JSONDecodeError:
                                    continue
            
            self.performance_data = performance
            return performance
            
        except Exception as e:
            print(f"❌ Erreur parsing performance: {e}")
            return []
    
    def calculate_advanced_metrics(self) -> Dict:
        """Calcule des métriques avancées"""
        if not self.trade_data:
            return {}
        
        # Conversion en DataFrame pour faciliter l'analyse
        df = pd.DataFrame(self.trade_data)
        
        # Métriques de base
        total_trades = len(df)
        profitable_trades = len(df[df['potential_profit'] > 0])
        losing_trades = len(df[df['potential_profit'] < 0])
        
        # Calculs financiers
        total_profit = df[df['potential_profit'] > 0]['potential_profit'].sum()
        total_loss = abs(df[df['potential_profit'] < 0]['potential_profit'].sum())
        net_profit = total_profit - total_loss
        
        # Ratios
        win_rate = (profitable_trades / total_trades * 100) if total_trades > 0 else 0
        profit_factor = (total_profit / total_loss) if total_loss > 0 else float('inf')
        
        # Analyse temporelle
        df['hour'] = df['timestamp'].dt.hour
        df['day_of_week'] = df['timestamp'].dt.dayofweek
        
        best_hour = df.groupby('hour')['potential_profit'].mean().idxmax()
        best_day = df.groupby('day_of_week')['potential_profit'].mean().idxmax()
        
        # Analyse des temps d'exécution
        avg_execution_time = df['execution_time'].mean()
        max_execution_time = df['execution_time'].max()
        
        # Analyse des volumes
        avg_trade_size = df['quantity'].mean()
        total_volume = df['quantity'].sum()
        
        # Drawdown analysis
        df_sorted = df.sort_values('timestamp')
        cumulative_profit = df_sorted['potential_profit'].cumsum()
        running_max = cumulative_profit.expanding().max()
        drawdown = cumulative_profit - running_max
        max_drawdown = drawdown.min()
        
        # Sharpe ratio simplifié
        if len(df) > 1:
            returns = df['potential_profit'].values
            sharpe_ratio = np.mean(returns) / np.std(returns) if np.std(returns) > 0 else 0
        else:
            sharpe_ratio = 0
        
        metrics = {
            'total_trades': total_trades,
            'profitable_trades': profitable_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate,
            'total_profit': total_profit,
            'total_loss': total_loss,
            'net_profit': net_profit,
            'profit_factor': profit_factor,
            'avg_profit_per_trade': net_profit / total_trades if total_trades > 0 else 0,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'avg_execution_time': avg_execution_time,
            'max_execution_time': max_execution_time,
            'avg_trade_size': avg_trade_size,
            'total_volume': total_volume,
            'best_trading_hour': best_hour,
            'best_trading_day': best_day,
            'trading_period_days': (df['timestamp'].max() - df['timestamp'].min()).days,
            'trades_per_day': total_trades / max(1, (df['timestamp'].max() - df['timestamp'].min()).days)
        }
        
        return metrics
    
    def generate_performance_report(self, output_file: str = None) -> str:
        """Génère un rapport de performance détaillé"""
        if not output_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"performance_report_{timestamp}.txt"
        
        metrics = self.calculate_advanced_metrics()
        
        report = []
        report.append("="*80)
        report.append("📊 RAPPORT DE PERFORMANCE DÉTAILLÉ")
        report.append("="*80)
        report.append(f"📅 Généré le: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"📁 Données analysées: {len(self.trade_data)} trades")
        
        if metrics:
            report.append(f"\n📈 MÉTRIQUES FINANCIÈRES:")
            report.append(f"   • Trades totaux: {metrics['total_trades']}")
            report.append(f"   • Trades rentables: {metrics['profitable_trades']}")
            report.append(f"   • Trades perdants: {metrics['losing_trades']}")
            report.append(f"   • Taux de réussite: {metrics['win_rate']:.2f}%")
            report.append(f"   • Profit total: {metrics['total_profit']:.4f}")
            report.append(f"   • Perte totale: {metrics['total_loss']:.4f}")
            report.append(f"   • Profit net: {metrics['net_profit']:.4f}")
            report.append(f"   • Facteur de profit: {metrics['profit_factor']:.2f}")
            report.append(f"   • Profit moyen par trade: {metrics['avg_profit_per_trade']:.4f}")
            
            report.append(f"\n📊 MÉTRIQUES DE RISQUE:")
            report.append(f"   • Drawdown maximum: {metrics['max_drawdown']:.4f}")
            report.append(f"   • Ratio de Sharpe: {metrics['sharpe_ratio']:.3f}")
            
            report.append(f"\n⚡ MÉTRIQUES D'EXÉCUTION:")
            report.append(f"   • Temps d'exécution moyen: {metrics['avg_execution_time']:.3f}s")
            report.append(f"   • Temps d'exécution max: {metrics['max_execution_time']:.3f}s")
            
            report.append(f"\n💰 MÉTRIQUES DE VOLUME:")
            report.append(f"   • Taille moyenne de trade: {metrics['avg_trade_size']:.6f} BTC")
            report.append(f"   • Volume total tradé: {metrics['total_volume']:.6f} BTC")
            
            report.append(f"\n⏰ ANALYSE TEMPORELLE:")
            report.append(f"   • Période de trading: {metrics['trading_period_days']} jours")
            report.append(f"   • Trades par jour: {metrics['trades_per_day']:.1f}")
            report.append(f"   • Meilleure heure: {metrics['best_trading_hour']}h")
            
            days = ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi', 'Dimanche']
            report.append(f"   • Meilleur jour: {days[metrics['best_trading_day']]}")
            
            # Évaluation de la stratégie
            report.append(f"\n🎯 ÉVALUATION DE LA STRATÉGIE:")
            
            if metrics['win_rate'] >= 60:
                report.append("   ✅ Taux de réussite excellent (≥60%)")
            elif metrics['win_rate'] >= 50:
                report.append("   ✅ Taux de réussite bon (≥50%)")
            else:
                report.append("   ⚠️ Taux de réussite faible (<50%)")
            
            if metrics['profit_factor'] >= 1.5:
                report.append("   ✅ Facteur de profit excellent (≥1.5)")
            elif metrics['profit_factor'] >= 1.2:
                report.append("   ✅ Facteur de profit bon (≥1.2)")
            elif metrics['profit_factor'] >= 1.0:
                report.append("   ⚠️ Facteur de profit marginal (≥1.0)")
            else:
                report.append("   ❌ Facteur de profit négatif (<1.0)")
            
            if metrics['sharpe_ratio'] >= 1.0:
                report.append("   ✅ Ratio de Sharpe excellent (≥1.0)")
            elif metrics['sharpe_ratio'] >= 0.5:
                report.append("   ✅ Ratio de Sharpe acceptable (≥0.5)")
            else:
                report.append("   ⚠️ Ratio de Sharpe faible (<0.5)")
            
            # Recommandations
            report.append(f"\n💡 RECOMMANDATIONS:")
            
            if metrics['net_profit'] > 0 and metrics['win_rate'] >= 50 and metrics['profit_factor'] >= 1.2:
                report.append("   🚀 Stratégie RECOMMANDÉE pour la production")
                report.append("   ✅ Tous les indicateurs sont positifs")
            elif metrics['net_profit'] > 0:
                report.append("   ⚠️ Stratégie MARGINALE - Optimisation recommandée")
                report.append("   🔧 Ajuster les paramètres pour améliorer les ratios")
            else:
                report.append("   ❌ Stratégie NON RECOMMANDÉE")
                report.append("   🔄 Révision complète de la stratégie nécessaire")
            
            if metrics['avg_execution_time'] > 1.0:
                report.append("   ⚡ Optimiser les temps d'exécution (>1s)")
            
            if metrics['max_drawdown'] < -5:
                report.append("   🛡️ Renforcer la gestion des risques (drawdown important)")
        
        report.append("\n" + "="*80)
        
        # Sauvegarde du rapport
        report_text = "\n".join(report)
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report_text)
            print(f"📄 Rapport sauvegardé: {output_file}")
        except Exception as e:
            print(f"❌ Erreur sauvegarde rapport: {e}")
        
        return report_text
    
    def create_performance_charts(self, output_dir: str = "charts"):
        """Crée des graphiques de performance"""
        if not self.trade_data:
            print("❌ Aucune donnée de trading à analyser")
            return
        
        # Créer le dossier de sortie
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        df = pd.DataFrame(self.trade_data)
        
        # Configuration du style
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
        
        # 1. Évolution du profit cumulé
        plt.figure(figsize=(12, 6))
        df_sorted = df.sort_values('timestamp')
        cumulative_profit = df_sorted['potential_profit'].cumsum()
        plt.plot(df_sorted['timestamp'], cumulative_profit, linewidth=2)
        plt.title('Évolution du Profit Cumulé', fontsize=16, fontweight='bold')
        plt.xlabel('Temps')
        plt.ylabel('Profit Cumulé')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(output_path / 'profit_evolution.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # 2. Distribution des profits
        plt.figure(figsize=(10, 6))
        plt.hist(df['potential_profit'], bins=30, alpha=0.7, edgecolor='black')
        plt.axvline(df['potential_profit'].mean(), color='red', linestyle='--', 
                   label=f'Moyenne: {df["potential_profit"].mean():.4f}')
        plt.title('Distribution des Profits par Trade', fontsize=16, fontweight='bold')
        plt.xlabel('Profit par Trade')
        plt.ylabel('Fréquence')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(output_path / 'profit_distribution.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # 3. Performance par heure
        plt.figure(figsize=(12, 6))
        hourly_profit = df.groupby(df['timestamp'].dt.hour)['potential_profit'].mean()
        hourly_profit.plot(kind='bar')
        plt.title('Profit Moyen par Heure de Trading', fontsize=16, fontweight='bold')
        plt.xlabel('Heure')
        plt.ylabel('Profit Moyen')
        plt.xticks(rotation=0)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(output_path / 'hourly_performance.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"📊 Graphiques sauvegardés dans: {output_path}")

def main():
    """Fonction principale pour analyser les performances"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Analyseur de Performance")
    parser.add_argument("--log-dir", default=".", help="Répertoire des logs")
    parser.add_argument("--trading-log", default="optimized_safe_bot_testnet.log", 
                       help="Fichier de log de trading")
    parser.add_argument("--performance-log", default="optimized_safe_bot_testnet_performance.log",
                       help="Fichier de log de performance")
    parser.add_argument("--output", default=None, help="Fichier de sortie du rapport")
    parser.add_argument("--charts", action="store_true", help="Générer des graphiques")
    
    args = parser.parse_args()
    
    print("📊 ANALYSEUR DE PERFORMANCE")
    print("="*40)
    
    analyzer = PerformanceAnalyzer(args.log_dir)
    
    # Parse des logs
    print(f"📖 Analyse des logs de trading: {args.trading_log}")
    trades = analyzer.parse_trading_logs(args.trading_log)
    print(f"✅ {len(trades)} trades analysés")
    
    print(f"📖 Analyse des logs de performance: {args.performance_log}")
    performance = analyzer.parse_performance_logs(args.performance_log)
    print(f"✅ {len(performance)} entrées de performance analysées")
    
    # Génération du rapport
    if trades:
        print("\n📄 Génération du rapport...")
        report = analyzer.generate_performance_report(args.output)
        print(report)
        
        # Génération des graphiques
        if args.charts:
            print("\n📊 Génération des graphiques...")
            analyzer.create_performance_charts()
    else:
        print("❌ Aucune donnée de trading trouvée")

if __name__ == "__main__":
    main()
